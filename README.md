# hvue-cli

## 简介

hvue-cli 脚手架工程：

- 基于vue.js 官方脚手架基于vue-cli2 搭建的二次开发脚手架库。
- 使用 npm + webpack + babel 的工作流，支持 ES2015。
- 提供配置好的vue-router, vuex, axios, babel-polyfill 模板及配置文件。
- 提供常用存取操作cookie,localStorage,sessionStorage方法, 及依赖`crypto-js`的加密方法。
- 提供基本ui组件及ajax请求调用的示例。

## 浏览器支持

> 现代浏览器和 IE9 及以上


## 工程安装启动【非现场】

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8081
npm run dev

# build for production with minification and test
npm run test

# build for production with minification and uat
npm run uat

# build for production with minification and pdt
npm run pdt

# build for production and view the bundle analyzer report
npm run build --report

```




'use strict'
const path = require('path')
const utils = require('./utils')
const config = require('../config')
const vueLoaderConfig = require('./vue-loader.conf')

function resolve (dir) {
  return path.join(__dirname, '..', dir)
}

const createLintingRule = () => ({
  test: /\.(js|vue)$/,
  loader: 'eslint-loader',
  enforce: 'pre',
  include: [resolve('src'), resolve('test')],
  options: {
    formatter: require('eslint-friendly-formatter'),
    emitWarning: !config.dev.showEslintErrorsInOverlay
  }
})

module.exports = {
  context: path.resolve(__dirname, '../'),
  entry: {
    app: './src/main.js'
  },
  output: {
    path: config.build.assetsRoot,
    filename: '[name].js',
    publicPath: process.env.NODE_ENV === 'production'
      ? config.build.assetsPublicPath
      : config.dev.assetsPublicPath
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    modules: [
      resolve('src'),
      resolve('node_modules')
    ],
    //别名配置
    alias: {
      'vue$': 'vue/dist/vue.runtime.esm.js',
      '@': resolve('src'),
      // 'lib': resolve('src/assets/lib'),
      // 'plugin': resolve('src/assets/plugin'),
      // 'thinkive-hvue': resolve('src/assets/lib/index.js'),
      // 'plugins': resolve('src/assets/plugins'),
      //thinkive-hvue start
      'config': resolve('config/config.js'),
      'store': resolve('src/store/index.js'),
      'router': resolve('src/router/index.js'),
      'netIntercept': resolve('src/service/netIntercept.js'),
      'nativeCallH5': resolve('src/nativeShell/nativeCallH5.js'),
      'h5CallNative': resolve('src/nativeShell/h5CallNative.js')
      //thinkive-hvue end

    }
  },
  module: {
    rules: [
      ...(config.dev.useEslint ? [createLintingRule()] : []),
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: vueLoaderConfig
      },
      {
        test: /\.js$/,
        loader: ['babel-loader'],
        include: [resolve('src'), resolve('test'), resolve('node_modules/webpack-dev-server/client'), resolve('node_modules/thinkive-hvue'), resolve('node_modules/_thinkive-hvue'), resolve('config/config.js')]
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        use: [{
            loader: 'url-loader',
            options: {
              limit: 500,
              name: utils.assetsPath('images/[name].[hash:7].[ext]')
            }
          },
          {
            //image(jpg,gif,png)压缩配置项，各参数已经测试校验过，保证压缩与质量比例最优.
            //如需更改，请先行熟悉各参数项的意义
            loader: 'image-webpack-loader',
            options: {
              mozjpeg: {
                progressive: true,
                quality: 92
              },
              gifsicle: {
                interlaced: true,
                optimizationLevel: 4
              },
              optipng: {
                enabled: false,
              },
              pngquant: {
                quality: '90-99',
                speed: 4
              }
            }
        }]
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 50,
          name: utils.assetsPath('media/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 50,
          name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
        }
      }
    ]
  },
  node: {
    // prevent webpack from injecting useless setImmediate polyfill because Vue
    // source contains it (although only uses it if it's native).
    setImmediate: false,
    // prevent webpack from injecting mocks to Node native modules
    // that does not make sense for the client
    dgram: 'empty',
    fs: 'empty',
    net: 'empty',
    tls: 'empty',
    child_process: 'empty'
  }
}

/*	H5调用原生壳子方法 集合写在这里  */
import {
  callMessageNative,
  getSession
} from 'thinkive-hvue'

export const callMessageNativeFunc = function (paramMap) {
  if ($hvue.platform === '0') {
    console.log('callMessageNative 出错：非原生环境')
    return { error_no: '-00001', error_info: '非原生环境无法调用原生SDK方法' }
  }
  return callMessageNative(paramMap)
}

/*
 * @param funcNo 70003 查询用户信息
 // 回调函数可以改成Promise的方式
 */
export const function70003 = function (paramMap) {
  let param = {
    funcNo: '70003'
  }
  Object.assign(param, paramMap)
  return new Promise((resolve, reject) => {
    callMessageNativeFunc(param, function () {
      if (res && res.error_no == 0) {
        resolve(res)
      } else {
        reject(res)
      }
    })
  })
}

/**
 * @param funcNo 50010 获取app 应用版本号
 */
export const function50010 = function (paramMap, callback) {
  let param = {}
  param['funcNo'] = '50010'
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 50041 获取账号信息
 * @param key 资金账号信息: tk_h5_account_info; 手机账号信息: tk_h5_phone_info
 */
export const function50041 = function (paramMap, callback) {
  let param = {
    funcNo: '50041'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 50040 清空sdk缓存
 */
export const function50040 = function (paramMap, callback) {
  let param = {}
  param['funcNo'] = '50040'
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 60350 登录
 */
export const function60350 = function (paramMap, callback) {
  let param = {
    funcNo: '60350',
    loginType: '1'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 60351 登出
 */
export const function60351 = function (paramMap, callback) {
  let param = {
    funcNo: '60351',
    logoutType: '1'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 60099 唤起原生登录
 */
export const function60099Login = function (paramMap, callback) {
  const urlHost = window.location.host
  const urlProtocol = window.location.protocol
  let param = {
    funcNo: '60099',
    actionType: '1',
    moduleName: 'ygt',
    params: {
      logintype: '1',
      loginkind: '1',
      url: `${urlProtocol}//${urlHost}/m/ygt/index.html?source=zzapp&loginFlag=2&mobilecode=($mobilecode)&user_token=($user_token)#!/zhyw/homepage.html`,
      actionURL: `http://action:10090/?logintype=1&&loginkind=1&&url=${urlProtocol}//${urlHost}/m/ygt/index.html?source=zzapp&loginFlag=2&mobilecode=($mobilecode)&user_token=($user_token)&AccountList=($AccountList)#!/zhyw/homepage.html,locationHref:${urlProtocol}//${urlHost}/m/ygt/index.html`
    }
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 50105 退出网厅
 */
export const function50105 = function (paramMap, callback) {
  let param = {
    moduleName: 'open',
    funcNo: '50105'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 50114 退出sdk
 */
export const function50114 = function (paramMap, callback) {
  let param = {
    moduleName: 'open',
    funcNo: '50114'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 60013 上传身份证图片(需要orc)
 */
export const function60013 = function (paramMap, callback) {
  let param = {
    funcNo: '60013', // 1：服务器是否需要ocr识别(is_orc_funcno等于60013)   0：不需要(is_orc_funcno等于60014)
    requestParam:
      'funcNo=1005378&version=1&isOcr=1&userId=' +
      getSession('ygtUserInfo', { decrypt: false }).userId,
    userId: getSession('ygtUserInfo', { decrypt: false }).userId,
    isUpload: 0, // 件照图片上传方式（默认1由原生上传）
    moduleName: 'ygt'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 60014 身份证件扫描识别
 */
export const function60014 = function (paramMap, callback) {
  let param = {
    funcNo: '60014', // 1：服务器是否需要ocr识别(is_orc_funcno等于60013)   0：不需要(is_orc_funcno等于60014)
    requestParam:
      `funcNo=501525&version=1&is_ocr=${
        paramMap.is_ocr
      }&userId='${
        getSession('ygtUserInfo', { decrypt: false }).userId
      }&file_name=${
        paramMap.file_name || getSession('ygtUserInfo', { decrypt: false }).userId
      }.jpg&file_type=image&op_source=${$hvue.platform}&flow_type=twvideo`,
    userId: getSession('ygtUserInfo', { decrypt: false }).userId,
    compressSize: 300,
    isAlbum: 1,
    isUpload: 0, // 件照图片上传方式（默认1由原生上传）
    moduleName: 'ygt'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

/**
 * @param funcNo 60002 上传身份证图片(需要orc)
 */
export const function60002 = function (paramMap, callback) {
  let param = {
    funcNo: '60002',
    r: Math.random(),
    funcNum: '501525',
    userId: getSession('ygtUserInfo', { decrypt: false }).userId || '',
    uuid: 'index',
    key: 'index',
    photoType: '身份证',
    clientInfo: '',
    jsessionId: '',
    isUpload: 0, // 件照图片上传方式（默认1由原生上传）
    moduleName: 'ygt'
  }
  Object.assign(param, paramMap)
  let result = callMessageNativeFunc(param, callback)
  return result
}

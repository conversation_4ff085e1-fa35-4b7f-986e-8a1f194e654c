/*
* jQuery Mobile Framework
* Copyright (c) jQuery Project
* Dual licensed under the MIT (MIT-LICENSE.txt) or GPL (GPL-LICENSE.txt) licenses.
*/
.in {
	-webkit-animation-timing-function: ease-out;
	-webkit-animation-duration: 350ms;
	animation-timing-function: ease-out;
	animation-duration: 350ms;
}
.out {
	-webkit-animation-timing-function: ease-in;
	-webkit-animation-duration: 225ms;
	animation-timing-function: ease-in;
	animation-duration: 225ms;
}
@-webkit-keyframes fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}
@-webkit-keyframes fadeout {
    from { opacity: 1; }
    to { opacity: 0; }
}
@keyframes fadeout {
    from { opacity: 1; }
    to { opacity: 0; }
}
.fade.out {
	opacity: 0;
	-webkit-animation-duration: 125ms;
	-webkit-animation-name: fadeout;
	animation-duration: 125ms;
	animation-name: fadeout;
}
.fade.in {
	opacity: 1;
	-webkit-animation-duration: 225ms;
	-webkit-animation-name: fadein;
	animation-duration: 225ms;
	animation-name: fadein;
}
.pop {
	-webkit-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
}
.pop.in {
	-webkit-transform: scale(1);
	transform: scale(1);
    opacity: 1;
	-webkit-animation-name: popin;
	animation-name: popin;
	-webkit-animation-duration: 350ms;
	animation-duration: 350ms;
}
.pop.out {
	-webkit-animation-name: fadeout;
	animation-name: fadeout;
	opacity: 0;
	-webkit-animation-duration: 100ms;
	animation-duration: 100ms;
}
.pop.in.reverse {
	-webkit-animation-name: fadein;
	animation-name: fadein;
}
.pop.out.reverse {
	-webkit-transform: scale(.8);
	transform: scale(.8);
	-webkit-animation-name: popout;
	animation-name: popout;
}
@-webkit-keyframes popin {
    from {
        -webkit-transform: scale(.8);
        opacity: 0;
    }
    to {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}
@keyframes popin {
    from {
        transform: scale(.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}
@-webkit-keyframes popout {
    from {
        -webkit-transform: scale(1);
        opacity: 1;
    }
    to {
        -webkit-transform: scale(.8);
        opacity: 0;
    }
}
@keyframes popout {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(.8);
        opacity: 0;
    }
}
@-webkit-keyframes slideinfromright {
    from { -webkit-transform: translateX(100%); }
    to { -webkit-transform: translateX(0); }
}
@keyframes slideinfromright {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}
@-webkit-keyframes slideinfromleft {
    from { -webkit-transform: translateX(-100%); }
    to { -webkit-transform: translateX(0); }
}
@keyframes slideinfromleft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
@-webkit-keyframes slideouttoleft {
    from { -webkit-transform: translateX(0); }
    to { -webkit-transform: translateX(-100%); }
}
@keyframes slideouttoleft {
    from { transform: translateX(0); }
    to { transform: translateX(-100%); }
}
@-webkit-keyframes slideouttoright {
    from { -webkit-transform: translateX(0); }
    to { -webkit-transform: translateX(100%); }
}
@keyframes slideouttoright {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}
.slide.out, .slide.in {
	-webkit-animation-timing-function: ease-out;
	-webkit-animation-duration: 350ms;
	animation-timing-function: ease-out;
	animation-duration: 350ms;
}
.slide.out {
	-webkit-transform: translateX(-100%);
	-webkit-animation-name: slideouttoleft;
	transform: translateX(-100%);
	animation-name: slideouttoleft;
}
.slide.in {
	-webkit-transform: translateX(0);
	-webkit-animation-name: slideinfromright;
	transform: translateX(0);
	animation-name: slideinfromright;
}
.slide.out.reverse {
	-webkit-transform: translateX(100%);
	-webkit-animation-name: slideouttoright;
	transform: translateX(100%);
	animation-name: slideouttoright;
}
.slide.in.reverse {
	-webkit-transform: translateX(0);
	-webkit-animation-name: slideinfromleft;
	transform: translateX(0);
	animation-name: slideinfromleft;
}
.slidefade.out {
	-webkit-transform: translateX(-100%);
	-webkit-animation-name: slideouttoleft;
	transform: translateX(-100%);
	animation-name: slideouttoleft;
	-webkit-animation-duration: 225ms;
	animation-duration: 225ms;
}
.slidefade.in {
	-webkit-transform: translateX(0);
	-webkit-animation-name: fadein;
	transform: translateX(0);
	animation-name: fadein;
	-webkit-animation-duration: 200ms;
	animation-duration: 200ms;
}
.slidefade.out.reverse {
	-webkit-transform: translateX(100%);
	-webkit-animation-name: slideouttoright;
	transform: translateX(100%);
	animation-name: slideouttoright;
	-webkit-animation-duration: 200ms;
	animation-duration: 200ms;
}
.slidefade.in.reverse {
	-webkit-transform: translateX(0);
	-webkit-animation-name: fadein;
	transform: translateX(0);
	animation-name: fadein;
	-webkit-animation-duration: 200ms;
	animation-duration: 200ms;
}
.slidedown.out {
	-webkit-animation-name: fadeout;
	animation-name: fadeout;
	-webkit-animation-duration: 100ms;
	animation-duration: 100ms;
}
.slidedown.in {
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfromtop;
	transform: translateY(0);
	animation-name: slideinfromtop;
	-webkit-animation-duration: 250ms;
	animation-duration: 250ms;
}
.slidedown.in.reverse {
	-webkit-animation-name: fadein;
	animation-name: fadein;
	-webkit-animation-duration: 150ms;
	animation-duration: 150ms;
}
.slidedown.out.reverse {
	-webkit-transform: translateY(-100%);
	transform: translateY(-100%);
	-webkit-animation-name: slideouttotop;
	animation-name: slideouttotop;
	-webkit-animation-duration: 200ms;
	animation-duration: 200ms;
}
@-webkit-keyframes slideinfromtop {
    from { -webkit-transform: translateY(-100%); }
    to { -webkit-transform: translateY(0); }
}
@keyframes slideinfromtop {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}
@-webkit-keyframes slideouttotop {
    from { -webkit-transform: translateY(0); }
    to { -webkit-transform: translateY(-100%); }
}
@keyframes slideouttotop {
    from { transform: translateY(0); }
    to { transform: translateY(-100%); }
}
.slideup.out {
	-webkit-animation-name: fadeout;
	animation-name: fadeout;
	-webkit-animation-duration: 100ms;
	animation-duration: 100ms;
}
.slideup.in {
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfrombottom;
	transform: translateY(0);
	animation-name: slideinfrombottom;
	-webkit-animation-duration: 250ms;
	animation-duration: 250ms;
}
.slideup.in.reverse {
	-webkit-animation-name: fadein;
	animation-name: fadein;
	-webkit-animation-duration: 150ms;
	animation-duration: 150ms;
}
.slideup.out.reverse {
	-webkit-transform: translateY(100%);
	transform: translateY(100%);
	-webkit-animation-name: slideouttobottom;
	animation-name: slideouttobottom;
	-webkit-animation-duration: 200ms;
	animation-duration: 200ms;
}
@-webkit-keyframes slideinfrombottom {
    from { -webkit-transform: translateY(100%); }
    to { -webkit-transform: translateY(0); }
}
@keyframes slideinfrombottom {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}
@-webkit-keyframes slideouttobottom {
    from { -webkit-transform: translateY(0); }
    to { -webkit-transform: translateY(100%); }
}
@keyframes slideouttobottom {
    from { transform: translateY(0); }
    to { transform: translateY(100%); }
}
.viewport-flip {
	-webkit-perspective: 1000px;
	perspective: 1000px;
	position: absolute;
}
.flip {
	-webkit-backface-visibility:hidden;
	-webkit-transform:translateX(0); 
	backface-visibility:hidden;
	transform:translateX(0);
}
.flip.out {
	-webkit-transform: rotateY(-90deg) scale(.9);
	-webkit-animation-name: flipouttoleft;
	-webkit-animation-duration: 175ms;
	transform: rotateY(-90deg) scale(.9);
	animation-name: flipouttoleft;
	animation-duration: 175ms;
}
.flip.in {
	-webkit-animation-name: flipintoright;
	-webkit-animation-duration: 225ms;
	animation-name: flipintoright;
	animation-duration: 225ms;
}
.flip.out.reverse {
	-webkit-transform: rotateY(90deg) scale(.9);
	-webkit-animation-name: flipouttoright;
	transform: rotateY(90deg) scale(.9);
	animation-name: flipouttoright;
}
.flip.in.reverse {
	-webkit-animation-name: flipintoleft;
	animation-name: flipintoleft;
}
@-webkit-keyframes flipouttoleft {
    from { -webkit-transform: rotateY(0); }
    to { -webkit-transform: rotateY(-90deg) scale(.9); }
}
@keyframes flipouttoleft {
    from { transform: rotateY(0); }
    to { transform: rotateY(-90deg) scale(.9); }
}
@-webkit-keyframes flipouttoright {
    from { -webkit-transform: rotateY(0) ; }
    to { -webkit-transform: rotateY(90deg) scale(.9); }
}
@keyframes flipouttoright {
    from { transform: rotateY(0); }
    to { transform: rotateY(90deg) scale(.9); }
}
@-webkit-keyframes flipintoleft {
    from { -webkit-transform: rotateY(-90deg) scale(.9); }
    to { -webkit-transform: rotateY(0); }
}
@keyframes flipintoleft {
    from { transform: rotateY(-90deg) scale(.9); }
    to { transform: rotateY(0); }
}
@-webkit-keyframes flipintoright {
    from { -webkit-transform: rotateY(90deg) scale(.9); }
    to { -webkit-transform: rotateY(0); }
}
@keyframes flipintoright {
    from { transform: rotateY(90deg) scale(.9); }
    to { transform: rotateY(0); }
}
.viewport-turn {
	-webkit-perspective: 1000px;
	perspective: 1000px;
	position: absolute;
}
.turn {
	-webkit-backface-visibility:hidden;
	-webkit-transform:translateX(0); 
	-webkit-transform-origin: 0;
	
	backface-visibility:hidden;
	transform:translateX(0); 
	transform-origin: 0;
}
.turn.out {
	-webkit-transform: rotateY(-90deg);
	-webkit-animation-name: turnouttoleft;
	transform: rotateY(-90deg);
	animation-name: turnouttoleft;
	-webkit-animation-duration: 125ms;
	animation-duration: 125ms;
}
.turn.in {
	-webkit-animation-name: turnintoright;
	animation-name: turnintoright;
	-webkit-animation-duration: 250ms;
	animation-duration: 250ms;
	
}
.turn.out.reverse {
	-webkit-transform: rotateY(90deg);
	-webkit-animation-name: turnouttoright;
	transform: rotateY(90deg);
	animation-name: turnouttoright;
}
.turn.in.reverse {
	-webkit-animation-name: turnintoleft;
	animation-name: turnintoleft;
}
@-webkit-keyframes turnouttoleft {
    from { -webkit-transform: rotateY(0); }
    to { -webkit-transform: rotateY(-90deg); }
}
@keyframes turnouttoleft {
    from { transform: rotateY(0); }
    to { transform: rotateY(-90deg); }
}
@-webkit-keyframes turnouttoright {
    from { -webkit-transform: rotateY(0) ; }
    to { -webkit-transform: rotateY(90deg); }
}
@keyframes turnouttoright {
    from { transform: rotateY(0); }
    to { transform: rotateY(90deg); }
}
@-webkit-keyframes turnintoleft {
    from { -webkit-transform: rotateY(-90deg); }
    to { -webkit-transform: rotateY(0); }
}
@keyframes turnintoleft {
    from { transform: rotateY(-90deg); }
    to { transform: rotateY(0); }
}
@-webkit-keyframes turnintoright {
    from { -webkit-transform: rotateY(90deg); }
    to { -webkit-transform: rotateY(0); }
}
@keyframes turnintoright {
    from { transform: rotateY(90deg); }
    to { transform: rotateY(0); }
}
.flow {
	-webkit-transform-origin: 50% 30%;
	transform-origin: 50% 30%;
}
.flow.out {
	-webkit-transform: translateX(-100%) scale(.7);
	-webkit-animation-name: flowouttoleft;
	-webkit-animation-timing-function: ease;
	-webkit-animation-duration: 350ms;
	transform: translateX(-100%) scale(.7);
	animation-name: flowouttoleft;
	animation-timing-function: ease;
	animation-duration: 350ms;
}
.flow.in {
	-webkit-transform: translateX(0) scale(1);
	-webkit-animation-name: flowinfromright;
	-webkit-animation-timing-function: ease;
	-webkit-animation-duration: 350ms;
	transform: translateX(0) scale(1);
	animation-name: flowinfromright;
	animation-timing-function: ease;
	animation-duration: 350ms;
}
.flow.out.reverse {
	-webkit-transform: translateX(100%);
	-webkit-animation-name: flowouttoright;
	transform: translateX(100%);
	animation-name: flowouttoright;
}
.flow.in.reverse {
	-webkit-animation-name: flowinfromleft;
	animation-name: flowinfromleft;
}
@-webkit-keyframes flowouttoleft {
    0% { -webkit-transform: translateX(0) scale(1); }
	60%, 70% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform: translateX(-100%) scale(.7); }
}
@keyframes flowouttoleft {
    0% { transform: translateX(0) scale(1); }
	60%, 70% { transform: translateX(0) scale(.7); }
    100% { transform:  translateX(-100%) scale(.7); }
}
@-webkit-keyframes flowouttoright {
    0% { -webkit-transform: translateX(0) scale(1); }
	60%, 70% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform:  translateX(100%) scale(.7); }
}
@keyframes flowouttoright {
    0% { transform: translateX(0) scale(1); }
	60%, 70% { transform: translateX(0) scale(.7); }
    100% { transform:  translateX(100%) scale(.7); }
}
@-webkit-keyframes flowinfromleft {
    0% { -webkit-transform: translateX(-100%) scale(.7); }
	30%, 40% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform: translateX(0) scale(1); }
}
@keyframes flowinfromleft {
    0% { transform: translateX(-100%) scale(.7); }
	30%, 40% { transform: translateX(0) scale(.7); }
    100% { transform: translateX(0) scale(1); }
}
@-webkit-keyframes flowinfromright {
    0% { -webkit-transform: translateX(100%) scale(.7); }
	30%, 40% { -webkit-transform: translateX(0) scale(.7); }
    100% { -webkit-transform: translateX(0) scale(1); }
}
@keyframes flowinfromright {
    0% { transform: translateX(100%) scale(.7); }
	30%, 40% { transform: translateX(0) scale(.7); }
    100% { transform: translateX(0) scale(1); }
}

@-webkit-keyframes allrotate {
  from {-webkit-transform:rotate(0deg);}
  to {-webkit-transform:rotate(-360deg);}
}
@keyframes allrotate {
  from {transform:rotate(0deg);}
  to {transform:rotate(-360deg);}
}







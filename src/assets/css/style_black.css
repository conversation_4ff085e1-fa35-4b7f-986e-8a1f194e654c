@charset "utf-8";

/* reset.css */

body {
  position: relative;
  overflow-y: visible !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: moz-none;
  user-select: none;
  -webkit-overflow-scrolling: auto;
}

body * {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/** html4 reset **/

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
}

fieldset,
img {
  border: 0 none;
}

address,
caption,
cite,
code,
dfn,
em,
th,
var,
b,
h1,
h2,
h3 {
  font-style: normal;
  font-weight: normal;
}

ol,
ul,
li {
  list-style-type: none;
}

q:before,
q:after {
  content: '';
}

abbr,
acronym {
  border: 0;
  font-variant: normal;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

th,
td,
caption {
  vertical-align: top;
  text-align: left;
}

input[type='text'],
input[type='email'],
input[type='search'],
input[type='password'],
input[type='date'],
input[type='month'],
input[type='tel'],
input[type='radio'],
input[type='checkbox'],
button,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -moz-tap-highlight-color: rgba(0, 0, 0, 0);
}

input[type='search'] {
  -webkit-appearance: textfield;
}

input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

img {
  vertical-align: middle;
  font-size: 0;
}

h1 {
  font-size: 0.24rem;
}

h2 {
  font-size: 0.2rem;
}

h3 {
  font-size: 0.18rem;
}

h4 {
  font-size: 0.16rem;
}

h5 {
  font-size: 0.14rem;
}

/** html5 reset **/

header,
footer,
section,
nav,
menu,
details,
hgroup,
figure,
figcaption,
article,
aside {
  margin: 0;
  padding: 0;
  display: block;
}

input::-moz-placeholder {
  color: rgba(156,166,177,0.4);
  font-weight: normal;
}

input::-webkit-input-placeholder {
  color: rgba(156,166,177,0.4);
  font-weight: normal;
}

textarea::-moz-placeholder {
  color: rgba(156,166,177,0.4);
  font-weight: normal;
}

textarea::-webkit-input-placeholder {
  color: rgba(156,166,177,0.4);
  font-weight: normal;
}

input,
textarea {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu;
}

a {
  text-decoration: none;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a:hover {
  opacity: 1;
}

.boost {
  -webkit-overflow-scrolling: touch;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  -webkit-backface-visibility: hidden;
  -webkit-perspective: 1000;
  -moz-backface-visibility: hidden;
  -moz-perspective: 1000;
  backface-visibility: hidden;
  perspective: 1000;
  zoom: 1;
}

/** Body, links, basics **/

/** Body, links, basics **/

body,
html {
  font-size: 100px;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

body {
  font-size: 0.14rem;
  line-height: 1.8;
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-weight: normal;
  color: #E4E4E4;
  background: #091018;
}

/*-- 布局grid --*/

.main.fixed {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.main.fixed article.content {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  -webkit-overflow-scrolling: auto;
}

section.main.fixed article.content::-webkit-scrollbar {
  width: 0;
  display: none;
}
.white_bg{
	background: #091018 !important;
}




/*-- 表单 form --*/

.input_form {
  background: #111F2D;
  padding: 0 0.1rem;
}

.input_text {
  border-top: 1px solid rgba(156,166,177,0.2);
  padding: 0 0.1rem;
  position: relative;
  display: flex;
}

.input_text:first-child {
  border-top: 0 none;
}

.input_text .tit {
  display: block;
  font-size: 0.16rem;
  line-height: 0.52rem;
  min-width: 0.92rem;
  color: #6D7987;
}

.input_text .t1 {
  display: block;
  flex: 1;
  width: 100%;
  height: 0.52rem;
  padding: 0.15rem 0;
  line-height: 0.22rem;
  font-size: 0.16rem;
  border: 0 none;
  outline: none;
  background: none;
  color: #E4E4E4;
  font-weight: 500;
}

.input_text .dropdown {
  flex: 1;
  width: 100%;
  min-width: 0;
  height: 0.52rem;
  line-height: 0.22rem;
  padding: 0.15rem 0.2rem 0.15rem 0;
  font-size: 0.16rem;
  color: #E4E4E4;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.input_text .dropdown:after {
  content: '';
  width: 0.08rem;
  height: 0.14rem;
  background: url(../images/arrow01_black.png) no-repeat center;
  background-size: 100%;
  position: absolute;
  top: 50%;
  margin-top: -0.07rem;
  right: 0;
}

.input_text .dropdown:empty:before {
  content: attr(placeholder);
  color: rgba(156,166,177,0.4);
  font-weight: normal;
}

.input_text .tarea1 {
  padding: 0.15rem 0;
  line-height: 0.22rem;
  min-height: 0.52rem;
  flex: 1;
  width: 100%;
  color: #E4E4E4;
  font-size: 0.16rem;
  font-weight: 500;
  outline: none;
  border: 0 none;
  background: none;
  -webkit-user-select: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  display: block;
}

.input_text .tarea1:empty:before {
  content: attr(placeholder);
  color: rgba(156,166,177,0.4);
  font-weight: normal;
}

.input_text.error .t1,
.input_text.error .dropdown,
.input_text.error .tarea1 {
  color: #FF4540;
}
.input_text .ct{
	flex: 1;
	width: 100%;
	text-align: right;
	padding: 0.15rem 0;
	font-size: 0.16rem;
	line-height: 0.22rem;
	font-weight: 500;
}




.code_img {
  display: block;
  width: 0.8rem;
  height: 0.28rem;
  position: relative;
  top: 0.12rem;
}

.code_img img {
  display: block;
  width: 100%;
  height: 100%;
}

.code_btn {
  display: block;
  margin-left: 0.05rem;
  height: 0.52rem;
  line-height: 0.52rem;
  font-size: 0.14rem;
  color: #177FFF;
}

.code_btn.time {
  color: rgba(156,166,177,0.2);
}

.text_clear {
  display: block;
  width: 0.28rem;
  height: 0.28rem;
  background: url(../images/text_clear_black.png) no-repeat center;
  background-size: 0.18rem;
  position: relative;
  top: 0.12rem;
}


/*-- 单选、复选框 radio checkbox --*/

.icon_check,
.icon_radio{
  display: inline-block;
  width: 0.2rem;
  height: 0.2rem;
  vertical-align: middle;
  position: relative;
}

.icon_check:before {
  content: '';
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 0.16rem;
  height: 0.16rem;
  border: 1px solid rgba(109,121,135,0.6);
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -0.08rem 0 0 -0.08rem;
}

.icon_check.checked:before {
  border-color: #177FFF;
  background: url(../images/icon_check01.png) no-repeat center;
  background-size: 100%;
}

.icon_radio:before {
  content: '';
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 0.16rem;
  height: 0.16rem;
  border: 1px solid rgba(109,121,135,0.6);
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -0.08rem 0 0 -0.08rem;
}

.icon_radio.checked:before {
  border-color: #177FFF;
  background: #177FFF url(../images/icon_check02.png) no-repeat center;
  background-size: 100%;
}

/*-- 开关 switch --*/

.switch {
  height: 20px;
  width: 36px;
  position: relative;
}

.switch > input[type='checkbox'] {
  width: 36px;
  height: 20px;
  position: absolute;
  left: 0;
  top: 0;
  -moz-opacity: 0;
  -webkit-opacity: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 100;
  outline: none;
}

.switch > .switch-inner {
  height: 20px;
  position: relative;
  background: #091018;
  border: 1px solid #6D7987;
  border-radius: 100px;
  transition: all 0.1s ease-in;
  -moz-transition: all 0.1s ease-in;
  -webkit-transition: all 0.1s ease-in;
}

.switch > .switch-inner > .switch-arrow {
  height: 18px;
  width: 18px;
  background: #E4E4E4;
  position: absolute;
  top: 0;
  right: 16px;
  border-radius: 100px;
  border: 1px solid #6D7987;
  transition: all 0.1s ease-in;
  -moz-transition: all 0.1s ease-in;
  -webkit-transition: all 0.1s ease-in;
  z-index: 10;
}

.switch > input[type='checkbox']:checked + .switch-inner {
  background: #177FFF;
  border-color: #177FFF;
}

.switch > input[type='checkbox']:checked + .switch-inner .switch-arrow {
  right: 0;
  border-color: #177FFF;
}

/*-- 按钮 button --*/
.ce_btn{
	padding: 0.2rem 0.12rem;
	display: flex;
	justify-content: center;
}
.p_button{
	flex: 1;
	height: 0.46rem;
	text-align: center;
	line-height: 0.46rem;
	border-radius: 0.03rem;
	background: #177FFF;
	font-size: 0.2rem;
	color: #ffffff;
	font-weight: 500;
	margin-left: 0.2rem;
	max-width: 2.75rem;
}
.p_button:first-child{
	margin-left: 0;
}
.p_button.border{
	background: rgba(23,127,255,0.1);
	color: #177FFF;
}
.p_button.disabled{
	background: #143359;
	color: rgba(91,132,182,0.6);
}
.p_button.border.disabled{
	background: #143359;
	color: rgba(91,132,182,0.6);
}
.icon_eye{
	display: block;
	width: 0.28rem;
	height: 0.28rem;
	margin-left: 0.05rem;
	background: url(../images/icon_eye01.png) no-repeat center;
	background-size: 0.2rem 0.14rem;
	position: relative;
	top: 0.12rem;
}
.icon_eye.show{
	background-image: url(../images/icon_eye02.png);
}

/*-- 其他公共 public --*/
.toast_poptips{
	position: fixed;
	top: 50%;
	left: 15%;
	right: 15%;
	text-align: center;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 2000;
}
.toast_poptips .cont{
	display: inline-block;
	padding: 0.1rem 0.23rem;
	background: rgba(9,16,24,0.6);
	border-radius: 0.06rem;
	font-size: 0.16rem;
	line-height: 0.22rem;
	color: #ffffff;
	min-width: 1.2rem;
}
.com_title{
	margin: 0.15rem 0.2rem 0.06rem;
	font-size: 0.14rem;
	line-height: 0.22rem;
	font-weight: normal;
	color: #6D7987;
	position: relative;
}
.com_title.spel{
	padding-left: 0.22rem;
}
.dialog_overlay{
	width: 100%;
	height: 100%;
	background: rgba(9,16,24,0.5);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 2000;
}
.dialog_box{
	width: 2.95rem;
	background: #111F2D;
	border-radius: 0.06rem;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -1.48rem;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 3000;
}
.dialog_cont{
	padding: 0.3rem 0.2rem;
}
.dialog_cont h3{
	text-align: center;
	font-size: 0.18rem;
	line-height: 0.25rem;
	color: #E4E4E4;
	font-weight: 500;
	margin-bottom: 0.2rem;
}
.dialog_cont .cont{
	font-size: 0.16rem;
	line-height: 0.24rem;
	color: #E4E4E4;
}
.dialog_btn{
	border-top: 1px solid rgba(156,166,177,0.2);
	display: flex;
	margin: 0 0.1rem;
}
.dialog_btn a{
	display: block;
	flex: 1;
	height: 0.46rem;
	line-height: 0.46rem;
	text-align: center;
	font-size: 0.2rem;
	font-weight: 500;
	color: #177FFF;
	border-left: 1px solid rgba(156,166,177,0.2);
}
.dialog_btn a:first-child{
	border-left: 0 none;
}
.dialog_btn a.cancel{
	color: #6D7987;
}
.dialog_close{
	display: block;
	width: 0.24rem;
	height: 0.24rem;
	background: url(../images/icon_close01_black.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.1rem;
	right: 0.1rem;
	z-index: 50;
}
.mt10{
	margin-top: 0.1rem !important;
}
.ared{
	color: #FF4540 !important;
}
.agray{
	color: #9CA6B1 !important;
}



/*-- 头部 header --*/

.header_inner {
  background: #091018;
  position: relative;
  height: 0.44rem;
  line-height: 0.44rem;
}

.header_inner > h1.title {
  font-size: 0.18rem;
  font-weight: 500;
  color: #E4E4E4;
  position: relative;
  z-index: 0;
  text-align: center;
  margin: 0 0.62rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.icon_text {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 0.15rem;
  padding: 0 0.15rem;
  color: #177FFF;
  z-index: 50;
}

.icon_back {
  width: 0.62rem;
  height: 0.44rem;
  background: url(../images/icon_back.png) no-repeat center;
  background-size: 0.25rem;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}


/*-- 登录 login --*/
.logo_page{
	padding: 0.3rem;
}
.logo_page .title{
	font-size: 0.24rem;
	line-height: 1;
	font-weight: 500;
	margin-bottom: 0.3rem;
}
.login_form .input_text{
	border-top: 0 none;
	border-bottom: 1px solid rgba(156,166,177,0.2);
	margin-top: 0.15rem;
	padding: 0;
}
.login_form .input_text:first-child{
	margin-top: 0;
}
.login_form .input_text .tit{
	line-height: 0.46rem;
	min-width: 0.74rem;
}
.login_form .input_text .t1{
	height: 0.46rem;
	padding-top: 0.12rem;
	padding-bottom: 0.12rem;
}
.login_form .code_btn{
	height: 0.46rem;
	line-height: 0.46rem;
}
.login_form .code_img{
	top: 0.09rem;
}
.login_form .text_clear{
	top: 0.09rem;
}
.login_form .icon_eye{
	top: 0.09rem;
}
.logo_page .ce_btn{
	padding-left: 0;
	padding-right: 0;
	margin-top: 0.2rem;
}


/*-- 内页 --*/
.cond_list{
	background: #111F2D;
	padding: 0 0.1rem;
}
.cond_list li{
	border-top: 1px solid rgba(156,166,177,0.2);
}
.cond_list li:first-child{
	border-top: 0 none;
}
.cond_list li .tit{
	padding: 0.14rem 0.1rem;
	position: relative;
}
.cond_list li .tit p{
	font-size: 0.16rem;
	line-height: 0.24rem;
	position: relative;
}
.right_link{
	padding-right: 0.2rem;
	line-height: 0.24rem;
	min-height: 0.24rem;
	font-size: 0.14rem;
	color: #177FFF;
	position: relative;
	background: url(../images/arrow01_black.png) no-repeat right center;
	background-size: 0.08rem 0.14rem;
}
.cond_list li .tit .right_link{
	position: absolute;
	top: 0.14rem;
	right: 0.1rem;
	z-index: 50;
}
.cond_list li.ok .tit p,
.cond_list li.error .tit p{
	padding-left: 0.24rem;
}
.cond_list li.ok .tit p:before,
.cond_list li.error .tit p:before{
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	position: absolute;
	top: 0.04rem;
	left: 0;
}
.cond_list li.ok .tit p:before{
	background: url(../images/tip_ic_ok.png) no-repeat center;
	background-size: 100%;
}
.cond_list li.error .tit p:before{
	background: url(../images/tip_ic_error.png) no-repeat center;
	background-size: 100%;
}
.cond_list li .tit .tips{
	display: block;
	font-size: 0.14rem;
	line-height: 0.18rem;
	color: #6D7987;
	position: relative;
	top: 0.02rem;
	margin-left: 0.24rem;
}
.cond_tips{
	font-size: 0.12rem;
	line-height: 0.18rem;
	margin: 0.2rem 0.2rem 0;
	color: #6D7987;
	text-align: center;
}
.cancel_reasonlist{
	background: #111F2D;
	padding: 0 0.1rem;
}
.cancel_reasonlist li{
	padding: 0.14rem 0.1rem;
	position: relative;
	font-size: 0.16rem;
	line-height: 0.24rem;
	border-top: 1px solid rgba(156,166,177,0.2);
}
.cancel_reasonlist li:first-child{
	border-top: 0 none;
}
.cancel_reasonlist li .icon_check{
	position: absolute;
	top: 0.16rem;
	right: 0.1rem;
	z-index: 50;
}
.other_reason{
	margin: 0.14rem 0 0.06rem;
	position: relative;
}
.other_reason textarea{
	display: block;
	width: 100%;
	border: 0 none;
	background: #091018;
	padding: 0.12rem 0.15rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	height: 1.1rem;
	resize: none;
	outline: none;
}
.other_reason textarea::-moz-placeholder {
  color: #6D7987;
}

.other_reason textarea::-webkit-input-placeholder {
  color: #6D7987;
}

.other_reason .num{
	background: #091018;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #6D7987;
	padding: 0 0.15rem 0.12rem;
	position: absolute;
	bottom: 0;
	right: 0;
	z-index: 50;
}

.upload_cont {
  background: #111F2D;
  margin-bottom: 0.1rem;
}

.upload_pic {
  position: relative;
}

.upload_pic .pic {
  padding: 0.14rem 0;
  position: relative;
}

.upload_pic .pic img {
  display: block;
  width: 2.37rem;
  margin: 0 auto;
}

.upload_pic .pic.finish:after{
	content: "";
	width: 0.2rem;
	height: 0.2rem;
	background: url(../images/finish_icon.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.1rem;
	left: 50%;
	margin-left: 1.36rem;
}

.upload_pic .pic .watermark{
	width: 0.51rem;
	height: 0.51rem;
	background: url(../images/ic_watermark.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.16rem;
	right: 50%;
	margin-right: -1.18rem;
	z-index: 50;
}


.upload_pic .btn {
	border-top: 1px solid rgba(156,166,177,0.2);
	display: block;
	text-align: center;
	font-size: 0.16rem;
	line-height: 0.44rem;
	color: #177FFF;
	margin: 0 0.1rem;
}

.reset_btn {
  border-top: 1px solid rgba(156,166,177,0.2);
	display: block;
	text-align: center;
	font-size: 0.16rem;
	line-height: 0.44rem;
	color: #177FFF;
	margin: 0 0.1rem;
}
.photo_tips {
  padding: 0.1rem;
}

.photo_tips .title {
  position: relative;
  line-height: 0.2rem;
  text-align: center;
  font-size: 0.14rem;
  font-weight: normal;
  color: #6D7987;
  margin-bottom: 0.1rem;
}

.photo_tips .title:before {
  content: '';
  width: 100%;
  height: 1px;
  background: rgba(156,166,177,0.2);
  position: absolute;
  top: 50%;
  left: 0;
}

.photo_tips .title span {
  background: #091018;
  padding: 0 0.1rem;
  position: relative;
  z-index: 50;
}
.photo_tips ul{
	padding: 0 0.05rem;
	display: flex;
}
.photo_tips ul li{
	flex: 1;
	margin-left: 0.06rem;
}
.photo_tips ul li:first-child{
	margin-left: 0;
}
.photo_tips ul li .pic{
	margin-bottom: 0.05rem;
}
.photo_tips ul li .pic img{
	display: block;
	width: 100%;
}
.photo_tips ul li span{
	display: block;
	text-align: center;
	font-size: 0.12rem;
	line-height: 0.18rem;
	color: #6D7987;
}
.photo_tips ul li span:before{
	content: "";
	display: inline-block;
	width: 0.12rem;
	height: 0.12rem;
	vertical-align: middle;
	margin-right: 0.04rem;
}
.photo_tips ul li span.ok:before{
	background: url(../images/photo_ic_ok.png) no-repeat center;
	background-size: 100%;
}
.photo_tips ul li span.error:before{
	background: url(../images/photo_ic_error.png) no-repeat center;
	background-size: 100%;
}
.icon_ling{
	display: inline-block;
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/icon_ling_black.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.03rem;
	left: 0;
}
.input_text .switch{
	position: absolute;
	top: 50%;
	margin-top: -0.1rem;
	right: 0.1rem;
	z-index: 50;
}
.acct_ctitem{
	background: #111F2D;
	margin: 0.1rem 0;
	padding: 0 0.1rem;
}
.acct_ctitem .title{
	border-bottom: 1px solid rgba(156,166,177,0.2);
	position: relative;
	padding: 0.14rem 0.2rem 0.14rem 0.1rem;
	font-size: 0.18rem;
	font-weight: 500;
	line-height: 0.24rem;
}
.acct_ctitem .title .arrow{
	display: block;
	width: 0.08rem;
	height: 0.14rem;
	background: url(../images/arrow01_black.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.07rem;
	right: 0.13rem;
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}
.acct_ctitem .title.on .arrow{
	-webkit-transform: rotate(-90deg);
	transform: rotate(-90deg);
}
.acct_ctlist li{
	padding: 0.14rem 0.1rem 0.14rem 0.4rem;
	border-top: 1px solid rgba(156,166,177,0.2);
	position: relative;
	font-size: 0.16rem;
	line-height: 0.22rem;
}
.acct_ctlist li:first-child{
	border-top: 0 none;
}
.acct_ctlist li .icon_check{
	position: absolute;
	top: 0.15rem;
	left: 0.1rem;
	z-index: 50;
}
.switch_item{
	border-top: 1px solid rgba(156,166,177,0.2);
	padding: 0.14rem 0.4rem 0.14rem 0.1rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
	position: relative;
}
.switch_item .switch{
	position: absolute;
	top: 50%;
	margin-top: -0.1rem;
	right: 0.1rem;
	z-index: 50;
}
.imp_txttips{
	margin: 0.14rem 0.2rem;
	padding-left: 0.22rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
	color: #6D7987;
	position: relative;
}
.imp_txttips:before{
	content: "";
	width: 0.16rem;
	height: 0.16rem;
	background: url(../images/icon_imp_black.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 0.02rem;
	left: 0;
}
.bank_input{
	height: 0.32rem;
	position: relative;
	padding-left: 1rem;
}
.bank_input .tit{
	font-size: 0.14rem;
	line-height: 0.32rem;
	color: #6D7987;
	position: absolute;
	top: 0;
	left: 0;
}
.bank_input .t1{
	display: block;
	width: 100%;
	text-align: right;
	height: 0.32rem;
	padding: 0.06rem 0;
	font-size: 0.14rem;
	line-height: 0.2rem;
	border: 0 none;
	background: none;
	color: #e4e4e4;
	outline: none;
}
.bank_input .t1::-moz-placeholder {
  color: rgba(156,166,177,0.2);
}

.bank_input .t1::-webkit-input-placeholder {
  color: rgba(156,166,177,0.2);
}
.acct_ctlist li .bank_input{
	position: relative;
	top: 0.05rem;
	margin-top: 0.08rem;
}
.error_info{
	display: block;
	font-size: 0.14rem;
	color: #FF4540;
	line-height: 0.2rem;
	position: relative;
	top: 0.03rem;
}
.type_title{
	padding: 0.14rem 0.2rem;
	font-size: 0.18rem;
	font-weight: 500;
	line-height: 0.24rem;
}
.xh_must_item{
	background: #111F2D;
	margin-top: 0.1rem;
	padding: 0 0.1rem;
}
.xh_must_item:first-child{
	margin-top: 0;
}
.xh_must_item .title{
	padding: 0.14rem 0.5rem 0.14rem 0.1rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
	position: relative;
}
.xh_must_item .title h5{
	font-size: 0.16rem;
	font-weight: 500;
	position: relative;
}
.xh_must_item .title h5.ok,
.xh_must_item .title h5.error{
	padding-left: 0.26rem;
}
.xh_must_item .title h5.ok:before,
.xh_must_item .title h5.error:before{
	content: "";
	width: 0.2rem;
	height: 0.2rem;
	position: absolute;
	top: 0.02rem;
	left: 0;
}
.xh_must_item .title h5.ok:before{
	color: #177FFF;
	background: url(../images/tip_ic_ok.png) no-repeat center;
	background-size: 0.16rem;
}
.xh_must_item .title h5.error:before{
	background: url(../images/tip_ic_error.png) no-repeat center;
	background-size: 0.16rem;
}
.xh_must_item .title .arrow{
	width: 0.2rem;
	height: 0.2rem;
	background: url(../images/arrow01_black.png) no-repeat center;
	background-size: 0.08rem 0.14rem;
	position: absolute;
	top: 0.16rem;
	right: 0.07rem;
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}
.xh_must_item .title .arrow.on{
	-webkit-transform: rotate(-90deg);
	transform: rotate(-90deg);
}
.xh_must_item .list {
	border-top: 1px  solid rgba(156,166,177,0.2);
	padding-left: 0.26rem;
}
.xh_must_item .list li{
	padding: 0.09rem 0.1rem;
	font-size: 0.14rem;
	line-height: 0.24rem;
	display: flex;
}
.xh_must_item .list li:first-child{
	padding-top: 0.19rem;
}
.xh_must_item .list li:last-child{
	padding-bottom: 0.19rem;
}
.xh_must_item .list li p{
	flex: 1;
	width: 100%;
	padding-left: 0.12rem;
	position: relative;
}
.xh_must_item .list li p:before{
	content: "";
	width: 0.04rem;
	height: 0.04rem;
	background: #091018;
	border-radius: 50%;
	position: absolute;
	top: 0.1rem;
	left: 0;
}
.xh_must_item .list li .link{
	display: inline-block;
	vertical-align: top;
	padding: 0 0.09rem;
	font-size: 0.14rem;
	line-height: 0.22rem;
	border: 1px solid #177FFF;
	color: #177FFF;
	border-radius: 0.03rem;
}
.ly_inputitem{
	margin-top: 0.1rem;
	position: relative;
}
.ly_inputitem:first-child{
	margin-top: 0;
}
.ly_inputitem .t1{
	display: block;
	width: 100%;
	background: #091018;
	border: 0 none;
	border-radius: 0.04rem;
	padding: 0.12rem 0.2rem;
	font-size: 0.16rem;
	line-height: 0.22rem;
	outline: none;
	color: #e4e4e4;
}
.ly_inputitem .text_clear{
	position: absolute;
	top: 0.09rem;
	right: 0.12rem;
	z-index: 50;
}
.input_errortips{
	display: block;
	margin-top: 0.1rem;
	font-size: 0.12rem;
	line-height: 0.14rem;
	color: #FF4540;
}
.result_main{
	background: #111F2D;
	margin-bottom: 0.1rem;
	padding: 0.26rem 0.4rem 0.2rem;
	text-align: center;
}
.white_bg .result_main{
	background: #091018;
}
.result_ok,
.result_error{
	display: block;
	width: 0.76rem;
	height: 0.76rem;
	margin: 0 auto 0.15rem;
}
.result_ok{
	background: url(../images/result_ok.png) no-repeat center;
	background-size: 100%;
}
.result_error{
	background: url(../images/result_error.png) no-repeat center;
	background-size: 100%;
}
.result_main h5{
	font-size: 0.22rem;
	font-weight: 500;
	line-height: 0.28rem;
	margin-bottom: 0.12rem;
}
.result_main h5.spel{
	font-size: 0.18rem;
}
.result_main p{
	font-size: 0.14rem;
	line-height: 0.2rem;
}
.witness_box {
  padding: 0.22rem 0.3rem 0.15rem;
  background: #111F2D;
  margin-bottom: 0.1rem;
  text-align: center;
  line-height: 0.2rem;
  color: #6D7987;
}

.witness_box h5 {
  font-size: 0.22rem;
  line-height: 0.28rem;
  margin-bottom: 0.1rem;
  font-weight: normal;
  color: #e4e4e4;
}

.witness_list {
  margin-top: 0.15rem;
}

.witness_list li {
  width: 100%;
  display: table;
  text-align: left;
  vertical-align: middle;
}

.witness_list li .icon {
  display: table-cell;
  width: 0.44rem;
  vertical-align: middle;
}

.witness_list li .icon img {
  display: block;
  width: 0.44rem;
  height: 0.44rem;
}

.witness_list li p {
  display: table-cell;
  height: 0.74rem;
  padding: 0.1rem 0 0.1rem 0.2rem;
  line-height: 0.2rem;
  font-size: 0.16rem;
  color: #e4e4e4;
  vertical-align: middle;
}

.witness_list li p em {
  font-size: 0.14rem;
  display: block;
  font-style: normal;
  color: #FF4540;
  margin-top: 0.02rem;
}
.queue_box {
  background: #091018;
  padding: 0.5rem 0.4rem 0.2rem;
  min-height: 3.1rem;
  margin-bottom: 0.1rem;
  text-align: center;
  line-height: 0.2rem;
  color: #6B7074;
}

.queue_box h5 {
  font-size: 0.22rem;
  line-height: 0.28rem;
  font-weight: normal;
  color: #e4e4e4;
  margin-bottom: 0.1rem;
}

.queue_level {
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 auto 0.35rem;
  position: relative;
}

.queue_level .bg {
  display: block;
  width: 100%;
  height: 100%;
  background: url(../images/queue_bg.png) no-repeat center;
  background-size: 100%;
  -webkit-animation: allrotate 1.6s infinite linear;
  animation: allrotate 1.6s infinite linear;
}

.queue_level .pic {
  width: 100%;
  height: 100%;
  background: url(../images/queue_cs_black.png) no-repeat center;
  background-size: 0.8rem;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}

@-webkit-keyframes allrotate {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes allrotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.queue_level .num {
  width: 100%;
  text-align: center;
  height: 1.2rem;
  line-height: 1.2rem;
  font-size: 0.62rem;
  color: #177FFF;
  font-weight: 500;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}
.notice_box{
	background: #091018;
	margin-bottom: 0.1rem;
	padding: 0.2rem 0.4rem;
	min-height: 2.9rem;
	text-align: center;
}
.notice_box .pic{
	margin-bottom: 0.1rem;
}
.notice_box .pic img{
	display: block;
	height: 1.2rem;
	margin: 0 auto;
}
.notice_box h5{
	font-size: 0.22rem;
	font-weight: 500;
	line-height: 0.28rem;
	margin-bottom: 0.12rem;
}
.notice_box p{
	font-size: 0.14rem;
	line-height: 0.2rem;
}
.protocol_list{
	background: #111F2D;
	padding: 0 0.1rem;
	margin: 0.1rem 0;
}
.protocol_list li{
	position: relative;
	border-bottom: 1px solid rgba(156,166,177,0.2);
}
.protocol_list li:last-child{
	border-bottom: 0 none;
}
.protocol_list li a{
	display: block;
	padding: 0.14rem 0.3rem 0.14rem 0.1rem;
	font-size: 0.16rem;
	line-height: 0.22rem;
	position: relative;
	color: #e4e4e4;
}
.protocol_list li a:after{
	content: "";
	width: 0.08rem;
	height: 0.14rem;
	background: url(../images/arrow01_black.png) no-repeat center;
	background-size: 100%;
	position: absolute;
	top: 50%;
	margin-top: -0.07rem;
	right: 0.1rem;
}
.rule_check{
	margin: 0.14rem 0.2rem;
	font-size: 0.14rem;
	padding: 0.02rem 0 0.02rem 0.3rem;
	color: #6D7987;
	line-height: 0.2rem;
	position: relative;
}
.rule_check a{
	color: #177FFF;
}
.rule_check .icon_check{
	position: absolute;
	top: 0.02rem;
	left: 0;
	z-index: 50;
}
.protocol_cont{
	padding: 0.2rem;
	background: #111F2D;
	font-size: 0.16rem;
	line-height: 0.28rem;	
}
.result_itemwrap{
	background: #111F2D;
	margin: 0.1rem 0;
	padding: 0 0.1rem;
}
.result_itemwrap .title{
	padding: 0.14rem 0.1rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
}
.result_info{
	padding-bottom: 0.1rem;
}
.result_info li{
	padding: 0.11rem 0.1rem;
	display: flex;
	font-size: 0.14rem;
	line-height: 0.2rem;
	align-items: center;
}
.result_info li:nth-child(2n+1){
	background: #091018;
}
.result_info li > p{
	flex: 1;
	width: 100%;
}
.result_info li .ct{
	max-width: 1.54rem;
	text-align: right;
}
.result_info li .state.ing{
	color: #6D7987;
}
.result_info li .state.error{
	color: #FF4540;
}
.result_info li .state.error .arrow{
	display: inline-block;
	vertical-align: top;
	position: relative;
	top: 0.03rem;
	width: 0.08rem;
	height: 0.14rem;
	background: url(../images/arrow02.png) no-repeat center;
	background-size: 100%;
	margin-left: 0.08rem;
}
.result_info li .state.ok{
	color: #6D7987;
}

.reject_item{
	background: #111F2D;
	margin: 0.1rem 0;
	padding: 0 0.1rem;
}
.reject_item .title{
	padding: 0.14rem 0.8rem 0.14rem 0.1rem;
	font-size: 0.16rem;
	line-height: 0.24rem;
	font-weight: 500;
	position: relative;
}
.reject_item .title h5{
	font-size: 0.16rem;
	font-weight: 500;
}
.reject_item .state{
	position: absolute;
	top: 0.14rem;
	right: 0.1rem;
	padding-left: 0.22rem;
	font-size: 0.14rem;
	line-height: 0.24rem;
}
.reject_item .state.ok{
	color: #177FFF;
	background: url(../images/tip_ic_ok.png) no-repeat 0 0.04rem;
	background-size: 0.16rem;
	
}
.reject_item .state.error{
	color: #FF4540;
	background: url(../images/tip_ic_error.png) no-repeat 0 0.04rem;
	background-size: 0.16rem;
}
.reject_item .cont {
	border-top: 1px  solid rgba(156,166,177,0.2);
	padding: 0.1rem;
	font-size: 0.14rem;
	line-height: 0.2rem;
}
.reject_item .cont p{
	padding: 0.06rem 0;
}
.select_list {
  background: #fff;
  padding: 0 0.1rem;
}
.select_list li {
  border-bottom: 1px solid #EBEDEF;
}
.select_list li span {
  display: block;
  min-height: 0.52rem;
  padding: 0.15rem 0.45rem 0.15rem 0.1rem;
  font-size: 0.16rem;
  line-height: 0.22rem;
  position: relative;
}
.select_list li:last-child {
  border-bottom: 0 none;
}
.select_list li.active {
  color: #177FFF;
}
.select_list li.active span:after {
  content: "";
  width: 0.18rem;
  height: 0.18rem;
  background: url(../images/icon_active.png) no-repeat center;
  background-size: 100%;
  position: absolute;
  top: 50%;
  margin-top: -0.09rem;
  right: 0.1rem;
}
/* 浏览器上传文件输入框 */
.file_uploader {
  position: absolute;
  top: 0;
  left: 0;
  height: 0;
  width: 0;
  opacity: 0;
  z-index: 9;
}



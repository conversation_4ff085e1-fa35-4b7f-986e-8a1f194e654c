<template>
  <div class="page_main">
    <headComponent :headerTitle="stepName" v-show="isShowHead"></headComponent>
    <article class="content">
      <keep-alive>
        <AccountCancellationAccountList :pageParam="accountCancellationAccountListParam"></AccountCancellationAccountList>
      </keep-alive>
      <div class="ce_btn mt20" v-show="parentBtnShow">
        <!--点击此按钮 触发所有子组件的putFormData,doSubmit事件-->
        <a
          class="ui button block rounded"
          :class="{ disabled: nextBtnDisabled, border: nextBtnCss }"
          @click.stop.prevent="emitNextEvent"
          >{{ nextBtnText }}</a
        >
      </div>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import AccountCancellationAccountList from '@/views/busComp/accountCancellationAccountList'

export default {
  components: { headComponent, AccountCancellationAccountList },
  data () {
    return {
      stepName: '测试页',
      flowName: 'gdhjg',
      emptyParam: [{}],
      appropriatenessCheckParam: [{
        businessName: '退市整理股权限',
        corpBeginDate: '',
        corpEndDate: '********',
        investTerm: '1',
        investTermDesc: '中期',
        investType: '02,03,04',
        investTypeDesc: '权益类,衍生品类,复杂或高风险类',
        isStrongMatch: '0',
        matchingFlag: '1',
        minRankFlag: '0',
        paperScore: '57.00',
        proRiskLevel: '9',
        proRiskLevelDesc: 'R4中等偏高风险',
        profFlag: '0',
        profType: '0',
        riskLevel: '7',
        riskLevelDesc: 'C2谨慎型'
      }],
      accessConditionsInspectionParam: [{
        accountList: '',
        checkItem: 'openAccountFlag|openAccountFlag|creditRecord|blackFlag|riskFlag|ageFlag',
        itemDesc: '您的深A、沪A、沪深场内基金账户都已经开满三户，无法继续增开|您在我司都已加挂了深A、沪A、沪深场内基金账户，可使用已有账户进行交易|不存在不良诚信记录|非重点监控账户及黑名单用户|风险等级非最低保守型',
        itemValue: '0|0|0|0|0',
        reasonDesc: '||您存在不良诚信记录，无法办理|您为重点监控账户，无法办理|您的风险等级不满足业务要求，无法办理'
      }],
      appMatchAgreementContentParam: [{
        agreeFno: 'sdx_match_agreement',
        agreeName: '产品或服务不适当警示及投资者确认书',
        agreementId: '49b8aa53df5842f5b5859bd620cb7e81',
        isRead: '',
        readTime: '0'
      }],
      returnVisitQuestionnaireParam: [{
        questionContent: '请问您提交的业务申请是否全程由您本人操作完成?',
        questionKind: '0',
        answereArray: '[{"isTrue":"1","answerContent":"是","answerNo":"1","questionScore":"20","optionOrder":"1604642331713"},{"isTrue":"0","answerContent":"否","answerNo":"2","questionScore":"0","optionOrder":"1604642343892"}]',
        paperTypes: 'tsgpkt_hfwj',
        questionNo: '1',
        subjectId: 'de6320408fd740a4a43f6c419960ab86'
      }, {
        questionContent: '请问您是否知悉退市整理期业务的投资风险、业务规则、法律法规等内容？',
        questionKind: '0',
        answereArray: '[{"isTrue":"1","answerContent":"是","answerNo":"1","questionScore":"20","optionOrder":"1604642380720"},{"isTrue":"0","answerContent":"否","answerNo":"2","questionScore":"0","optionOrder":"1604642393075"}]',
        paperTypes: 'tsgpkt_hfwj',
        questionNo: '2',
        subjectId: 'de6320408fd740a4a43f6c419960ab86'
      }, {
        questionContent: '请问您是否愿意承担该投资可能引起的损失和其他后果？',
        questionKind: '0',
        answereArray: '[{"isTrue":"1","answerContent":"是","answerNo":"1","questionScore":"20","optionOrder":"1604642427527"},{"isTrue":"0","answerContent":"否","answerNo":"2","questionScore":"0","optionOrder":"1604642438353"}]',
        paperTypes: 'tsgpkt_hfwj',
        questionNo: '3',
        subjectId: 'de6320408fd740a4a43f6c419960ab86'
      }, {
        questionContent: '请问投资者基本信息、风险承受能力评估问卷是否为您本人真实填写？',
        questionKind: '0',
        answereArray: '[{"isTrue":"1","answerContent":"是","answerNo":"1","questionScore":"20","optionOrder":"1604642470217"},{"isTrue":"0","answerContent":"否","answerNo":"2","questionScore":"0","optionOrder":"1604642482264"}]',
        paperTypes: 'tsgpkt_hfwj',
        questionNo: '4',
        subjectId: 'de6320408fd740a4a43f6c419960ab86'
      }, {
        questionContent: '请问您是否知悉自己的风险承受能力等级、可转债业务风险等级以及适当性匹配意见？',
        questionKind: '0',
        answereArray: '[{"isTrue":"1","answerContent":"是","answerNo":"1","questionScore":"20","optionOrder":"*************"},{"isTrue":"0","answerContent":"否","answerNo":"2","questionScore":"0","optionOrder":"*************"}]',
        questionNo: '5',
        subjectId: 'de6320408fd740a4a43f6c419960ab86'
      }],
      userInfoConfirmParam: [{
        name: '姓名',
        clientId: '001',
        fundAccount: '00',
        branchName: '营业部',
        telephone: '138'
      }],
      accountCancellationAccountListParam: [{
        accountCancellationAccountList: [{
          accountCancellationAssetList: '[{"state":"0","accountCancellationType":"1","mainFlag":"1","account":"************"}]',
          accountCancellationClientList: '[{"accountCancellationType":"0","account":"************"}]',
          accountCancellationFundAccountList: '[{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020291"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020292"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020293"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020294"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020295"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020296"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020297"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020298"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020299"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020200"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020201"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020202"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020203"},{"accountCode":"30","accountName":"中邮基金管理有限公司","accountCancellationType":"3","account":"ZY1000020204"}]',
          accountCancellationOtcAccountList: '[{"accountName":"沪市TA","accountCancellationType":"4","account":"99A100681524"},{"accountName":"深市TA","accountCancellationType":"4","account":"98**********"}]',
          accountCancellationStockAccountList: '[{"market":"00","marketName":"深A","holderStatus":"0","accountCancellationType":"2","account":"**********"},{"market":"10","marketName":"沪A","holderStatus":"0","accountCancellationType":"2","account":"A100681524"}]'
        }]
      }],
      publicParam: {
        serivalId: '001'
      }
    }
  },
  computed: {
    // 父组件的下一步按钮是否显示，保存在vuex中
    parentBtnShow() {
      return this.$store.state.businessNextBtnShow
    },
    // 父组件的下一步按钮的文字描述值，保存在vuex中
    nextBtnText() {
      return this.$store.state.nextBtnText
    },
    nextBtnDisabled() {
      return this.$store.state.nextBtnDisabled
    },
    nextBtnCss() {
      return this.$store.state.nextBtnCss
    },
    isShowHead() {
      return this.$store.state.isShowHead
    }
  },
  watch: {},
  mounted() {
    // 更新下一步按钮文字
    // this.$store.commit('updateNextBtnText', '下一步')
    // // 显示下一步按钮 初始化成功后再显示下一步按钮
    // this.$store.commit('updateBusinessNextBtnStatus', true)
    // this.$store.commit('updateNextBtnCss', false)
    // // 初始化成功后，再允许用户点击下一步按钮
    // this.$store.commit('updateNextBtnDisabled', false)
  },
  methods: {
    back() {
      this.$router.push({ name: 'index' })
    },
    async emitNextEvent() {
      const childrens = this.$children
      let reqParams = {} // 请求参数
      let formData = {} // 表单数据

      for (let index = 0; index < childrens.length; index++) {
        if (childrens[index].doCheck) {
          await childrens[index].doCheck() // 执行子组件的校验事件
        }
        if (childrens[index].reqParamPackage) {
          let childReqParam = childrens[index].reqParamPackage()
          // 合并子组件的请求参数
          Object.assign(reqParams, childReqParam)
        }
        if (childrens[index].putFormData) {
          // 合并子组件的表单数据
          formData = Object.assign(
            formData,
            childrens[index].putFormData()
          )
        }
      }

      _hvueToast({ mes: '提交成功' })
      console.log('reqParams', reqParams)
      console.log('formData', formData)

      for (let index = 0; index < childrens.length; index++) {
        if (childrens[index].handleResult) {
          // 需要对结果集进行处理
          childrens[index].handleResultFunc({})
          // 隐藏加载框
          _hvueLoading.closeLoading()
          return
        }
      }
    }
  }
}
</script>
<style>
</style>

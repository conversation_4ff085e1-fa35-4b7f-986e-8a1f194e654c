<template>
  <div class="page_main" :class="className" v-show="isShowPage">
    <headComponent v-show="!showPartIndex">
      <div class="header_inner">
        <a class="icon_back" @click.stop="pageBack"></a>
        <h1 class="title text-center">业务办理</h1>
      </div>
    </headComponent>
    <article class="content" v-show="!showPartIndex" style="background:#fff;">
      <div class="banner_box">
        <!-- <a class="search_btn" @click.stop="showPartIndex = 1">
          <span>搜索</span>
        </a> -->
        <div class="home_nav">
          <!-- <h5 class="title"><span>热门业务</span></h5> -->
          <ul>
            <li
              v-for="(item, index) in hotBusiness"
              :key="index"
              @click.stop="toBusiness(item)"
            >
              <i class="icon">
                <img :src="item.mobileImgUrl" />
              </i>
              <p>{{ item.businessName }}</p>
            </li>
          </ul>
        </div>
      </div>

      <!-- <div
        class="home_annbox"
        v-show="businessInProgress"
        @click.stop="goBusinessRecord"
      >
        <p>
          您有<b>{{ businessInProgress }}</b
          >个历史业务正在办理中
        </p>
        <em></em>
      </div> -->

      <div class="nav_main">
        <div
          class="nav_list"
          v-for="(typeItem, typeIndex) in showBusinessArr"
          :key="typeIndex"
        >
          <h5 v-show="typeItem.businessMenu.length > 0" class="title">
            {{ typeItem.groupName }}
          </h5>
          <div v-show="typeItem.businessMenu.length > 0" class="nav_comlist">
            <ul
              :style="{
                height: !+typeItem.showMore
                  ? typeItem.businessMenu.length > 2
                    ? '1rem'
                    : '0.5rem'
                  : Math.ceil(typeItem.businessMenu.length / 2) / 2 + 'rem'
              }"
            >
              <li
                v-for="(item, index) in typeItem.businessMenu"
                :key="index"
                @click.stop="toBusiness(item)"
              >
                <i class="icon"><img :src="item.mobileImgUrl"/></i>
                <h5>
                  <span>{{ item.businessName }}</span>
                </h5>
              </li>
            </ul>
            <div class="nav_morebtn" v-show="typeItem.showMore">
              <a
                :class="!+typeItem.showMore ? 'down' : 'up'"
                @click.stop="toggleShowMore(typeIndex)"
                href="javascript:;"
              >
                <span>{{ !+typeItem.showMore ? '展开更多' : '收起' }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </article>
    <queryBusiness
      v-if="queryShow"
      v-model="queryShow"
      @toBusiness="toBusiness"
    ></queryBusiness>
  </div>
</template>

<script>
import { businessList, queryInProgressBusiness } from '@/service/comServiceNew'
import { closeYgt, checkLogin } from '@/common/sso'
import queryBusiness from '@/views/queryBusiness'
import headComponent from '@/components/headComponent'
import { goBackXcApp } from '@/common/util'
export default {
  components: { headComponent, queryBusiness },
  name: 'index',
  inject: ['reload'],
  data() {
    return {
      isShowPage: false, // 是否显示页面
      className: 'index',
      showPartIndex: 0,
      isLogin: $h.getSession('isLogin', { decrypt: false }) || false,
      allBusinessArr: $h.getSession('allBusinessArr') || [],
      showBusinessArr: [],
      hotBusiness: $h.getSession('hotBusiness') || [],
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      businessInProgress: 0
    }
  },
  computed: {
    queryShow: {
      get() {
        return this.showPartIndex === 1
      },
      set(newVal) {
        this.showPartIndex = +!!newVal
      }
    },
    progressCheckShow: {
      get() {
        return this.showPartIndex === 2
      },
      set(newVal) {
        this.showPartIndex = +!!newVal
      }
    }
  },
  mounted() {
    
    this.clearBusinessSession()
    // window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack = $hvue.env == 'ths' ? goBackXcApp: this.goIndex
    this.$store.commit('updateReturnHomeConfirmFlag', false) // 关闭返回首页弹窗确认
    const ssoInfo = $h.getSession('ssoInfo', { decrypt: true })

    if (
      $h.getSession('isLogin', { decrypt: false }) &&
      $h.getSession('ygtUserInfo', { decrypt: false }).userId
    ) {
      this.queryInProgressBusiness()
    }
    if (
      $hvue.platform !== '0' &&
      ssoInfo &&
      ssoInfo.source &&
      ssoInfo.mobilecode &&
      ssoInfo.token &&
      ssoInfo.loginFlag > 1
    ) {
      // app登录后进入网厅直接调统一登录
      checkLogin(this.$route, () => {
        if ($h.getSession('allBusiness')) {
          if (!this.showBusinessArr.length) {
            this.getShowBusinessArr()
          }
          this.autoPageJump()
          return false
        }
        this.queryBusinessList(this.autoPageJump)
      })
      return
    }
    if ($h.getSession('allBusiness')) {
      if (!this.showBusinessArr.length) {
        this.getShowBusinessArr()
      }
      this.autoPageJump()
      return false
    }
    this.queryBusinessList(this.autoPageJump)
  },
  watch: {
    isLogin(val) {
      this.queryBusinessList()
    }
  },
  methods: {
    // 清除业务相关缓存
    clearBusinessSession() {
      $h.clearSession('agreementIds')
      $h.clearSession('historyLength')
    },
    // 自动页面跳转
    autoPageJump() {
      const toPage = JSON.parse($h.getSession('toPage', { decrypt: false }))
      const fromSiteUrl = $h.getSession('fromSiteUrl', { decrypt: false })
      if (toPage) {
        this.toBusiness(toPage)
        return
      }
      // if (fromSiteUrl && fromSiteUrl.includes('http')) {
      //   window.location.replace(fromSiteUrl)
      //   return
      // }
      // if (fromSiteUrl === '') {
      //   closeYgt(0, '1A', 0, 1) // 退出网厅
      //   return
      // }
      this.isShowPage = true
    },
    // 查询业务列表
    queryBusinessList(cb) {
      // console.log('queryBusinessList', $h.getSession('isLogin', { decrypt: false }))
      if (this.allBusinessArr.length > 0) {
        this.getShowBusinessArr()
        this.checkBusinessArr()

        cb && typeof cb === 'function' && cb()
        return
      }
      businessList({ source: 2 }, {}).then(
        res => {
          if (res.error_no === '0') {
            let allBusiness = []
            let ismpCompQueryMenu = res.ismpCompQueryMenu || res.results
            for (let i = 0; i < ismpCompQueryMenu.length; i++) {
              let el = ismpCompQueryMenu[i]
              let menus = (el.businessMenu = JSON.parse(el.businessMenu))
              for (let j = 0; j < menus.length; j++) {
                let menu = menus[j]
                menu.mobileImgUrl =
                  (process.env.NODE_ENV === 'development' ? '' : ROUTER_BASE) +
                  menu.mobileImgUrl
                allBusiness.push(menu)
              }
              this.allBusinessArr.push(el)
            }
            this.getShowBusinessArr()
            this.checkBusinessArr()
            $h.setSession('allBusinessArr', this.allBusinessArr)
            $h.setSession('allBusiness', allBusiness)
            $h.setSession('hotBusiness', this.hotBusiness)

            cb && typeof cb === 'function' && cb()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 获取需要展示的业务列表
    getShowBusinessArr() {
      this.hotBusiness = []
      this.showBusinessArr = this.allBusinessArr.map(item => {
        const businessMenu = item.businessMenu.filter(item => {
          if (
            item.isHot === '1' &&
            (!this.isLogin ||
              !$hvue.config.loginHideBusiness.includes(item.businessCode))
          ) {
            this.hotBusiness.push(item)
          }
          return (
            !this.isLogin ||
            !$hvue.config.loginHideBusiness.includes(item.businessCode)
          )
        })
        return {
          showMore: businessMenu.length > 4 ? '0' : false,
          groupName: item.groupName,
          groupCode: item.groupCode,
          businessMenu
        }
      })
    },
    // 根据类别的业务数量控制展开更多按钮的展示隐藏
    checkBusinessArr() {
      this.showBusinessArr.forEach(item => {
        if (item.businessMenu.length > 4) {
          this.$set(item, 'showMore', '0')
        }
      })
    },
    // 通过businessCode获取业务菜单项
    getMenuItemByBusinessCode(businessCode) {
      const allBusiness = $h.getSession('allBusiness')
      return allBusiness.filter(item => {
        return item.businessCode === businessCode
      })[0]
    },
    // 展示/隐藏更多
    toggleShowMore(index) {
      this.showBusinessArr[index].showMore =
        +!+this.showBusinessArr[index].showMore + ''
    },
    // 跳转业务办理
    toBusiness(menuItem) {
      let _menuItem = menuItem
      if (!_menuItem.mobileBusinessUrl && _menuItem.businessCode) {
        _menuItem = this.getMenuItemByBusinessCode(_menuItem.businessCode)
      }

      let url = _menuItem.mobileBusinessUrl || ''
      let state = _menuItem.mobileState || ''
      let businessCode = _menuItem.businessCode || ''

      // console.log('toBusiness', _menuItem)
      // 判断是否为在建业务
      if (state === '2') {
        _hvueToast({ mes: '业务建设中，敬请期待' })
        $h.clearSession('toPage')
        return
      }
      if (!url.includes('http') && businessCode !== 'wdzj') {
        url = 'business'
      }
      // if (businessCode === 'xh') {
      //   url = 'closeAccount'
      // }

      this.ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {}
      $h.clearSession('toPage')
      if (
        (this.ygtUserInfo && this.ygtUserInfo.userId && this.isLogin) ||
        $hvue.config.noLoginBusiness.includes(businessCode)
      ) {
        // 已登录
        if (url.includes('http')) {
          // 2.0业务为完整地址
          this.jumpToWt2(url)
          return
        }

        this.$router.push({
          name: url,
          query: {
            type: businessCode
          }
        })
      } else {
        // 未登录
        if ($hvue.platform === '0') {
          // 非APP渠道
          $h.setSession('toPage', JSON.stringify(_menuItem), { encrypt: false })
          this.$router.push({ name: 'login' })
        } else {
          checkLogin(
            { path: '/business', query: { type: businessCode } },
            () => {
              if (url.includes('http')) {
                // 2.0业务为完整地址
                this.jumpToWt2(url)
                return
              }
              this.$router.push({
                name: url,
                query: {
                  type: businessCode
                }
              })
            }
          )
        }
      }
    },
    // 跳转至2.0业务
    jumpToWt2(url) {
      if (url.includes('http')) {
        if ($hvue.platform === '0') {
          // 非APP渠道拦截
          _hvueAlert({
            title: '错误提示',
            mes: '当前渠道不支持该业务'
          })
          this.isShowPage = true
          return
        }

        const wt3Url = window.location.origin + window.location.pathname
        const ssoInfo = $h.getSession('ssoInfo', { decrypt: true }) || {}
        // ssoInfo.ssoToken = 'f29f680a-939b-49ae-8b19-3ac496f5e08c'
        console.log('ssoInfo', ssoInfo)
        if (
          ssoInfo &&
          ssoInfo.params &&
          ssoInfo.params.user_token &&
          ssoInfo.params.mobilecode &&
          ssoInfo.params.appid
        ) {
          const ssoToken = encodeURIComponent(ssoInfo.params.user_token)
          const mobilecode = encodeURIComponent(ssoInfo.params.mobilecode)
          const appid = encodeURIComponent(ssoInfo.params.appid)
          // url = url.replace('https://test-xsgt.test.zszq.net', 'http://192.168.40.231:8081')
          // url = 'http://192.168.40.231:8081/m/ygt/index.html#!/convertibleBond/conditionCheck.html'
          if (url.includes('?')) {
            window.location.href = url.replace(
              /\?(.*)#!/,
              `?$1&isFromwt3=${wt3Url}&token=${ssoToken}&mobile=${mobilecode}&appID=${appid}&fundAccount=${this.ygtUserInfo.clientId}#!`
            )
            return
          }
          window.location.href = url.replace(
            '#!',
            `?isFromwt3=${wt3Url}&token=${ssoToken}&mobile=${mobilecode}&appID=${appid}&fundAccount=${this.ygtUserInfo.clientId}#!`
          )
        } else {
          _hvueAlert({
            title: '错误提示',
            mes: '无法获取统一登录信息，请重新登录',
            callback: () => {
              this.logout()
            }
          })
        }
      }
    },
    // 查询在途业务
    queryInProgressBusiness(cb) {
      queryInProgressBusiness({
        clientId: this.ygtUserInfo.clientId,
        queryFlag: 0
      }).then(res => {
        if (res.error_no === '0') {
          const businessHandlingRecord = res.businessHandlingRecord[0]
          const flowList = JSON.parse(businessHandlingRecord.data)

          this.businessInProgress = +businessHandlingRecord.totalRows

          cb && typeof cb === 'function' && cb()
        } else {
          _hvueToast({
            icon: 'error',
            mes: res.error_info
          })
        }
      })
    },
    // 跳转业务进度查询
    goBusinessRecord() {
      this.$router.push({
        name: 'businessRecord'
      })
      // this.showPartIndex = 2
    },
    // 退出登录
    logout() {
      // h5访问时退出登录
      $h.clearSession()
      $hvue.env == 'ths' ? goBackXcApp() : this.$router.push({ name: 'index' })
      // if ($hvue.env == 'ths') {
      //     invokeNative('8002', '000', {}, data => {})
      // }
      // if ($hvue.platform === '0') {
      //   this.reload()
      //   this.isLogin = false
      //   this.ygtUserInfo = {}
      // } else {
      //   closeYgt(0, '1A', 0, 1) // 退出网厅
      // }
    },
    // 返回
    pageBack() {
      this.logout()
    },
    goIndex(){
      this.$router.push({ name: 'index' })
    }
  }
}
</script>

<style scoped>
.nav_main{
	padding-bottom: 0.2rem;
}

.nav_list .title {
	padding: 0.2rem 0.1rem 0.08rem;
	line-height: 0.24rem;
	font-weight: 500;
	font-size: 0.17rem;
	color: #333;
}

.nav_list ul {
	background: #fff;
	padding: 0 0.1rem;
}

.nav_list ul li {
	border-bottom: 1px solid #eee;
	position: relative;
	padding: 0.12rem 0;
	margin-left: 0.67rem;
	line-height: 0.2rem;
	font-size: 0.13rem;
	color: #999;
}

.nav_list ul li .icon{
	display: block;
	width: 0.5rem;
	height: 0.5rem;
	padding: 0.05rem;
	background: #fff;
	-moz-border-radius: 0.08rem;
	-webkit-border-radius: 0.08rem;
	border-radius: 0.08rem;
	box-shadow: 0 0 0.07rem 0 rgba(0,0,0,0.05);
	overflow: hidden;
	position: absolute;
	top: 50%;
	margin-top: -0.25rem;
	left: -0.65rem;
}

.nav_list ul li .icon img {
	display: block;
	width: 100%;
	height: 100%;
}

.nav_list ul li h5 {
	font-weight: normal;
	color: #333;
	font-size: 0.16rem;
	line-height: 0.26rem;
}

.nav_list ul li .not_time{
	line-height: 0.2rem;
	font-size: 0.12rem;
	color: #999;
	padding: 0 0.08rem;
	position: absolute;
	top: 0.15rem;
	right: 0;
}

.nav_list ul li .not_time:before,
.nav_list ul li .not_time:after{
	content: "";
	width: 0.06rem;
	height: 1px;
	background: #999;
	position: absolute;
	top: 50%;
}

.nav_list ul li .not_time:before{
	left: 0;
}

.nav_list ul li .not_time:after{
	right: 0;
}

.nav_list ul li .state{
	font-size: 0.13rem;
	color: #999;
	line-height: 0.2rem;
	position: absolute;
	bottom: 0.12rem;
	right: 0.15rem;
}

.nav_list ul li .state.ok,
.nav_list ul li .state.ing{
	color: #999;
}

.nav_list ul li .state.error{
	color: #F3A91C;
}
</style>></style>

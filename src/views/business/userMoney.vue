<template>
  <div class="page_main">
    <headComponent>
      <div class="header_inner">
        <a class="icon_back" @click.stop="pageBack"></a>
        <h1 class="title text-center">我的资金</h1>
        <a class="icon_text" @click.stop="toAccessDetail">转取转存明细</a>
      </div>
    </headComponent>
    <article class="content">
      <div class="fund_topbox">
        <div class="my_fundbox">
          <div class="pic" id="echartss"></div>
          <ul class="list">
            <li>
              <i style="background: #336FCA ;"></i>可用现金
              <span>{{moneyResult.enableBalance|formatMoney}}</span>
            </li>
            <li>
              <i style="background: #A5C0EE ;"></i>可取现金
              <span>{{moneyResult.fetchBalance|formatMoney}}</span>
            </li>
            <li>
              <i style="background: #E56C6E ;"></i>股票市值
              <span>{{moneyResult.marketValue|formatMoney}}</span>
            </li>
            <li>
              <i style="background: #F8C82A ;"></i>冻结资金
              <span>{{moneyResult.frozenBalance|formatMoney}}</span>
            </li>
          </ul>
        </div>
        <div class="bk_transferlink">
          <a class="out" @click.stop="toYzzz">
            <h5>银行转取</h5>
            <p>证券账户转入银行卡</p>
          </a>
          <a class="in" @click.stop="toYzzz">
            <h5>银行转存</h5>
            <p>银行卡转入证券账户</p>
          </a>
        </div>
      </div>
      <div class="fund_botbox" v-if="StockResult.length >0">
        <h5 class="com_title">股票市值</h5>
        <div class="stock_infobox">
          <ul>
            <li v-for="(it,index) in StockResult" :key="index">
              <p>
                {{it.stockName}}
                <em>{{it.stockCode}}</em>
              </p>
              <span class="info">{{it.holdAmount}}股</span>
            </li>
          </ul>
        </div>
        <div class="stock_infobtn">
          <a v-show="currentPage>1" class="prev" @click.stop="currentPage--">上一批</a>
          <a v-show="currentPage < totalPage" class="next" @click.stop="currentPage++">下一批</a>
        </div>
      </div>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent'
import echarts from 'echarts'
import { queryUserMoney, queryStockPositions } from '@/service/comServiceNew'

export default {
  components: {
    headComponent
  },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      echarts_option: {
        title: {
          show: true,
          text: '总资产',
          textStyle: {
            color: '#333333',
            fontSize: '22'
          },
          subtext: '总资产',
          subtextStyle: {
            color: '#999999',
            fontSize: '14'
          },
          left: 'center',
          top: 'middle'
        },
        series: [
          {
            type: 'pie',
            radius: ['80%', '100%'],
            avoidLabelOverlap: false,
            legendHoverLink: false,
            hoverAnimation: false,
            stillShowZeroSum: true,
            itemStyle: {
              borderColor: '#FFFFFF',
              borderWidth: 1
            },
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: []
          }
        ]
      },
      moneyResult: {}, // 用户资金情况
      StockResult: [], // 用户持仓情况
      totalPage: 1, // 持仓总页数
      currentPage: 1, // 持仓当前页数
      numPerPage: 5 // 查询条数
    }
  },
  watch: {
    currentPage () {
      this.stockPositions()
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    this.userMoney()
    this.stockPositions()
  },
  methods: {
    initEchart () {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById('echartss'))
      // 绘制图表，this.echarts1_option是数据
      myChart.setOption(this.echarts_option)
    },
    // 查资金
    userMoney () {
      let _this = this
      queryUserMoney({
        clientId: this.ygtUserInfo.clientId,
        fundAccount: this.ygtUserInfo.fundAccount
      }).then(
        res => {
          if (res.error_no === '0') {
            _this.moneyResult = res.results
              ? res.results[0]
              : res.queryAssetInfo[0]
            let filters = this.$root.$options.filters
            _this.echarts_option.title.text = filters.formatMoney(
              _this.moneyResult.asset
            )
            let data = [
              {
                value: _this.moneyResult.enableBalance,
                name: '可用现金',
                itemStyle: {
                  color: '#336FCA'
                }
              },
              {
                value: _this.moneyResult.fetchBalance,
                name: '可取现金',
                itemStyle: {
                  color: '#A5C0EE'
                }
              },
              {
                value: _this.moneyResult.marketValue,
                name: '股票市值',
                itemStyle: {
                  color: '#E56C6E'
                }
              },
              {
                value: _this.moneyResult.frozenBalance,
                name: '冻结资金',
                itemStyle: {
                  color: '#F8C82A'
                }
              }
            ].sort(function (a, b) {
              return a.value - b.value
            })
            Object.assign(_this.echarts_option.series[0].data, data)
            _this.initEchart()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 查持仓
    stockPositions () {
      queryStockPositions({
        clientId: this.ygtUserInfo.clientId,
        fundAccount: this.ygtUserInfo.fundAccount,
        branchNo: this.ygtUserInfo.branchNo,
        currentPage: this.currentPage,
        numPerPage: this.numPerPage
      }).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results ? res.results[0] : res.queryStockInfo[0]
            this.totalPage = results.totalPages
            this.StockResult = JSON.parse(results.data)
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 转取转存明细
    toAccessDetail () {
      this.$router.push({ name: 'accessDetail', params: {} })
    },
    // 跳转银证转账
    toYzzz () {
      let msg = {
        funcNo: '60099',
        actionType: '6', // 打开交易页面
        params: {
          pageName: '107',
          account_type: 'A' // A普通账户，B两融账户
        }
      }
      $h.callMessageNative(msg)
    },
    // 返回
    pageBack () {
      this.$router.push({ name: 'index', params: {} })
    }
  }
}
</script>

<style>
</style>

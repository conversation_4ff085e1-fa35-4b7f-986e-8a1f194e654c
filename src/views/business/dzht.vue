<template>
  <div>
    <headComponent v-if="!showProtocolParam.isShow" headerTitle="电子合同"></headComponent>
    <article v-if="prodCode =='' || showPage ==0" class="content">
      <div class="pro_topbox">
        <div class="pro_select" :class="{on:showSelect}" @click.stop="showSelect = !showSelect">
          <strong>选择电子合同签订日期</strong>
        </div>
      </div>
      <div class="pro_ctbox">
        <scroll
          :height="scrollheight"
          :pullupActive="!noMoreData"
          @pullingDown="pullDownFun"
          @pullingUp="pullupFun"
          ref="scrollProduct"
          :tiptxt="tiptext"
        >
          <ul>
            <li v-for="(item,index) in productList" :key="index" @click.stop="showProtocolList(item)">
              <h5>{{item.prodName}}</h5>
              <p>电子合同签订日期：{{item.econtractDate|formatDate('YYYY.MM.DD')}}</p>
              <p>购入日期：{{item.initDate|formatDate('YYYY.MM.DD')}}</p>
            </li>
          </ul>
        </scroll>
      </div>
      <!-- 弹出层 -->
      <div v-if="showSelect" class="pro_selelayer">
        <div class="shade" @click.stop="showSelect = false"></div>
        <ul>
          <li :class="{active:sortTime == 1}" @click.stop="sortByTime(1)">
            <span>从近到远</span>
          </li>
          <li :class="{active:sortTime == 0}" @click.stop="sortByTime(0)">
            <span>从远到近</span>
          </li>
        </ul>
      </div>
    </article>
    <article v-else-if="showPage == 1 && !showProtocolParam.isShow" class="content">
      <div class="pro_infotitle">
        <h5>{{prodName}}</h5>
      </div>
      <h5 class="com_title">{{comTitle}}</h5>
      <div class="rule_ctbox">
        <ul>
          <li v-for="(item,index) in dzhtList" :key="index">
            <a @click.prevent="showProtocolClick(item)">《{{item.agreeTitle}}》</a>
          </li>
        </ul>
      </div>
      <p class="rule_time">电子合同签订日期：{{econtractDate|formatDate('YYYY.MM.DD')}}</p>
      <!-- <p class="rule_time">购入日期：{{initDate|formatDate('YYYY.MM.DD')}}</p> -->
      <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="showPage = 0">返回</a>
      </div>
    </article>

    <showProtocol
      v-if="showProtocolParam.isShow"
      v-model="showProtocolParam.isShow"
      :name="showProtocolParam.title"
      :content="showProtocolParam.content"
      :filePath="showProtocolParam.filePath"
      btnDesc="返回"
      @showProtocolCall="showProtocolCall"
    ></showProtocol>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
import showProtocol from '@/components/showProtocol' // 协议详情
import scroll from '@/components/scroll'
import {
  queryProduct,
  queryDzhtByProduct,
  queryDzhtDetail
} from '@/service/comServiceNew'
export default {
  components: {
    headComponent,
    scroll,
    showProtocol
  },
  data () {
    return {
      productList: [], // 产品列表
      dzhtList: [], // 电子合同列表
      showSelect: false, // 是否展示时间排序选择列表
      showPage: 0, // 展示页面 0产品列表 1协议列表 2协议详情
      sortTime: 1, // 排序方式 从近到远1 从远到近0 默认1
      prodCode: '', // 当前选择的产品编号
      prodName: '', // 当前选择的产品名称
      initDate: '', // 当前选择的产品购入时间
      econtractDate: '', // 当前选择的产品签署时间
      comTitle: '点击协议可查看协议内容详情',
      showProtocolParam: {
        isShow: false, // 是否展示
        title: '', // 协议名称
        filePath: '', // 协议附件地址
        content: '' // 协议内容
      }, // 协议详情组件参数
      // 下拉加载组件参数
      scrollheight: '100%',
      pulldown: true,
      pullup: true,
      curPage: 1,
      numPerPage: 10,
      totalPages: 0,
      noMoreData: false,
      tiptext: '没有更多内容啦'
    }
  },
  created () {
    this.scrollheight = window.innerHeight - 44
    this.queryProduct()
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    sortByTime (type) {
      this.showSelect = false
      if (this.sortTime == type) {
        return
      }
      this.sortTime = type
      this.queryProduct()
    },

    pullDownFun () {
      // console.log('down')
      this.curPage = 1
      this.noMoreData = true
      this.queryProduct()
    },
    pullupFun () {
      // console.log('up')
      this.curPage++
      this.queryProduct()
    },
    queryProduct () {
      queryProduct({
        fundAccount: $h.getSession('ygtUserInfo', {decrypt: false}).fundAccount,
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        name: $h.getSession('ygtUserInfo', {decrypt: false}).name,
        branchNo: $h.getSession('ygtUserInfo', {decrypt: false}).branchNo,
        sortDirection: this.sortTime,
        currentPage: this.curPage
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.results ? res.results[0] : res.DataSet[0]
            this.totalPages = _results.totalPages
            this.curPage = _results.currentPage
            _results.dataRow = JSON.parse(_results.dataRow)

            if (_results.currentPage == 1 && _results.dataRow) {
              this.productList = _results.dataRow
            } else if (_results.currentPage > 1 && _results.dataRow != '') {
              this.productList = this.productList.concat(_results.dataRow)
            }
            if (_results.currentPage >= _results.totalPages) {
              this.noMoreData = true
            }
            this.$refs.scrollProduct.refresh()
          } else {
            this.noMoreData = true
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    queryDzhtByProduct () {
      queryDzhtByProduct({
        fundAccount: $h.getSession('ygtUserInfo', {decrypt: false}).fundAccount,
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        name: $h.getSession('ygtUserInfo', {decrypt: false}).name,
        branchNo: $h.getSession('ygtUserInfo', {decrypt: false}).branchNo,
        relProductCode: this.prodCode
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.results ? res.results[0] : res.DataSet[0]
            this.dzhtList = JSON.parse(_results.dataRow)
            if (this.dzhtList.length < 1) {
              this.comTitle = '无协议内容'
            } else {
              this.comTitle = '点击协议可查看协议内容详情'
            }
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    queryDzhtDetail (item) {
      queryDzhtDetail({
        clientId: $h.getSession('ygtUserInfo', {decrypt: false}).clientId,
        agreeNoIn: item.agreeNoIn,
        primaryKeyId: item.primaryKeyId
      }).then(
        res => {
          if (res.error_no === '0') {
            let _results = res.results ? res.results[0] : res.DataSet[0]
            if (_results.agreeSignedFilepath == '') {
              // 展示协议内容agreeSignedContent
              // 如果需要用纯H5展示pdf，可以用这里的方法，但是无法跨域（后端需做处理），建议在app中调用原生查看pdf的50240方法
              this.showProtocolParam.isShow = true
              this.showProtocolParam.title = _results.agreeTitle
              this.showProtocolParam.content = _results.agreeSignedContent
              this.showProtocolParam.filePath = _results.agreeSignedFilepath
            } else {
              // 调用壳子的查看pdf，没有跨域烦恼
              let param = {
                funcNo: '50240',
                url: _results.agreeSignedFilepath,
                title: _results.agreeTitle,
                moduleName: 'open' // 必须为open
              }
              let result = $h.callMessageNative(param)
            }
          } else {
            this.noMoreData = true
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    showProtocolList (item) {
      if (this.prodCode != item.prodCode) {
        this.prodCode = item.prodCode
        this.initDate = item.initDate
        this.econtractDate = item.econtractDate
        this.prodName = item.prodName
        this.queryDzhtByProduct()
      }
      this.showPage = 1
    },
    showProtocolClick (item) {
      // 展示协议详情
      this.queryDzhtDetail(item)
    },
    showProtocolCall () {
      // 展示协议详情返回
      this.showProtocolParam.isShow = false
    },
    pageBack () {
      if (this.showPage == 0) {
        this.$router.push({
          name: 'index'
        })
      } else {
        this.showPage = 0
      }
    }
  }
}
</script>

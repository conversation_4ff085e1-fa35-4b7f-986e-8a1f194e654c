<!-- 新三板-选择开通交易权限类型 -->
<template>
  <div>
    <div class="level_title">
      <h5>请选择您需要开通的新三板交易权限</h5>
      <p class="imp">您的前10个交易日日均资产：{{assets}} 万元</p>
    </div>
    <ul class="select_levellist">
      <li>
        <div class="tit" @click.stop.prevent="titClick('200',3)">
          <h5>一级交易权限</h5>
          <span
            :class="{state:classType['3']>0,error:classType['3']>1,icon_check:classType['3']==0,checked : selectedType==3}"
          >{{getState('200',3)}}</span>
        </div>
        <div class="cont">
          <p>可交易精选层、创新层、基础层股票</p>
          <p>日均资产200万元以上（前10个交易日）</p>
        </div>
      </li>
      <li>
        <div class="tit" @click.stop.prevent="titClick('150',4)">
          <h5>二级交易权限</h5>
          <span
            :class="{state:classType['4']>0,error:classType['4']>1,icon_check:classType['4']==0,checked : selectedType==4}"
          >{{getState('150',4)}}</span>
        </div>
        <div class="cont">
          <p>可交易精选层、创新层股票</p>
          <p>日均资产150万元以上（前10个交易日）</p>
        </div>
      </li>
      <li>
        <div class="tit" @click.stop.prevent="titClick('100',5)">
          <h5>三级交易权限</h5>
          <span
            :class="{state:classType['5']>0,error:classType['5']>1,icon_check:classType['5']==0,checked : selectedType==5}"
          >{{getState('100',5)}}</span>
        </div>
        <div class="cont">
          <p>可交易精选层股票</p>
          <p>日均资产100万元以上（前10个交易日）</p>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      classType: { '3': 0, '4': 0, '5': 0 }, // 分别为一类二类三类的三种权限办理情况，0可以办理，1当前权限，2不满足
      assets: 0,
      openedType: 0, // 当前权限，3  4  5 分别对应一类 二类 三类
      selectedType: 0, // 选择的权限类型，3  4  5 分别对应一类 二类 三类
      changeFlag: '0', // 是否是调整新三板权限
      allowNext: true // 是否允许下一步
    }
  },
  activated () {
    this.classType = { '3': 0, '4': 0, '5': 0 }
    this.assets = 0
    this.openedType = 0
    this.selectedType = 0
    this.changeFlag = '0'
    this.allowNext = true
    if (this.pageParam.length > 0) {
      this.openedType = this.pageParam[0].openType
      this.assets = parseFloat(this.pageParam[0].assets).toFixed(2)
      if (this.assets < 100) {
        this.classType['3'] = 2
        this.classType['4'] = 2
        this.classType['5'] = 2
        // 都不滿足 放弃办理
        // 更新下一步按钮文字
        this.$store.commit('updateNextBtnText', '放弃办理')
        this.allowNext = false
      } else if (this.assets < 150) {
        this.classType['3'] = 2
        this.classType['4'] = 2
      } else if (this.assets < 200) {
        this.classType['3'] = 2
      }
      if (this.openedType != '') {
        this.classType[this.openedType] = 1
        this.changeFlag = '1'
      }
    }
  },
  methods: {
    getState (ass, type) {
      if (type == this.openedType) {
        return '当前权限'
      }
      if (parseFloat(ass) > parseFloat(this.assets)) {
        return '不满足'
      }
      return ''
    },
    titClick (ass, type) {
      if (parseFloat(ass) > parseFloat(this.assets)) {
        return
      }
      if (type == this.openedType) {
        return
      }
      this.selectedType = this.selectedType == type ? 0 : type
    },
    checkSubmit () {
      if (this.selectedType == 0 && this.allowNext) {
        _hvueToast({
          mes: '请选择交易权限'
        })
        return false
      } else if (this.selectedType == 0 && !this.allowNext) {
        // 返回首页
        this.$router.push({ name: 'index' })
        return false
      }
      if (this.selectedType == this.openedType) {
        _hvueToast({
          mes: '当前选择的权限已开通，请勿重新申请'
        })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          if (this.selectedType > this.openedType && this.openedType != '') {
            _hvueConfirm({
              mes:
                '您选择的交易权限比您当前的交易权限更小，请问是否确认继续变更？',
              opts: [
                {
                  txt: '取消',
                  color: false
                },
                {
                  txt: '确定',
                  color: true,
                  callback: () => {
                    resolve()
                  }
                }
              ]
            })
          } else {
            // 可以下一步
            resolve()
          }
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = {
        oldOpenType: this.openedType,
        changeFlag: this.changeFlag,
        newOpenType: this.selectedType
      } // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        qualityInspectionInfo: {
          transFlag: this.pageParam[0].transFlag,
          oldOpenType: this.openedType,
          changeFlag: this.changeFlag,
          newOpenType: this.selectedType,
          asset_ten: this.assets
        }
      } // 需要保存的表单数据
      return formData
    }
  }
}
</script>

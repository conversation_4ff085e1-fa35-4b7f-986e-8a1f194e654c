<!-- 视频见证组件 -->
<template>
  <div v-cloak>
    <!--头部 -->
    <div v-show="pageStatus !== 3">
      <div class="witness_box">
        <h5>将由见证人员与您进行视频连线</h5>
        <p>请做好以下准备</p>
        <ul class="witness_list">
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon01.png" />
            </i>
            <p>光线充足的位置</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon02.png" />
            </i>
            <p>推荐使用WIFI网络</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon03.png" />
            </i>
            <p>周遭环境安静</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon04.png" />
            </i>
            <p>
              视频认证时间<em>交易日 {{ videoTime }}</em>
            </p>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class=" p_button" @click.stop="setIdInRedis">开始视频</a>
      </div>
    </div>

    <!-- <div v-show="pageStatus == 3">
      <div class="notice_box">
        <div class="item">
          <div class="pic">
            <img
              v-if="rejectStatus == 2 || rejectStatus == 3 || rejectStatus == 4"
              src="@/assets/images/notice_ic04.png"
            /><img v-else src="@/assets/images/notice_ic05.png" />
          </div>
          <h5>{{ rejectTpye }}</h5>
          <p>{{ rejectReason }}</p>
        </div>
      </div>
      <div class="ce_btn">
        <a
          class="p_button"
          v-show="
            rejectStatus === 4 || rejectStatus === 3 || rejectStatus === 2
          "
          @click.stop="rejectVideoQuery"
          >重新上传证件</a
        >
        <a
          class="p_button"
          v-show="rejectStatus === 1"
          @click.stop="setIdInRedis"
          >重新视频</a
        >
      </div>
    </div> -->
    <div v-show="pageStatus == 3">
      <!-- <div class="notice_box">
        <div class="item">
          <div class="pic">
            <img
              v-if="rejectStatus == 2 || rejectStatus == 3 || rejectStatus == 4"
              src="@/assets/images/notice_ic04.png"
            /><img v-else src="@/assets/images/notice_ic05.png" />
          </div>
          <h5>{{ rejectTpye }}</h5>
          <p>{{ rejectReason }}</p>
        </div>
      </div> -->
      <div class="reject_item" v-for="(item, index) in msgInfo" :key="index">
        <div class="title">
          <h5>{{ item.rejectTpye }}</h5>
          <span class="state error">已驳回</span>
        </div>
        <div class="cont">
          <p>驳回原因：{{ item.reasons }}</p>
        </div>
      </div>

      <div class="ce_btn">
        <a
          class="p_button"
          v-show="
            rejectStatus === 4 || rejectStatus === 3 || rejectStatus === 2
          "
          @click.stop="rejectVideoQuery"
          >重新上传证件</a
        >
        <a
          class="p_button"
          v-show="rejectStatus === 1"
          @click.stop="setIdInRedis"
          >重新视频</a
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  setSidInRedis,
  regUnifiedWitness,
  syncWitness
} from '@/service/comServiceNew'
import '@/nativeShell/nativeCallH5'
import { callMessageNative } from 'thinkive-hvue'
import {
  queryDictionary,
  queryBusiness,
  goBackXcApp,
  registerNativeBack
} from '@/common/util'
export default {
  props: ['pageParam'],
  name: 'videoPage',
  data() {
    return {
      category: 'ismp.biz_type',
      rejectInfo: {
        flag: false,
        slideDown: true,
        dataList: []
      },
      pageStatus: 0, // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
      rejectReason: '',
      rejectStatus: 0, // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
      rejectTpye: '', // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
      anychatH5AppId: '',
      ocrUserInfo: $h.getSession('ocrUserInfo') || {},
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      bizType: '',
      businessCode: this.$route.query.type,
      businessName: '',
      rejectDataInfo: '',
      msgInfo: []
    }
  },
  computed: {
    videoTime() {
      return $hvue.config.videoTime
    }
  },
  async activated() {
    // window.androidAppBack = this.pageBack
    registerNativeBack({
      callback: this.pageBack
    })
    window.videoCallBack = this.videoCallBack
    window.imgCallBack = this.getImgCallBack
    this.rejectInfo = {
      flag: false,
      slideDown: true,
      dataList: []
    }
    this.pageStatus = 0 // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
    this.rejectReason = ''
    this.rejectStatus = 0 // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.rejectTpye = '' // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.anychatH5AppId = ''
    this.ocrUserInfo = $h.getSession('ocrUserInfo') || {}
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮

    queryBusiness(this.$route.query.type, data => {
      this.businessName = data.businessName || this.$route.query.name
    })
    // 查询视频biz_type数据字典
    queryDictionary({ type: this.category, value: this.businessCode }, data => {
      this.bizType = data.key
    })
    let infoData = this.pageParam[0]
    const data = await regUnifiedWitness({
      userName: infoData.userName,
      identityNum: infoData.identityNum,
      papersAddr: infoData.papersAddr,
      sex: infoData.sex,
      birthday: infoData.birthday,
      signOffice: infoData.signOffice,
      validityBegin: infoData.validityBegin,
      validityEnd: infoData.validityEnd,
      ethnicName: infoData.ethnicName,
      clientId: this.ygtUserInfo.clientId,
      serivalId: this.$parent.publicParam.serivalId,
      flow_name: this.businessCode
    })
    if (data.error_no == 0) {
      this.ygtUserInfo.token = data.registerVideoResult[0].token
      this.ygtUserInfo.regFlowNo = data.registerVideoResult[0].regFlowNo
      this.ygtUserInfo.bizType = data.registerVideoResult[0].bizType
      // $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })
    } else {
      _hvueAlert({ mes: data.error_info })
    }
  },
  methods: {
    // 返回
    pageBack() {
      _hvueConfirm({
        mes: '视频认证未完成，是否返回首页？',
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              registerNativeBack({
                callback: this.pageBack
              })
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 返回首页
              $hvue.env == 'ths'
                ? goBackXcApp()
                : this.$router.push({ name: 'index' })
            }
          }
        ]
      })
    },
    // 视频结束后原生回调H5方法
    videoCallBack(msg) {
      msg['videoFlag'] = decodeURI(msg['videoFlag'])
      // 兼容iOS，主动挂断时会进回调返回20000，不做驳回展示的处理
      if (msg['videoFlag'] === '20000') {
        return
      }
      let videoFlag = JSON.parse(msg['videoFlag'])
      let msgNo = +videoFlag.msgNo
      let msgInfo = videoFlag.msgInfo || []
      if (msgNo === 0) {
        this.pageStatus = 2
        this.nextStep()
      } else {
        this.pageStatus = 3
        this.rejectStatus = 1
        this.rejectTpye = '视频认证未通过'
        // 身份正面不合规 ：idcardFace
        // 身份证反面不合规：idcardBack
        // 身份证不合规：idcard
        // 视频驳回：witness
        let rejectContent = []
        for (let i = 0; i < msgInfo.length; i++) {
          let el = msgInfo[i]
          rejectContent.push(el.rejectContent)
          let step = el.step
          switch (step) {
            case 'idcardFace':
              this.rejectTpye = '肖像面认证未通过'
              this.rejectStatus = 3
              break
            case 'idcardBack':
              this.rejectTpye = '国徽面认证未通过'
              this.rejectStatus = 4
              break
            case 'idcard':
              this.rejectTpye = '身份证认证未通过'
              this.rejectStatus = 2
              break
            case 'witness':
              this.rejectTpye = '视频认证未通过'
              this.rejectStatus = 1
              break
            default:
              this.rejectStatus = 1
              this.rejectTpye = '视频认证未通过'
              break
          }
        }
        this.rejectReason = rejectContent.join(';')
      }
    },
    async witnessCallBack(data) {
      data = JSON.parse(data)
      if (data.code == '1') {
        // 成功
        //
        this.pageStatus = 2
        this.nextStep()
        /* const res = await syncWitness({
          regFlowNo: this.ygtUserInfo.regFlowNo
        })
        if (res.error_no == 0) {
          this.pageStatus = 2
          this.nextStep()
        } else {
          _hvueAlert({ mes: res.error_info })
        } */
      } else if (data.code == '0') {
        //
        // const errorInfo = data.errorInfo
        // this.pageStatus = 3
        // this.rejectStatus = 1
        // this.rejectTpye = '视频认证未通过'
        // this.rejectReason = errorInfo

        this.pageStatus = 3
        this.rejectStatus = 1
        this.rejectTpye = '视频认证未通过'
        let errorInfo = JSON.parse(data.errorInfo)
        let msgInfo = errorInfo.msgInfo || []
        this.rejectDataInfo = msgInfo
        let rejectContent = []
        let stepArray = []
        for (let i = 0; i < msgInfo.length; i++) {
          let el = msgInfo[i]
          rejectContent.push(el.reasons)
          let step = el.step
          stepArray.push(step)

          el.reasons = el.reasons.join(';')
          switch (step) {
            case 'g_id_front_path':
              el.rejectTpye = '身份证人像面'
              break
            case 'g_id_back_path':
              el.rejectTpye = '身份证国徽面'
              break
            case 'g_both_witness_video_url':
              el.rejectTpye = '视频见证'
              break
            case 'kh_witness':
              el.rejectTpye = '其他原因'
              break
            default:
              el.rejectTpye = '视频认证未通过'
              break
          }
        }
        this.msgInfo = msgInfo
        this.rejectReason = rejectContent.join(';')
        if (
          stepArray.includes('g_id_front_path') ||
          stepArray.includes('g_id_back_path')
        ) {
          this.rejectTpye = '身份证认证未通过'
          this.rejectStatus = 2
        }
      }
    },
    async setIdInRedis() {
      let infoData = this.pageParam[0]
      const data = await regUnifiedWitness({
        userName: infoData.userName,
        identityNum: infoData.identityNum,
        papersAddr: infoData.papersAddr,
        sex: infoData.sex,
        birthday: infoData.birthday,
        signOffice: infoData.signOffice,
        validityBegin: infoData.validityBegin,
        validityEnd: infoData.validityEnd,
        ethnicName: infoData.ethnicName,
        clientId: this.ygtUserInfo.clientId,
        serivalId: this.$parent.publicParam.serivalId,
        flow_name: this.businessCode
      })
      if (data.error_no == 0) {
        this.ygtUserInfo.token = data.registerVideoResult[0].token
        this.ygtUserInfo.regFlowNo = data.registerVideoResult[0].regFlowNo
        this.ygtUserInfo.bizType = data.registerVideoResult[0].bizType
        // $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })
      } else {
        _hvueAlert({ mes: data.error_info })
        return false
      }
      let param = {
        userId: this.ygtUserInfo.userId,
        serivalId: this.$parent.publicParam.serivalId,
        businessCode: this.businessCode
      }
      setSidInRedis(param)
        .then(data => {
          if (data.error_no === '0') {
            this.startVideo()
          } else {
            return Promise.reject(new Error(data.error_info))
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    startVideo() {
      let videoParam = {
        activity: 'activity',
        isShowChange: false,
        userId: this.ygtUserInfo.userId,
        custName: this.ygtUserInfo.name, // 名字
        orgNo: this.ygtUserInfo.branchNo,
        channelNo: 'ths',
        regFlowNo: this.ygtUserInfo.regFlowNo,
        token: this.ygtUserInfo.token,
        bizType: this.ygtUserInfo.bizType
      }

      invokeNative('8010', '000', videoParam, data => {
        console.log('视频回调结果', data)
        this.witnessCallBack(data)
      })

      // let url = process.env.NODE_ENV === 'development'
      //   ? SERVER_URL.YGT_NEW_SERVER
      //   : window.location.protocol + '//' + window.location.host + SERVER_URL.YGT_NEW_SERVER

      // let param = {
      //   userId: this.ygtUserInfo.userId, // 用户编号
      //   userName: `${this.ygtUserInfo.name}(${this.businessName})`, // 用户名称
      //   orgId: this.ygtUserInfo.branchNo, // 营业部编号
      //   bizType: this.bizType, // 业务类型
      //   netWorkStatus: 'WIFI', // 网络状态 (可不填
      //   url: url + '?', // 连接排队BUS的服务器地址 (ios必须要在url加上?不然无法识别
      //   moduleName: 'ygt',
      //   funcNo: '60005', // 双向视频见证
      //   // isNewView: "0",
      //   version: '4.0',
      //   appId: this.anychatH5AppId,
      //   userType: '1',
      //   // 兼容原生和统一视频版本
      //   isRejectToH5: '1', // 是否返回所有驳回参数
      //   user_id: this.ygtUserInfo.userId,
      //   user_name: this.ygtUserInfo.name,
      //   client_name: this.ygtUserInfo.name,
      //   branch_id: this.ygtUserInfo.branchNo,
      //   branchno: this.ygtUserInfo.branchNo,
      //   origin: $hvue.platform,
      //   user_type: '1',
      //   // jsession_id:$h.getSession("ygtUserInfo").jsessionid,
      //   videoType: $hvue.config.videoType, // 0:tchat  1:anychat
      //   business_id: this.$parent.publicParam.serivalId,
      //   business_code: this.businessCode,
      //   business_type: this.bizType,
      //   biz_type: this.bizType,
      //   client_id: this.ygtUserInfo.userId,
      //   // 兼容多站点的传参
      //   ext_param: JSON.stringify({'serviceTpye': 'service_type_' + $hvue.config.serviceType})
      //   // videoServerIP: $hvue.config.serviceTypeUrl.split(':')[0], // String 视频服务器ip N 4.0视频用；h5传了该参数视频ip就用h5传的，不再取排队bus的ip了
      //   // videoServerPort: $hvue.config.serviceTypeUrl.split(':')[1] // String 视频服务器端口 N 4.0视频用；h5传了该参数视频端口就用h5传的，不再取排队bus的端口了
      // }
      // let result = $h.callMessageNative(param)
      // if (result.error_no !== 0) {
      //   if (result === 'not_thinkive_native_sdk') {
      //     _hvueToast({ mes: '不在原生sdk环境中，无法调用' })
      //     return
      //   }
      //   _hvueToast({ mes: result.error_info })
      // }
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.ygtUserInfo.regFlowNo) {
          // 可以下一步
          resolve()
        } else {
          reject(new Error({ msg: 'regFlowNo不能为空' }))
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {
        regFlowNo: this.ygtUserInfo.regFlowNo
      } // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = {
        regFlowNo: this.ygtUserInfo.regFlowNo
      } // 需要保存的表单数据
      return formData
    },
    rejectVideoQuery() {
      // 判断是否是驳回流水
      if (this.$parent.flow.currentStepInfo.flow_reject === '1') {
        this.$parent.rejectFlow('uploadIdCard,oneOrTwoVideoPage')
        return
      }
      // 驳回后回滚到上传身份证步骤
      this.$parent.rollback('uploadIdCard')
    },
    // 调用父组件的下一步事件
    nextStep() {
      this.$parent.emitNextEvent()
    }
  }
}
</script>

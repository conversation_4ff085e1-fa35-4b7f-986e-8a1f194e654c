<template>
  <div>
    <headComponent headerTitle="办理结果"></headComponent>
    <div
      class="result_main"
      v-show="!(businessCode === 'xh' && formStatus === '4')"
    >
      <div
        :class="{
          icon_ing:
            (handleStatus === '0' && formStatus !== '4') || handleStatus === '',
          icon_ok:
            (formStatus === '2' || formStatus === '3') && handleStatus === '1',
          icon_error: formStatus === '4' || handleStatus === '2'
        }"
      ></div>
      <h5>{{ showDealDesc }}</h5>
      <p
        v-if="
          ((formStatus === '2' || formStatus === '3') &&
            handleStatus === '1') ||
            formStatus === '4' ||
            handleStatus === '2'
        "
        class="agray"
      >
        {{ resultDescription }}
      </p>
      <p class="agray" v-else v-html="getTips(businessCode)"></p>
    </div>
    <h5 class="com_title" v-if="formStatus === '4' && businessCode === 'xh'">
      您的以下信息被我司业务审核人员驳回，请您根据驳回原因重新提交相关业务资料。
    </h5>
    <!-- 视频或身份证驳回 -->
    <div v-if="formStatus === '4' && !isFormReject">
      <div class="reject_item" v-for="(item, index) in rejectList" :key="index">
        <div class="title">
          <h5>{{ item.rejectCode | fillRejectCode }}</h5>
          <span class="state error">已驳回</span>
        </div>
        <div class="cont">
          <p>驳回原因：{{ item.reason }}</p>
        </div>
      </div>
    </div>

    <!-- 表单驳回 -->
    <div v-if="isFormReject">
      <div v-for="(item, index) in rejectList" :key="index" class="fail_reason">
        <span>失败原因</span>
        <p @click.stop="showErrorInfo(item.reason)">
          {{ item.reason.substring(0, 8) }}...
        </p>
      </div>
    </div>
    <dl
      class="result_tips"
      v-if="
        businessCode === 'lryzx' && formStatus === '2' && handleStatus === '1'
      "
    >
      <dt>
        <span>提示</span>
      </dt>
      <dd>
        1. 请您持有效身份证明文件等相关资料亲临我公司营业部办理融资融券业务
      </dd>
      <dd>2. 请您确保临柜办理业务时您的账户20个交易日日均资产满50万元</dd>
      <dd>
        3. 本页面仅作为融资融券预约，不代表我公司已同意您的融资融券业务申请
      </dd>
    </dl>

    <div class="ce_btn mt20" v-if="formStatus === '4' && !isFormReject">
      <a class="p_button" @click.stop="reject">{{ rejectBtnName }}</a>
      <a class="p_button border" @click.stop="giveup">放弃办理</a>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { closeYgt } from '@/common/sso'
import {
  queryRejectReason,
  closeFlow,
  xhResubmit
} from '@/service/comServiceNew'
import { queryBusiness, goBackXcApp, registerNativeBack } from '@/common/util'

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data() {
    return {
      serivalId: this.$parent.publicParam.serivalId,
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      formStatus: '', // 表单状态 0:表单未提交完(暂存) 1:表单已提交完成 2:表单已处理 3:结束 4:驳回 5:主动结束办理 8:审核 9:待跑批(审核通过)
      handleStatus: '', // 表单处理状态  0:未处理 1:处理成功 2:处理失败
      businessName: '',
      businessCode: '',
      businessFlowInfo: '',
      rejectList: [], // 驳回信息
      rejectStepInfo: [],
      isFormReject: false, // 标记是否是表单驳回
      rejectBtnName: '' // 驳回按钮
    }
  },
  computed: {
    showDealDesc() {
      return this.getDealDesc(this.formStatus, this.handleStatus)
    },
    resultDescription() {
      const updateTime = this.businessFlowInfo.updateDate
      const resultText =
        this.formStatus === '4' || this.handleStatus === '2' ? '失败' : '成功'
      return `您于 ${updateTime} 办理${this.businessName}${resultText}`
    }
  },
  created() {
    queryBusiness(this.$route.query.type, data => {
      this.businessName = data.businessName || this.$route.query.name
    })
  },
  activated() {
    this.$store.commit('updateIsShowHead', false) // 隐藏head
    // window.phoneBackBtnCallBack = this.back
    console.log('businessFlowInfo - 注册返回方法')
    registerNativeBack({
      callback: window.$hvue.env === 'ths' ? goBackXcApp : this.goIndex
    })
    // window.androidAppBack = $hvue.env == 'ths' ? goBackXcApp : this.goIndex
    if (this.pageParam.length <= 0) {
      _hvueAlert({
        mes: '未查询到办理结果'
      })
    }
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮

    this.businessFlowInfo = this.pageParam[0]
    this.formStatus = this.businessFlowInfo.formStatus
    this.handleStatus = this.businessFlowInfo.handleStatus
    this.businessCode =
      this.businessFlowInfo.businessCode || this.$parent.flow.flowName
    this.$parent.flow.formStatus = this.formStatus
    this.$store.commit('updateHandleStatus', this.handleStatus) // handleStatus
    if (
      this.businessCode === 'xh' &&
      this.handleStatus === '0' &&
      this.formStatus !== '4'
    ) {
      _hvueConfirm({
        title: '重要提示',
        mes:
          '审核期间请不要操作账户，否则可能会导致销户失败，销户进度将会以短信方式进行告知',
        opts: [
          {
            txt: '我已知晓',
            color: true
          }
        ]
      })
    }
    if (this.formStatus === '4') {
      this.queryRejectReason()
    }
    // 办理成功时，先结束流程
    // if (this.handleStatus === '1' && this.formStatus === '2') {
    //   this.confirmEvent()
    // }
    // 通过vuebus接受businessResult组件的confirmEvent事件
    this.$bus.on('confirmEvent', () => {
      this.confirmEvent()
    })
    this.$bus.on('pageBack', () => {
      this.back()
    })
    // 通过vuebus接受businessResult组件的giveUp事件
    this.$bus.on('giveUp', () => {
      this.giveup()
    })
    // 通过vuebus接受businessResult组件的giveUp事件
    this.$bus.on('xhResubmit', () => {
      this.reSubmit()
    })
  },
  methods: {
    showErrorInfo(errorDesc) {
      _hvueAlert({
        title: '失败原因',
        mes: errorDesc
      })
    },
    getDealDesc(formStatus, handleStatus) {
      if (handleStatus === '0' && formStatus !== '4') {
        return '成功提交，处理中'
      }
      if (handleStatus === '1' && (formStatus === '2' || formStatus === '3')) {
        return '办理成功'
      }
      if (formStatus === '4') {
        return '审核驳回'
      }
      if (handleStatus === '2' && formStatus === '2') {
        return '办理失败'
      }
      return '成功提交，处理中'
    },
    getTips(code) {
      switch (code) {
        case 'cybzq':
          return '创业板转签开通资料已提交审核，结果将于2个工作日内以短信方式通知。'
        case 'dzqmyds':
          return `恭喜您，${this.businessName}签署成功。<br>办理时间：${this.pageParam[0].updateDate}`
        default:
          let tips =
            this.businessName +
            '业务申请已提交，请耐心等待处理完成后再进入业务查看结果'
          return tips
      }
    },
    // 点击确定按钮，直接返回首页
    confirmEvent() {
      if (this.$parent.flow.currentStepInfo.flow_finish === '0') {
        // 业务请求参数封装
        let params = {}
        // 表单已结束 结束业务流程 调用success的submit方法。调用父组件的提交方法。
        // this.$parent.next(params, { isLastReq: true })
        // _hvueAlert({
        //   mes: '调用流程结束接口',
        //   callback: () => {
        //     this.$parent.next({}, { isLastReq: true }).then(res => {
        //       // 返回首页
        //       $hvue.env == 'ths'
        //         ? goBackXcApp()
        //         : this.$router.push({ name: 'index' })
        //     })
        //   }
        // })
        this.$parent.next({}, { isLastReq: true }).then(res => {
          // 返回首页
          $hvue.env == 'ths'
            ? goBackXcApp()
            : this.$router.push({ name: 'index' })
        })
        return
      }
    },
    back() {
      if (
        this.$route.query.type === 'czmm' &&
        (this.formStatus === '2' && this.handleStatus === '1') &&
        (this.formStatus === '2' || this.formStatus === '3')
      ) {
        // 重置密码办理成功时登出
        this.logout()
      } else {
        $hvue.env == 'ths'
          ? goBackXcApp()
          : this.$router.push({ name: 'index' })
      }
    },
    giveup() {
      // 放弃办理时 若pageFlow未结束 则先将pageFlow结束掉
      // if(this.$parent.flow.currentStepInfo.flow_finish ==='0' && this.$parent.flow.currentStepInfo.flow_current_step_name=='success'){
      //   this.$parent.next();
      // }
      // 放弃办理
      closeFlow({
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        businessCode: this.$route.query.type,
        serivalId: this.serivalId
      }).then(res => {
        if (res.error_no === '0') {
          $hvue.env == 'ths'
            ? goBackXcApp()
            : this.$router.push({ name: 'index' })
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info
          })
        }
      })
    },

    reject() {
      // 步骤名称为空时，结束流程，直接返回首页
      if (!this.rejectStepInfo.stepName) {
        // 返回首页
        this.$parent.next({}, { isLastReq: true }).then(res => {
          // 返回首页
          this.$router.push({ name: 'index' })
        })
        return
      }
      if (this.$parent.flow.currentStepInfo.flow_finish === '1') {
        // 如果流程已经结束 则走驳回
        this.$parent.rejectFlow(this.rejectStepInfo.stepName)
      } else {
        // 如果未结束，先结束，再驳回
        this.$parent.next().then(res => {
          this.$parent.rejectFlow(this.rejectStepInfo.stepName)
        })
      }
    },
    // 查询驳回原因
    queryRejectReason() {
      queryRejectReason({
        serivalId: this.serivalId
      }).then(data => {
        if (data.error_no === '0') {
          // 定义优先级顺序
          const priorityOrder = {
            uploadIdCard: 1,
            videoType: 2 // 所有视频类型统一使用一个优先级
          }

          // 判断是否为视频类型的函数
          const isVideoType = name => {
            const videoTypes = [
              'oneOrTwoVideoPage',
              'oneWayVideoPage',
              'videoPage',
              'video'
            ]
            return videoTypes.some(type => name.indexOf(type) > -1)
          }

          // 处理 rejectInfo 数组，合并相同 stepNo 的项
          if (data.rejectInfo && data.rejectInfo.length > 0) {
            const stepNoMap = {}
            data.rejectInfo.forEach(item => {
              let stepNo = item.stepNo || item.rejectCode
              stepNo = isVideoType(stepNo) ? 'videoPage' : stepNo
              if (!stepNo) return

              if (!stepNoMap[stepNo]) {
                stepNoMap[stepNo] = { ...item }
              } else if (
                item.reason &&
                stepNoMap[stepNo].reason !== item.reason
              ) {
                // 合并不同的 reason
                stepNoMap[stepNo].reason =
                  stepNoMap[stepNo].reason + '，' + item.reason
              }
            })

            // 转换回数组
            this.rejectList = Object.values(stepNoMap)
            // 按优先级排序
            this.rejectList.sort((a, b) => {
              const getPriority = ({ stepNo, rejectCode }) => {
                const s = stepNo || rejectCode || ''

                // 所有视频类型统一处理
                if (isVideoType(s)) {
                  return priorityOrder.videoType
                }

                // 身份证上传类型
                if (s.indexOf('uploadIdCard') > -1) {
                  return priorityOrder.uploadIdCard
                }

                return 999 // 其他类型放最后
              }

              return getPriority(a) - getPriority(b)
            })
          } else {
            this.rejectList = []
          }

          // 处理 rejectStepInfo 数组
          if (data.rejectStepInfo && data.rejectStepInfo.length > 0) {
            // 获取 stepName 字符串并拆分
            let stepNameStr = data.rejectStepInfo[0].stepName || ''
            let stepNames = stepNameStr.split(',')

            // 按优先级排序
            stepNames.sort((a, b) => {
              const getPriority = name => {
                // 所有视频类型统一处理
                if (isVideoType(name)) {
                  return priorityOrder.videoType
                }

                // 身份证上传类型
                if (name.indexOf('uploadIdCard') > -1) {
                  return priorityOrder.uploadIdCard
                }

                return 999 // 其他类型放最后
              }

              return getPriority(a) - getPriority(b)
            })

            // 重新组合 stepName
            data.rejectStepInfo[0].stepName = stepNames.join(',')
            this.rejectStepInfo = data.rejectStepInfo[0]
          } else {
            this.rejectStepInfo = { stepName: '' }
          }

          // 设置驳回按钮名称
          if (this.rejectStepInfo.stepName) {
            if (this.rejectStepInfo.stepName.indexOf('uploadIdCard') > -1) {
              this.rejectBtnName = '重新上传证件'
            } else if (isVideoType(this.rejectStepInfo.stepName)) {
              // 所有视频类型统一处理为"重新视频"
              this.rejectBtnName = '重新视频'
            } else {
              // 表单审核驳回--视频和身份证之外的资料被驳回时（暂时两融有这种情况）
              this.rejectStepInfo.stepName = ''
              // 列出驳回列表
              this.rejectBtnName = '返回首页'
              this.isFormReject = true
            }
          } else {
            // 没有有效的 stepName
            this.rejectStepInfo.stepName = ''
            this.rejectBtnName = '返回首页'
            this.isFormReject = true
          }

          $h.setSession('rejectList', this.rejectList) // 保存驳回信息
        } else {
          _hvueAlert({
            mes: data.error_info
          })
        }
      })
    },
    reSubmit() {
      // 销户重新提交
      xhResubmit({
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        businessCode: this.$route.query.type,
        serivalId: this.$parent.publicParam.serivalId
      }).then(res => {
        if (res.error_no === '0') {
          $hvue.env == 'ths'
            ? goBackXcApp()
            : this.$router.push({ name: 'index' })
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info
          })
        }
      })
    },
    // 退出登录
    logout() {
      // h5访问时退出登录
      if ($hvue.platform === '0') {
        $h.clearSession('ygtUserInfo')
        $h.clearSession('isLogin')
        $h.clearSession('ssoInfo')
        $h.clearSession('passwordType')
        this.$router.replace({ name: 'login', params: {} })
      } else {
        closeYgt(1, '1A', 0, 1) // 退出网厅
      }
    },
    goIndex() {
      this.$router.push({ name: 'index' })
    },
    goBack() {
      let backToApp = $h.getSession('backToApp')
      try {
        if (backToApp == '1') {
          invokeNative('8002', '000', {}, data => {})
        } else {
          window.history.back()
        }
      } catch (e) {
        window.history.back()
      }
    }
  },
  destroyed() {
    this.$store.commit('updateBusinessNextBtnStatus', true)
    this.$store.commit('updateIsShowHead', true)
    this.$bus.off('pageBack') // 事件销毁，防止多次触发
    this.$bus.off('giveUp') // 事件销毁，防止多次触发
    this.$bus.off('xhResubmit') // 事件销毁，防止多次触发
    this.$bus.off('confirmEvent') // 事件销毁，防止多次触发
    $h.clearSession('agreementIds')
  }
}
</script>

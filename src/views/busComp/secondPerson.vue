<template>
  <div class="user_main">
    <div v-show="!showSelBox" class="input_form">
      <div class="ui field text">
        <label class="ui label">姓名</label>
        <input type="text" class="ui input" placeholder="输入姓名" v-model.trim="name" maxlength="20" />
        <a class="txt_close" @click.stop="name=''" v-show="name!=''"></a>
      </div>
      <div class="ui field text">
        <label class="ui label">关系</label>
        <div class="ui dropdown" @click.stop="showSelBoxClick">
          <strong>{{relation}}</strong>
        </div>
      </div>
      <div class="ui field text">
        <label class="ui label">手机号码</label>
        <input
          type="text"
          class="ui input"
          placeholder="输入手机号"
          v-model.trim="mobile"
          maxlength="11"
        />
        <a class="txt_close" @click.stop="mobile=''" v-show="mobile!=''"></a>
      </div>
    </div>
    <selBox
      v-if="showSelBox"
      v-model="showSelBox"
      title="关系"
      category="ismp.relation"
      :defaultStr="relation"
      @selCallback="selCallback"
    ></selBox>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import selBox from '@/components/selBox' // 选择器
import { queryDictionary } from '@/common/util'
export default {
  name: 'secRelation',
  props: ['pageParam'],
  components: {
    headComponent,
    selBox
  },
  data () {
    return {
      category: 'ismp.relation',
      showSelBox: false,
      mobile: '',
      relation: '请选择社会关系',
      name: '',
      relationKey: ''
    }
  },
  activated () {
    this.name = this.pageParam[0].secRelationName
    this.mobile = this.pageParam[0].secRelationPhone
    let that = this
    if (this.pageParam[0].secRelationType) {
      queryDictionary(
        {
          type: 'ismp.relation',
          key: that.pageParam[0].secRelationType
        },
        function (d) {
          that.relation = d.value
          that.relationKey = d.key
        }
      )
    }
  },
  methods: {
    // 校验格式
    verification () {
      if ($h.isEmptyString(this.name)) {
        _hvueToast({ mes: '请输入姓名' })
        return false
      }
      if (!/^[\u4E00-\u9FA5]{2,14}([·•]?[\u4E00-\u9FA5]+)*$/.test(this.name)) {
        _hvueToast({ mes: '姓名格式不正确' })
        return false
      }
      if ($h.isEmptyString(this.relationKey)) {
        _hvueToast({ mes: '请选择社会关系' })
        return false
      }
      if ($h.isEmptyString(this.mobile)) {
        _hvueToast({ mes: '请输入手机号码' })
        return false
      }
      if (!/^1\d{10}$/.test(this.mobile)) {
        _hvueToast({ mes: '手机号码格式不正确' })
        return false
      }
      if (this.mobile === this.pageParam[0].mobile) {
        _hvueToast({ mes: '手机号码不能是本人手机号码' })
        return false
      }
      return true
    },
    showSelBoxClick () {
      // 查询关系
      this.showSelBox = !this.showSelBox
    },
    selCallback (a) {
      this.showSelBox = false
      if (!a) {
        return
      }
      this.relationKey = a.data.key
      this.relation = a.data.value
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.verification()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        secRelationName: this.name,
        secRelationType: this.relationKey,
        secRelationPhone: this.mobile
      }
      return params
    },
    putFormData () {
      let formData = {
        secondLinkPerson: {
          secRelationName: this.name,
          secRelationType: this.relationKey,
          secRelationPhone: this.mobile
        }
      }
      return formData
    }
  }
}
</script>

<style>
</style>

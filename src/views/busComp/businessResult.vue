<!-- 结果页组件 -->
<template>
  <div v-show="formStatus !== '4'">
    <!--  账户类业务展示的结果列表界面  -->
    <template v-if="busiType === 1">
      <template v-if="businessCode === 'xh'">
        <div
          class="result_itemwrap"
          v-for="(typeItem, typeIndex) in accountTypeList"
          :key="typeIndex"
        >
          <h5 class="title">{{ typeItem }}</h5>
          <div class="cont">
            <ul class="result_info">
              <li
                v-for="(item, index) in accountResultList"
                v-show="item.isShow && item.accountType === typeItem"
                :key="index"
              >
                <p v-if="item.views">{{ item.views }}</p>
                <div class="ct">
                  <span
                    class="state"
                    :class="item.dealBusinessResultState | fillStateStyle"
                    >{{ item.dealBusinessResultState | fillStateDesc
                    }}<i
                      v-if="item.dealBusinessResultState === '2'"
                      class="arrow"
                      @click.stop="showErrorInfo(item.dealBusinessDesc)"
                    ></i
                  ></span>
                </div>

                <!-- <em :class="item.dealBusinessResultState | fillStateStyle">
                {{ item.dealBusinessResultState | fillStateDesc }}
              </em>
              <div v-if="item.dealBusinessResultState === '2'" class="fail_reason">
                <span>失败原因</span>
                <p
                  @click.stop="showErrorInfo(item.dealBusinessDesc)"
                >{{ item.dealBusinessDesc.substring(0,8) }}...</p>
              </div> -->
              </li>
            </ul>
          </div>
        </div>
      </template>
      <div v-else class="result_sfinfo">
        <ul>
          <li
            v-for="(item, index) in accountResultList"
            v-show="item.isShow"
            :key="index"
          >
            <span v-if="item.views" class="tit">{{ item.views }}</span>
            <p v-if="item.views">
              <em
                :class="item.dealBusinessResultState | fillStateStyle"
                class="status"
                >{{ item.dealBusinessResultState | fillStateDesc }}</em
              >
            </p>
            <div
              v-if="
                item.dealBusinessResultState === '2' && item.dealBusinessDesc
              "
              class="fail_reason"
            >
              <span>失败原因</span>
              <p @click.stop="showErrorInfo(item.dealBusinessDesc)">
                {{ item.dealBusinessDesc.substring(0, 8) }}...
              </p>
            </div>
          </li>
        </ul>
      </div>
    </template>
    <!--  非账户类业务，办理失败时展示的失败原因界面  -->
    <template v-else-if="busiType === 2">
      <div class="result_fail_reason">
        <div
          v-if="
            pageParam[0].dealBusinessResultState === '2' &&
              pageParam[0].dealBusinessDesc
          "
          class="fail_reason"
        >
          <span>失败原因</span>
          <p @click.stop="showErrorInfo(pageParam[0].dealBusinessDesc)">
            {{ pageParam[0].dealBusinessDesc }}
          </p>
        </div>
      </div>
    </template>
    <div class="ce_btn">
      <!-- <a v-if="handleStatus === '2' && businessCode === 'xh'"
        class="p_button"
        @click.stop="reSubmit"
      >重新提交</a> -->
      <a
        v-if="
          (handleStatus === '2' && formStatus !== '5') || formStatus === '4'
        "
        class="p_button border"
        @click.stop="giveup"
        >放弃办理</a
      >
      <a
        v-else-if="handleStatus === '1' && formStatus === '2'"
        class="p_button"
        @click.stop="confirmEvent"
        >返回首页</a
      >
      <a v-else class="p_button" @click.stop="pageBack">返回首页</a>
    </div>
  </div>
</template>

<script>
import { registerNativeBack } from '@/common/util'

export default {
  name: 'businessResult',
  props: {
    pageParam: {
      type: Array
    }
  },
  data() {
    return {
      businessCode: this.$parent.flowName,
      accountResultList: [],
      accountTypeList: [],
      busiType: 1 // 1账户类业务 2其他业务
    }
  },
  computed: {
    formStatus() {
      return this.$parent.flow.formStatus
    },
    handleStatus() {
      return this.$store.state.handleStatus
    }
  },
  activated() {
    registerNativeBack({
      callback: this.pageBack
    })
    $h.clearSession('ocrUserInfo')
    this.$store.commit('updateIsShowHead', false) // 隐藏head
    if (this.pageParam.length > 0) {
      this.accountResultList = this.pageParam
      for (let s = 0; s < this.accountResultList.length; s++) {
        let element = this.accountResultList[s]
        if (element.viewFields === undefined) {
          // 非权限和开通类业务没有账户数据，只在业务办理失败的时候展示一个失败原因
          this.busiType = 2
          if (element.dealBusinessResultState === '2') {
            element.isShow = true
          } else {
            element.isShow = false
          }
        } else {
          // 权限和开通类业务有账户数据，需要展示结果列表的
          this.busiType = 1
          element.isShow = true
          element.viewFields = JSON.parse(element.viewFields)
          element.views = ''
          for (let i = 0; i < element.viewFields.length; i++) {
            let v = element.viewFields[i]
            // 是否是数据字典类型
            if (v.isEnumFlag === '1') {
              // 不是数据字典值直接展示value
              element.views += v.value + ' '
            } else {
              // 是数据字典值展示enumValue
              element.views += v.enumValue + ' '
            }
          }
          if (this.businessCode === 'xh') {
            const accountInfo = element.views.split(' ')
            const accountType = element.viewFields.filter(item => {
              return item.filed === 'accountType'
            })
            element.accountType = accountType[0]
              ? accountType[0].enumValue
              : accountInfo[0]
            element.views = element.views
              .replace(element.accountType, '')
              .trim()
            this.accountTypeList.push(element.accountType)
          }
        }
        this.$set(this.accountResultList, s, element)
      }
      if (this.accountTypeList[0]) {
        // 账户类型列表去重及排序
        this.accountTypeList = [...new Set(this.accountTypeList)].sort(
          (a, b) => {
            return a === '客户号'
              ? -1
              : b === '客户号'
              ? 1
              : a === '资金账户'
              ? -1
              : b === '资金账户'
              ? 1
              : 0
          }
        )
      }
    } else {
      _hvueAlert({
        mes: '未查询到办理结果'
      })
    }
    if (this.$parent.businessName === '修改手机号' && this.formStatus !== '3') {
      let ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false })
      let newMobile = $h.getSession('newMobile')
      ygtUserInfo.mobile = newMobile
      $h.setSession('ygtUserInfo', ygtUserInfo, { encrypt: false })
      $h.clearSession('newMobile')
    }
  },
  methods: {
    confirmEvent() {
      this.$bus.emit('confirmEvent') // 通过vuebus调用businessFlowInfo组件pageBack事件
    },
    pageBack() {
      this.$bus.emit('pageBack') // 通过vuebus调用businessFlowInfo组件pageBack事件
    },
    giveup() {
      // 放弃办理
      this.$bus.emit('giveUp') // 通过vuebus调用businessFlowInfo组件giveUp事件
    },
    reSubmit() {
      this.$bus.emit('xhResubmit') // 通过vuebus调用businessFlowInfo组件xhResubmit事件
    },
    showErrorInfo(info) {
      _hvueAlert({
        title: '失败原因',
        mes: info
      })
    }
  }
}
</script>
<style scoped>
.result_fail_reason {
  margin: .2rem;
}
</style>

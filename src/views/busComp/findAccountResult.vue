<!-- 找回账户-结果页 -->
<template>
  <div>
    <div class="recover_result">
      <dl>
        <dt>您的账号为</dt>
        <dd>
          <a class="fr" href="javascript:;" @click.stop="copyAccount">复制</a>
          <strong ref="accountShow">{{ account }}</strong>
        </dd>
      </dl>
      <p class="mt15">为防止账户再次丢失，请您将账号进行记录保存</p>
    </div>
    <p class="bot_tips mt20">
      温馨提示：您的账号已通过短信发送至您的预留手机，请留意查收
    </p>
    <div class="ce_btn mt30">
      <a
        class="ui button block rounded"
        href="javascript:;"
        @click.stop="goLogin"
        >立即前往登录</a
      >
    </div>
  </div>
</template>
<script>
import { function60350 } from 'h5CallNative'

export default {
  props: ['pageParam'],
  data() {
    return {}
  },
  computed: {
    ygtUserInfo() {
      return $h.getSession('ygtUserInfo', { decrypt: false }) || {}
    },
    account() {
      return this.ygtUserInfo.clientId || ''
    }
  },
  mounted() {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  methods: {
    /** ************子组件公共方法定义****** */
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData() {
      let formData = {}
      return formData
    },
    /** ************ 子组件公共方法定义 ***** **/
    // 复制账号
    copyAccount() {
      this.$copyText(this.account).then(
        () => {
          _hvueToast({
            timeout: 1000,
            mes: '复制成功'
          })
        },
        e => {
          console.warn(e)
          _hvueToast({
            timeout: 1000,
            mes: '复制失败'
          })
        }
      )
    },
    // 去登录
    goLogin() {
      if ($hvue.platform === '0') { // 浏览器登录
        this.$router.replace({ name: 'login' })
        return
      }

      // 唤起原生登录页
      function60350()
      this.$router.go(-1)
    }
  }
}
</script>

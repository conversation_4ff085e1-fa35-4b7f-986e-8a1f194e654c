<template>
  <div>
    <div class="notice_box" v-show="!showProtocolParam.isShow">
      <div class="pic">
        <img :src="imageSrc" />
      </div>
      <h5>{{ `您已签署了${pageParam[0].agreeName}，无需重复签署` }}</h5>
      <p style="margin-top:0.1rem;">您可查看<a href="javascript:;"
        @click="toggleAgreement(true)">{{ `《${pageParam[0].agreeName}》` }}</a></p>
      <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="pageBack">返回首页</a>
      </div>
    </div>

    <showProtocol
      v-if="showProtocolParam.isShow"
      v-model="showProtocolParam.isShow"
      :agreementId="showProtocolParam.agreementId"
      btnDesc="确定"
      @showProtocolCall="toggleAgreement(false)"
    ></showProtocol>
  </div>
</template>
<script>
import showProtocol from '@/components/showProtocol' // 协议详情

export default {
  props: ['pageParam'],
  components: { showProtocol },
  data() {
    return {
      title: '提示',
      message: '',
      imageSrc: require('../../assets/images/not_ic04.png'),
      showProtocolParam: {
        isShow: false,
        agreementId: ''
      }
    }
  },
  activated() {
    this.title = this.$route.params.title
      ? this.$route.params.title
      : this.title
    this.message = this.$route.params.message
      ? this.$route.params.message
      : this.message
    this.imageSrc = this.$route.params.imageSrc
      ? this.$route.params.imageSrc
      : this.imageSrc

    this.showProtocolParam.isShow = false
    this.showProtocolParam.agreementId = this.pageParam[0].agreementId || ''
    this.$store.commit('updateBusinessNextBtnStatus', false)
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {} // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = {} // 需要保存的表单数据
      return formData
    },
    toggleAgreement(flag) {
      this.$set(this.showProtocolParam, 'isShow', flag)
    },
    pageBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped>
.kcb_cebtn {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99999;
}
</style>

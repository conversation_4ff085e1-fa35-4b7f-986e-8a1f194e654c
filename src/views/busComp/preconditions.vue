<template>
  <div>
    <div class="content">
      <h5 v-if="!totalFlag" class="com_title">以下条件尚未满足，请先满足</h5>
      <h5 v-else class="com_title">以下条件已满足，可继续办理</h5>
      <ul class="cond_list">
        <li
          v-for="(item, index) in conditionList"
          :key="index"
          :class="item.className(item.value)"
        >
          <div class="tit">
            <p>{{ item.getText(item.value) }}</p>
            <a
              v-show="item.value"
              class="right_link"
              @click.stop="goFix(index, item.fixToParam)"
              >{{ item.fixLinkText }}</a
            >
            <!-- <span v-show="item.errorText" class="tips">{{
              item.errorText
            }}</span> -->
          </div>
        </li>
      </ul>
    </div>
    <div class="hint">
      <p>
        如您已开基金账户，我司需向基金公司同步您的最新个人信息、证件信息，在基金公司完成更新前（预计2个交易日），您将无法办理销户业务。
      </p>
      <p>请在信息更新成功后2个交易日再办理销户。</p>
    </div>
    <div class="cond_tips" v-show="!totalFlag">
      <p>抱歉，您以上条件尚未满足，暂无法办理</p>
    </div>
    <!-- <div class="ce_btn mt20">
        <a
          @click.stop="doCheck"
          class="ui button block rounded"
          :class="totalFlag ? '' : 'border'"
        >{{ totalFlag ? '下一步' : '返回'}}</a>
    </div>-->
  </div>
</template>

<script>
import { closeYgt } from '@/common/sso'
import { registerNativeBack, goBackXcApp } from '@/common/util'
export default {
  props: ['pageParam'],
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      conditionList: {
        validityFlag: {
          // 身份证有效期
          isShow: true,
          value: false,
          getText: function(value) {
            return value ? '证件信息未更新、不完整或已过期' : '证件信息有效'
          },
          className: function(value) {
            return value ? 'error' : 'ok'
          },
          fixLinkText: '更新身份证',
          // fixToParam: { name: 'business', query: { type: 'sfzgx', name: '身份证更新', isBreakPoint: '1' } }
          fixToParam: 'www.baidu.com'
        },
        userDataFlag: {
          // 基本资料
          isShow: true,
          value: false,
          getText: function(value) {
            // return `基本信息${value ? '不' : ''}完整`
            return value ? '基本信息不完整' : '基本信息完整'
          },
          className: function(value) {
            return value ? 'error' : 'ok'
          },
          fixLinkText: '去完善',
          // fixToParam: { name: 'business', query: { type: 'zlxg' } }
          fixToParam: 'www.baidu.com'
        },
        // signFlag: {
        //   // 电子签名约定书
        //   isShow: true,
        //   value: false,
        //   getText: function(value) {
        //     return `电子签名约定书${value ? '未' : '已'}签订`
        //   },
        //   className: function(value) {
        //     return value ? 'error' : 'ok'
        //   },
        //   fixLinkText: '去签署',
        //   // fixToParam: { name: 'business', query: { type: 'dzqmyds' } }
        //   fixToParam: 'www.baidu.com'
        // },
        riskFlag: {
          // 风险测评
          isShow: true,
          value: false,
          getText: function(value) {
            // return `风险测评${value ? '已' : '未'}过期`
            return value ? '风险测评未做或已过期' : '风险测评有效'
          },
          className: function(value) {
            return value ? 'error' : 'ok'
          },
          fixLinkText: '立即更新',
          // fixToParam: { name: 'business', query: { type: 'fxcp' } },
          fixToParam: 'www.baidu.com',
          errorText: '您的风险测评等级结果已发生改变，请前往查看确认'
        }
      },
      showPage: false
    }
  },
  computed: {
    totalFlag() {
      let str = JSON.stringify(this.pageParam[0])
      if (str.indexOf('0') > -1) {
        // 有为0的情况不满足
        return false
      }
      return true
    }
  },
  created() {
    // this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  activated() {
    console.log('activated 注册返回方法')
    registerNativeBack({
      callback: this.$parent.back
    })
    // 缓存路由历史长度
    $h.setSession('historyLength', history.length)
    let conditionCheckResult = this.pageParam[0]
    let hasUnfulfilledCondition = false // 是否有不满足的条件
    this.showPage = false
    for (let item in this.conditionList) {
      if (!conditionCheckResult[item]) {
        this.conditionList[item].isShow = false
        continue
      }

      this.conditionList[item].value = conditionCheckResult[item] === '0'
      // this.conditionList[item].value = false
      this.conditionList[item].isShow = this.conditionList[item].value
      if (this.conditionList[item].value) {
        hasUnfulfilledCondition = true
      }
    }
    // this.getOldWtUrl()
    if (hasUnfulfilledCondition) {
      this.showPage = true
      this.$store.commit('updateBusinessNextBtnStatus', '返回首页') // 显示下一步按钮
      return
    }
    // if ($hvue.env == 'ths') {
    //   invokeNative('8011', '000', {}, data => {
    //     console.log('8011准入回调结果', data)
    //   })
    // }
    this.$store.commit('updateNextBtnText', '下一步')
    // this.$parent.emitNextEvent()
  },
  methods: {
    // 获取网厅2.0业务地址
    // getOldWtUrl() {
    //   const allBusiness = $h.getSession('allBusiness') || []
    //   const businessMap = {
    //     validityFlag: 'sfzgx',
    //     userDataFlag: 'zlxg',
    //     signFlag: 'dzqmyds',
    //     riskFlag: 'fxcp'
    //   }

    //   allBusiness.forEach(item => {
    //     for (let i in this.conditionList) {
    //       if (!this.conditionList[i].fixToParam && businessMap[i] === item.businessCode) {
    //         this.$set(this.conditionList[i], 'fixToParam', item.mobileBusinessUrl)
    //       }
    //     }
    //   })
    // },
    // 去修复前置条件
    goFix(index, param) {
      if (!this.checkPreconditionStatus(index)) {
        _hvueAlert({
          title: '提示',
          mes: '请您按照从上到下的顺序依次完善相关条件'
        })
        return
      }

      if (param) {
        // this.$router.push(param)
        // param = $hvue.config.dwWtUrl+$h.getSession('gsfyParam')
        // window.location.replace(param)
        $hvue.env == 'ths'
          ? goBackXcApp()
          : this.$router.push({ name: 'index' })
      } else {
        // this.jumpToWt2($hvue.config.wt2IndexUrl)
        _hvueAlert({
          title: '提示',
          mes: '该功能暂不可用'
        })
      }
    },
    // 检查修复顺序
    checkPreconditionStatus(index) {
      const keys = Object.keys(this.conditionList)
      let flag = true
      // console.log(keys, index)
      for (let i = 0; i < keys.length; i++) {
        if (keys[i] === index) {
          break
        }
        flag = flag && !this.conditionList[keys[i]].value
      }
      return flag
    },
    // 跳转至2.0业务
    jumpToWt2(url) {
      if (url.includes('http')) {
        if ($hvue.platform === '0') {
          // 非APP渠道拦截
          _hvueAlert({
            title: '错误提示',
            mes: '当前渠道不支持该业务'
          })
          return
        }

        const wt3Url = window.location.href
        const ssoInfo = $h.getSession('ssoInfo', { decrypt: true }) || {}
        // ssoInfo.ssoToken = 'f29f680a-939b-49ae-8b19-3ac496f5e08c'
        console.log('ssoInfo', ssoInfo)
        if (
          ssoInfo &&
          ssoInfo.params &&
          ssoInfo.params.user_token &&
          ssoInfo.params.mobilecode &&
          ssoInfo.params.appid
        ) {
          const ssoToken = encodeURIComponent(ssoInfo.params.user_token)
          const mobilecode = encodeURIComponent(ssoInfo.params.mobilecode)
          const appid = encodeURIComponent(ssoInfo.params.appid)
          // url = url.replace('https://test-xsgt.test.zszq.net', 'http://192.168.40.231:8081')
          // url = 'http://192.168.40.231:8081/m/ygt/index.html#!/convertibleBond/conditionCheck.html'
          if (url.includes('?')) {
            window.location.replace(
              url.replace(
                /\?(.*)#!/,
                `?$1&isFromwt3=${wt3Url}&isPrecondition=1&token=${ssoToken}&mobile=${mobilecode}&appID=${appid}&fundAccount=${this.ygtUserInfo.clientId}#!`
              )
            )
            return
          }
          window.location.replace(
            url.replace(
              '#!',
              `?isFromwt3=${wt3Url}&isPrecondition=1&token=${ssoToken}&mobile=${mobilecode}&appID=${appid}&fundAccount=${this.ygtUserInfo.clientId}#!`
            )
          )
        } else {
          _hvueAlert({
            title: '错误提示',
            mes: '无法获取统一登录信息，请重新登录',
            callback: () => {
              this.logout()
            }
          })
        }
      }
    },
    // 退出登录
    logout() {
      // h5访问时退出登录
      if ($hvue.platform === '0') {
        $h.clearSession('ygtUserInfo')
        $h.clearSession('isLogin')
        $h.clearSession('ssoInfo')
        $h.clearSession('passwordType')
        this.$router.replace({ name: 'login' })
      } else {
        closeYgt(0, '1A', 0, 1) // 退出网厅
      }
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 开发环境不做拦截 || process.env.NODE_ENV == "development"
        if (this.totalFlag) {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          // this.$router.push({ name: 'index' })
          $hvue.env == 'ths'
            ? goBackXcApp()
            : this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: ''
      }
      return params
    },
    putFormData() {
      let formData = {}
      return formData
    }
  }
}
</script>
<style scoped>
.cond_list li.error .tit p {
  padding-right: 0.92rem;
}
div.hint {
  margin: 0 0.1rem;
  background-color: #fff8f2;
  padding: 0.1rem;
}
div.hint > p {
  color: #da8a5c;
  font-weight: 500;
}
</style>

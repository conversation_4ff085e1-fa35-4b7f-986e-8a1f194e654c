<template>
  <div v-cloak>
    <div class="cond_box">
      <h5 class="title">办理融资融券预约，需要满足以下条件</h5>
      <ul>
        <li :class="{ok : assetFlag=='1',error:assetFlag == '0'}">
          <div class="tit">
            <p>20日日均资产50万</p>
          </div>
        </li>
        <li :class="{ok : tradeTimeFlag=='1',error:tradeTimeFlag == '0'}">
          <div class="tit">
            <p>交易年限大于等于6个月</p>
          </div>
        </li>
        <li :class="{ok : ageFlag=='1',error:ageFlag == '0'}">
          <div class="tit">
            <p>年龄小于70岁</p>
          </div>
        </li>
        <li :class="{ok : riskFlag=='1',error:riskFlag == '0'}">
          <div class="tit">
            <p>风险等级不为保守型</p>
            <a class="link" @click.stop="toRisk" v-show="riskFlag == '0'">重新测评</a>
          </div>
        </li>
        <li :class="{ok : breakFlag=='1',error:breakFlag == '0'}">
          <div class="tit">
            <p>未在违约黑名单客户</p>
          </div>
        </li>
      </ul>
    </div>
    <div class="cond_tips">
      <p>{{errorDesc}}</p>
    </div>
  </div>
</template>
<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      ageFlag: "0",
      breakFlag: "0",
      riskFlag: "0",
      assetFlag: "0",
      tradeTimeFlag: "0",
      errorDesc: "",
      nextFlag: false
    };
  },
  activated() {
    this.ageFlag = this.pageParam[0].ageFlag;
    this.breakFlag = this.pageParam[0].breakFlag;
    this.riskFlag = this.pageParam[0].riskFlag;
    this.tradeTimeFlag = this.pageParam[0].tradeTimeFlag;
    this.assetFlag = this.pageParam[0].assetFlag;

    let str = JSON.stringify(this.pageParam[0]);
    if (str.indexOf("0") != -1) {
      this.nextFlag = false;
      this.$store.commit("updateNextBtnText", "返回首页");
      this.errorDesc = "抱歉，您不满足基本条件，暂时不能办理";
    } else {
      this.nextFlag = true;
      this.$store.commit("updateNextBtnText", "下一步");
      this.errorDesc = "恭喜您，满足全部基本条件";
    }
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag || process.env.NODE_ENV == "development") {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        param1: "",
        param2: ""
      };
      return params;
    },
    putFormData() {
      let formData = {};
      return formData;
    },
    // 跳转到风险测评
    toRisk() {
      this.$router.push({
        name: "business",
        query: { type: "fxcp", name: "风险测评" }
      });
    }
  }
};
</script>

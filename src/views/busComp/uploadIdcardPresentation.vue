<template>
  <div class="notice_box">
    <div class="pic">
      <img src="@/assets/images/not_ic01.png" />
    </div>
    <h5>
      {{businessName}}需要拍摄身份证
      <br />请做好拍摄准备
    </h5>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      businessName: this.$parent.businessName // 业务中文名称
    }
  },
  activated () {
    this.$store.commit('updateNextBtnText', '开始拍摄') // 修改下一步按钮
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = {} // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {} // 需要保存的表单数据
      return formData
    }
  }
}
</script>

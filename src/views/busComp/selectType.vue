<template>
  <div>
    <div class="user_main">
      <h5 class="com_title">可选择一个密码单独修改，也可选择两个密码统一修改</h5>
      <ul class="type_selectlist">
        <li v-for="(item, index) in selectList" :key="index">
          <h5>{{ item.name }}</h5>
          <p>{{ item.description }}</p>
          <span class="icon_check" :class="{ checked: item.checked }" @click="checkType(item)"></span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      selectList: [],
      submitTypeList: []
    }
  },
  mounted() {
    this.$on('putFormData', () => {
      return this.putFormData()
    })
    this.$on('reqParamPackage', () => {
      return this.reqParamPackage()
    })
  },
  activated() {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到选择类型', title: '提示' })
    }
    this.selectList = this.pageParam
    if (this.allOpened) {
      this.$store.commit('updateNextBtnText', '返回首页')
    }
  },
  methods: {
    checkType(item) {
      item.checked = true
    },
    checkSubmit() {
      this.submitTypeList = this.selectList.filter(item => {
        return item.checked
      })
      return this.submitTypeList.length > 0
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        chooseTypeInfo: JSON.stringify(this.submitTypeList)
      }
      return params
    },
    putFormData() {
      let formData = {
        chooseType: this.submitTypeList
      }
      return formData
    }
  }
}
</script>

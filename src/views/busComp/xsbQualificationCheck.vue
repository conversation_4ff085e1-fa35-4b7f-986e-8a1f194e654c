<template>
  <div>
    <h5 v-if="pageParam[0].hasGZFlag === '0'" class="com_title">您还没有股转账户，请选择一个正常的深A开通</h5>
    <div class="acct_spbox mt10">
      <ul>
        <li
          :class="{disable:item.holderStatus!=0, checked : index == currentChecked}"
          v-for="(item,index) in showAccountList"
          :key="index"
          @click.stop="addCheckedClass(index,item.stockAccount,(item.openStatus == '1' || item.holderStatus != '0') ? false : true)"
        >
          <p>{{item.stockAccount}}</p>
          <em>{{item.marketName}}</em>
          <span class="state">{{item.holderStatus!=0 ? item.holderName : ""}}</span>
        </li>
      </ul>
    </div>
    <div v-if="pageParam[0].hasGZFlag === '1'" class="cond_tips">
      <p>您没有正常的账户，请先将账户规整至正常状态</p>
    </div>
    <div v-if="pageParam[0].hasGZFlag === '0' && !continueFlag" class="acct_spbox">
      <p class="tips">您在我司没有深A账户，请先前往加挂或者增开</p>
    </div>
  </div>
</template>
<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      currentChecked: "-1", // 当前选中的账户index
      checkedAccount: "", // 选中的账号,
      showAccountList: [], // 展示的账户列表
      continueFlag: false // 是否可以办理开通股转账户标识
    };
  },
  activated() {
    if (this.pageParam[0].hasGZFlag === "1") {
      // 有股转账户
      this.showAccountList = JSON.parse(this.pageParam[0].transferList);
    } else {
      this.showAccountList = JSON.parse(this.pageParam[0].accountList);
      for (let index = 0; index < this.showAccountList.length; index++) {
        const element = this.showAccountList[index];
        if (element.holderStatus == 0 && element.market == "00") {
          this.continueFlag = true;
        }
      }
    }
    if (this.continueFlag) {
      // 可以办理开通股转账户
      this.$store.commit("updateNextBtnText", "下一步"); // 修改下一步按钮
    } else {
      // 无法继续办理
      this.$store.commit("updateNextBtnText", "返回首页"); // 修改下一步按钮
    }
    this.currentChecked = -1;
    this.checkedAccount = "";
  },
  methods: {
    addCheckedClass(index, stockAccount, enableChecked) {
      if (!enableChecked) {
        return;
      }
      if (this.currentChecked == index) {
        this.currentChecked = -1;
        this.checkedAccount = "";
        return;
      }
      this.currentChecked = index;
      this.checkedAccount = stockAccount;
    },
    checkSubmit() {
      if (!this.checkedAccount) {
        _hvueToast({
          mes: "请选择账户"
        });
        return false;
      }
      return true;
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.continueFlag) {
          if (this.checkSubmit()) {
            // 可以下一步
            resolve();
          }
        } else {
          // 无法继续办理
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        market: "00",
        openType: "",
        stockAccount: this.checkedAccount
      };
      return params;
    },
    putFormData() {
      let formData = {
        chooseAccount: {
          //   transFlag: "1",
          market: "00",
          stockAccount: this.checkedAccount
        }
      }; // 需要保存的表单数据
      return formData;
    }
  }
};
</script>

/* * 销户账号条件检测--接口版 * @Author: Way * @Date: 2020-08-22 12:50:29 *
@Last Modified by: Way * @Last Modified time: 2020-08-27 10:52:19 */
<template>
  <div>
    <!-- <headComponent headerTitle="销户条件判断"></headComponent> -->
    <div class="cond_box">
      <div v-show="pageParam[0].accountCancellationClientList != undefined">
        <h3 class="type_title">客户号</h3>
        <div class="xh_must_box">
          <div
            class="xh_must_item"
            v-for="(d, index) in showClientAccountList"
            :key="index"
          >
            <div class="title">
              <h5 :class="d.isAccountCancellationFlag == '0' ? 'ok' : 'error'">
                {{ d.account }}
              </h5>
              <a
                v-if="d.isAccountCancellationFlag != '0'"
                class="arrow"
                :class="d.isShowDetails ? 'on' : ''"
                @click.stop="showDetails('0', index)"
              ></a>
            </div>
            <ul
              class="list"
              v-if="d.isAccountCancellationFlag == '1' && d.isShowDetails"
            >
              <li v-for="(a, i) in d.msg" :key="i">
                <p>{{ a }}</p>
                <!-- <a class="link" href="#">结息</a> -->
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div v-show="pageParam[0].accountCancellationAssetList != undefined">
        <h3 class="type_title">资金账户</h3>
        <div class="xh_must_box">
          <div
            class="xh_must_item"
            v-for="(d, index) in showAssetAccountList"
            :key="index"
          >
            <div class="title">
              <h5 :class="d.isAccountCancellationFlag == '0' ? 'ok' : 'error'">
                {{ d.mainFlag=='1'?'主':'辅' }}资金账号：{{ d.account }}
              </h5>
              <a
                v-if="d.isAccountCancellationFlag != '0'"
                class="arrow"
                :class="d.isShowDetails ? 'on' : ''"
                @click.stop="showDetails('1', index)"
              ></a>
            </div>
            <ul
              class="list"
              v-if="(d.isAccountCancellationFlag == '1') & d.isShowDetails"
            >
              <li v-for="(a, i) in d.msg" :key="i">
                <p>{{ a }}</p>
                <!-- <a class="link" href="#">结息</a> -->
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div
        v-show="pageParam[0].accountCancellationStockAccountList != undefined"
      >
        <h3 class="type_title">证券账户</h3>
        <div class="xh_must_box">
          <div
            class="xh_must_item"
            v-for="(d, index) in showStockAccountList"
            :key="index"
          >
            <div class="title">
              <h5 :class="d.isAccountCancellationFlag == '0' ? 'ok' : 'error'">
                {{ getMarketName(d.market)}}：{{ d.account }}
              </h5>
              <a
                v-if="d.isAccountCancellationFlag != '0'"
                class="arrow"
                :class="d.isShowDetails ? 'on' : ''"
                @click.stop="showDetails('2', index)"
              ></a>
            </div>
            <ul
              class="list"
              v-if="(d.isAccountCancellationFlag == '1') & d.isShowDetails"
            >
              <li v-for="(a, i) in d.msg" :key="i">
                <p>{{ a }}</p>
                <!-- <a class="link" href="#">结息</a> -->
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div v-show="pageParam[0].accountCancellationOtcAccountList != undefined">
        <h3 class="type_title">OTC账户</h3>
        <div class="xh_must_box">
          <div
            class="xh_must_item"
            v-for="(d, index) in showOtcAccountList"
            :key="index"
          >
            <div class="title">
              <h5 :class="d.isAccountCancellationFlag == '0' ? 'ok' : 'error'">
                {{ d.accountName }}：{{ d.account }}
              </h5>
              <a
                v-if="d.isAccountCancellationFlag != '0'"
                class="arrow"
                :class="d.isShowDetails ? 'on' : ''"
                @click.stop="showDetails('3', index)"
              ></a>
            </div>
            <ul
              class="list"
              v-if="(d.isAccountCancellationFlag == '1') & d.isShowDetails"
            >
              <li v-for="(a, i) in d.msg" :key="i">
                <p>{{ a }}</p>
                <!-- <a class="link" href="#">结息</a> -->
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div
        v-show="pageParam[0].accountCancellationFundAccountList != undefined"
      >
        <h3 class="type_title">基金账户</h3>
        <div class="xh_must_box">
          <div
            class="xh_must_item"
            v-for="(d, index) in showFundAccountList"
            :key="index"
          >
            <div class="title">
              <h5 :class="d.isAccountCancellationFlag == '0' ? 'ok' : 'error'">
                {{ d.accountName }}：{{ d.account }}
              </h5>
              <a
                v-if="d.isAccountCancellationFlag != '0'"
                class="arrow"
                :class="d.isShowDetails ? 'on' : ''"
                @click.stop="showDetails('4', index)"
              ></a>
            </div>
            <ul
              class="list"
              v-if="d.isAccountCancellationFlag == '1' && d.isShowDetails"
            >
              <li v-for="(a, i) in d.msg" :key="i">
                <p>{{ a }}</p>
                <!-- <a class="link" href="#">结息</a> -->
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent' // 头部
import { queryDictionary ,goBackXcApp} from '@/common/util'
export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data() {
    return {
      showPage: 0,

      clientFlag: '',
      assetFlag: '',
      stockFlag: '',
      otcFlag: '',
      fundFlag: '',

      accountCancellationClientList: [], // 客户号
      accountCancellationAssetList: [], // 资金账号
      accountCancellationStockAccountList: [], // 股东户
      accountCancellationFundAccountList: [], // 基金户
      accountCancellationOtcAccountList: [], // otc
      clientDetailsShow: false,
      assetDetailsShow: false,
      stockAccountDetailsShow: false,
      OtcAccountDetailsShow: false,
      fundAccountDetailsShow: false,
      accountTypeName: '',
      showAccountList: [],
      showClientAccountList: [],
      showAssetAccountList: [],
      showStockAccountList: [],
      showOtcAccountList: [],
      showFundAccountList: []
    }
  },
  activated() {
    // this.$store.commit('updateIsShowHead', false) // 隐藏head
    // 转换结果集格式
    this.accountCancellationClientList = this.pageParam[0]
      .accountCancellationClientList
      ? JSON.parse(this.pageParam[0].accountCancellationClientList)
      : []
    this.accountCancellationAssetList = this.pageParam[0]
      .accountCancellationAssetList
      ? JSON.parse(this.pageParam[0].accountCancellationAssetList)
      : []
    this.accountCancellationStockAccountList = this.pageParam[0]
      .accountCancellationStockAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationStockAccountList)
      : []
    this.accountCancellationFundAccountList = this.pageParam[0]
      .accountCancellationFundAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationFundAccountList)
      : []
    this.accountCancellationOtcAccountList = this.pageParam[0]

      .accountCancellationOtcAccountList
      ? JSON.parse(this.pageParam[0].accountCancellationOtcAccountList)
      : []
    // 重置是否满足条件
    this.clientFlag = ''
    this.assetFlag = ''
    this.stockFlag = ''
    this.otcFlag = ''
    this.fundFlag = ''

    // 获取是否满足条件
    this.clientFlag = this.pageParam[0].accountCancellationClientList
      ? this.accountCancellationClientList[0].isAccountCancellationFlag
      : ''
    this.accountCancellationClientList[0] &&
      this.$set(this.accountCancellationClientList, 0, {
        ...this.accountCancellationClientList[0],
        msg: this.accountCancellationClientList[0].msg.split('|')
      })
    for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
      let el = this.accountCancellationAssetList[i]
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.assetFlag = '1'
        el.msg = el.msg.split('|')
        this.$set(this.accountCancellationAssetList, i, el)
      }
    }
    for (let i = 0; i < this.accountCancellationStockAccountList.length; i++) {
      let el = this.accountCancellationStockAccountList[i]
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.stockFlag = '1'
        el.msg = el.msg.split('|')
        this.$set(this.accountCancellationStockAccountList, i, el)
      }
    }

    for (let i = 0; i < this.accountCancellationOtcAccountList.length; i++) {
      let el = this.accountCancellationOtcAccountList[i]
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.otcFlag = '1'
        el.msg = el.msg.split('|')
        this.$set(this.accountCancellationOtcAccountList, i, el)
      }
    }

    for (let i = 0; i < this.accountCancellationFundAccountList.length; i++) {
      let el = this.accountCancellationFundAccountList[i]
      if (el.isAccountCancellationFlag === '1') {
        // 不满足
        this.fundFlag = '1'
        el.msg = el.msg.split('|')
        this.$set(this.accountCancellationFundAccountList, i, el)
      }
    }
    this.showClientAccountList = this.showAccount('0')
    this.showAssetAccountList = this.showAccount('1')
    this.showStockAccountList = this.showAccount('2')
    this.showFundAccountList = this.showAccount('3')
    this.showOtcAccountList = this.showAccount('4')
    if (
      this.clientFlag == '1' ||
      this.assetFlag == '1' ||
      this.stockFlag == '1' ||
      this.otcFlag == '1' ||
      this.fundFlag == '1'
    ) {
      // 有为1的情况不满足
      this.$store.commit('updateNextBtnText', '返回首页')
      return
    }
    // this.$parent.emitNextEvent()
    this.$store.commit('updateNextBtnText', '下一步')
  },
  computed: {
    totalFlag() {
      if (
        this.clientFlag == '1' ||
        this.assetFlag == '1' ||
        this.stockFlag == '1' ||
        this.otcFlag == '1' ||
        this.fundFlag == '1'
      ) {
        // 有为false的情况不满足
        return false
      }
      return true
    }
  },
  methods: {
    getMarketName(m) {
      if (m == '10') {
        return '沪A'
      } else if (m == '98') {
        return '沪封基'
      } else if (m == '99') {
        return '深封基'
      } else {
        return '深A'
      }
    },
    showDetails(accountType, i) {
      let el
      switch (accountType) {
        case '0':
          el = this.showClientAccountList[i]
          el.isShowDetails = !el.isShowDetails
          this.$set(this.showClientAccountList, i, el)
          break
        case '1':
          el = this.showAssetAccountList[i]
          el.isShowDetails = !el.isShowDetails
          this.$set(this.showAssetAccountList, i, el)
          break
        case '2':
          el = this.showStockAccountList[i]
          el.isShowDetails = !el.isShowDetails
          this.$set(this.showStockAccountList, i, el)
          break
        case '3':
          el = this.showOtcAccountList[i]
          el.isShowDetails = !el.isShowDetails
          this.$set(this.showOtcAccountList, i, el)
          break
        case '4':
          el = this.showFundAccountList[i]
          el.isShowDetails = !el.isShowDetails
          this.$set(this.showFundAccountList, i, el)
          break
        default:
          break
      }
    },
    // 展示账户明细
    showAccount(accountType) {
      let showAccountList = []
      let _this = this
      queryDictionary(
        { type: 'ismp.xh.chooseAccountType', key: accountType },
        function(d) {
          _this.accountTypeName = d.value
        }
      )

      switch (accountType) {
        case '0':
          showAccountList = this.accountCancellationClientList
          break
        case '1':
          showAccountList = this.accountCancellationAssetList
          break
        case '2':
          showAccountList = this.accountCancellationStockAccountList
          break
        case '3':
          showAccountList = this.accountCancellationFundAccountList
          break
        case '4':
          showAccountList = this.accountCancellationOtcAccountList
          break
        default:
          break
      }
      showAccountList.map(item => {
        return (item.isShowDetails = true)
      })
      return showAccountList
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 开发环境不做拦截 || process.env.NODE_ENV == "development"
        if (this.totalFlag) {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          // this.$router.push({ name: 'index' })
          $hvue.env == 'ths' ? goBackXcApp() : this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let cancelAccount = []
      cancelAccount.push(...this.accountCancellationClientList) // 客户号
      cancelAccount.push(...this.accountCancellationAssetList) // 资金账户
      cancelAccount.push(...this.accountCancellationFundAccountList) // 基金账户
      cancelAccount.push(...this.accountCancellationStockAccountList) // 证券账户
      cancelAccount.forEach(item => {
        item.msg && delete item.msg
        item.needPassword && delete item.needPassword
        item.checked && delete item.checked
      })
      // 业务请求参数封装
      let params = {
        cancellationChooseAccount: JSON.stringify(cancelAccount)
      }
      return params
    },
    putFormData() {
      let cancelAccount = []
      cancelAccount.push(...this.accountCancellationClientList) // 客户号
      cancelAccount.push(...this.accountCancellationAssetList) // 资金账户
      cancelAccount.push(...this.accountCancellationFundAccountList) // 基金账户
      cancelAccount.push(...this.accountCancellationStockAccountList) // 证券账户
      cancelAccount.forEach(item => {
        item.msg && delete item.msg
        item.needPassword && delete item.needPassword
        item.checked && delete item.checked
      })
      let formData = {
        cancellationChooseAccount: JSON.stringify(cancelAccount)
      }
      return formData
    },
    pageBack() {
      if (this.showPage === 1) {
        this.showPage = 0
        this.$store.commit('updateBusinessNextBtnStatus', true)
      } else {
        this.$parent.back()
      }
    }
  }
}
</script>

<!-- 资质查询（准入条件查询）组件 -->
<template>
  <div v-cloak>
    <div class="cond_box">
      <h5 v-if="!totalFlag" class="com_title">您的以下条件存在问题，无法办理</h5>
       <h5 v-else class="com_title">以下条件已满足，可继续办理</h5>
      <ul  class="cond_list">
        <li v-for=" (item, index) in checkData" :key="index"

          :class="{ error: item.value === '0', ok: item.value === '1', warn: item.value === '2' }">
          <div class="tit">
            <p v-if="item.value !== '0'">{{ item.desc }}</p>
            <p v-else>{{ item.reasonDesc }}</p>
            <!-- <p>{{ item.desc }}</p> -->
            <a
              class="link"
              @click.stop="toRisk"
              v-show="item.key === 'minRankFlag' && item.value !== '1'"
            >重新测评</a>
            <!-- <span v-show="item.value !== '1' && item.key !== 'openAccountFlag'" class="tips">{{ item.value === '0' ? item.reasonDesc : item.unknowItemDesc }}</span> -->
          </div>
          <div class="cont" v-show="item.key === 'openAccountFlag'">
            <p v-for="(item,index) in accountList" :key="index">
              <span>{{ item.marketName }} {{ item.stockAccount }}</span>
              <span class="state">{{ item.holderStatus | filAccountState(item.regFlag, item.openStatus, item.market, item.holderName) }}</span>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="cond_tips">
      <p v-if="!totalFlag">抱歉，您不满足办理条件，无法办理此业务，详情可咨询客服人员</p>
      <p v-else>恭喜您，满足全部基本条件</p>
    </div>
  </div>
</template>
<script>
import accountViewList from '@/views/common/accountViewList' // 账户已全部开通
import { goBackXcApp, registerNativeBack } from '@/common/util'

export default {
  components: { accountViewList },
  props: ['pageParam'],
  data () {
    return {
      businessName: this.$parent.businessName, // 业务中文名
      checkData: [], // 检查列表
      totalFlag: true, // 整体条件是否满足
      openAccountFlag: '', // 账户是否满足：0已全部开通，1满足，2不满足
      conditionListShow: false,
      isQualityInspectionInfoActivated: false
    }
  },
  computed: {
    accountList() {
      const accountList = this.pageParam[0].accountList
      if (accountList && typeof accountList === 'string') {
        return typeof JSON.parse(accountList) === 'string'
          ? JSON.parse(JSON.parse(accountList))
          : JSON.parse(accountList)
      } else {
        return accountList
      }
    }
  },
  async activated () {
    // this.isQualityInspectionInfoActivated = false
    // this.$bus.on('qualityInspectionInfoActivated', () => {
    //   this.isQualityInspectionInfoActivated = true
    //   this.autoSkip()
    // })
    // window.androidAppBack = this.$parent.back
    console.log('activated 注册返回方法')
    registerNativeBack({
      callback: this.$parent.back
    })
    await this.conditionCheck()

    if (this.totalFlag) { // 条件满足直接跳到下一步
      this.$store.commit('updateBusinessNextBtnStatus', '下一步')
      // this.autoSkip()
    }

    this.conditionListShow = true
    // invokeNative('8011', '000', {}, data => {
    //   console.log('8011准入回调结果', data)
    // })
  },
  deactivated () {
    this.$bus.off('qualityInspectionInfoActivated')
  },
  methods: {
    conditionCheck () {
      this.$store.commit('updateBusinessNextBtnStatus', false)
      this.totalFlag = true
      this.checkData = []
      // 处理接口返回的结果集，拼装成前端展示用的数组结构checkData
      let key = this.pageParam[0].checkItem.split('|')// 检查项key
      let value = this.pageParam[0].itemValue.split('|')// 检查项value
      let desc = this.pageParam[0].itemDesc.split('|')// 检查项标题
      let reasonDesc = this.pageParam[0].reasonDesc.split('|')// 检查项描述
      for (var i = 0; i < key.length; i++) {
        let json = {
          key: key[i],
          value: value[i],
          desc: desc[i],
          reasonDesc:reasonDesc[i]
        }
        // 检查项描述,当value状态为2时，展示tips的提示语,默认展示reasonDesc
        if (value[i] === '2') {
          let tips = this.pageParam[0].unknowItemDesc ? this.pageParam[0].unknowItemDesc.split('|') : '' // 检查项描述,当itemValue状态为2时，展示这里的提示语
          json.reasonDesc = tips[i]
        } else {
          json.reasonDesc = reasonDesc[i]
        }
        this.checkData.push(json)
        if (value[i] === '0') {
          // 不满足办理条件
          this.totalFlag = false
          // 修改按钮文字
          this.$store.commit('updateBusinessNextBtnStatus', '返回首页')
        }
      }
    },
    // 满足条件时自动跳过
    autoSkip () {
      if (this.totalFlag) { // 条件满足直接跳到下一步
        this.$bus.off('qualityInspectionInfoActivated')
        this.$parent.emitNextEvent()
      }
    },
    // 跳转账户详细
    toAccountDetail () {
      this.$router.push({
        name: 'accountDetail',
        query: {
          accountList: this.pageParam[0].accountList,
          openAccountFlag: this.openAccountFlag
        }
      })
    },
    // 跳转到风险测评
    toRisk () {
      this.$router.push({
        name: 'business',
        query: { type: 'fxcp', name: '风险测评' }
      })
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 开发调试环境测试可以下一步
        if (this.totalFlag || process.env.NODE_ENV === 'development') {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          // this.$router.push({ name: 'index' })
          $hvue.env == 'ths' ? goBackXcApp() : this.$router.push({ name: 'index' });
          reject()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    // 表单参数打包
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>

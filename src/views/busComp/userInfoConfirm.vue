<!--两融预约开户身份信息确认-->
<template>
  <div>
    <h5 class="com_title">请您确认以下信息：</h5>
    <div class="input_form">
      <div class="input_text" v-show="initData.name">
        <span class="tit">姓名</span>
        <div class="ct">{{ initData.name }}</div>
      </div>
      <div class="input_text" v-show="initData.clientId">
        <span class="tit">客户号</span>
        <div class="ct">{{ initData.clientId }}</div>
      </div>
      <div class="input_text" v-show="initData.identityType">
        <span class="tit">证件类型</span>
        <div class="ct">
          {{
            initData.identityType == '0' || initData.identityType == '00'
              ? '身份证'
              : '非身份证'
          }}
        </div>
      </div>
      <div class="input_text" v-show="initData.identityNum">
        <span class="tit">证件号码</span>
        <div class="ct">{{ initData.identityNum }}</div>
      </div>
      <div class="input_text" v-show="initData.fundAccount">
        <span class="tit">资金账号</span>
        <div class="ct">{{ initData.fundAccount }}</div>
      </div>
      <div class="input_text" v-show="initData.branchName">
        <span class="tit">营业部</span>
        <p class="ct">{{ initData.branchName }}</p>
      </div>
    </div>
    <div class="input_form mt10">
      <div class="input_text">
        <span class="tit">手机号码</span>
        <div class="ct">{{ initData.mobile | formatMobileNo }}</div>
      </div>
      <div class="input_text" v-show="isDowntime">
        <span class="tit">图形验证码</span>
        <input
          ref="imgCodeInput"
          maxlength="4"
          v-model="imgCode"
          @input="checkInputComplete"
          class="t1"
          type="text"
          placeholder="请输入验证码"
        />
        <a class="code_img" href="javascript:;" @click="getImgCode"
          ><img :src="codeImgUrl"
        /></a>
      </div>
      <div class="input_text">
        <span class="tit">短信验证码</span>
        <input
          class="t1"
          ref="verifyCodeInput"
          type="tel"
          maxlength="6"
          v-model="verifyCode"
          @input="checkInputComplete"
          placeholder="请输入6位数字"
        />
        <!-- <a class="code_btn" @click="initGeetest">获取短信验证码</a> -->
        <smsTimer v-if="!isDowntime" v-model="startFlag" @sendSms="initGeetestClick"></smsTimer>
        <smsTimer v-if="isDowntime" v-model="startFlag" @sendSms="getVerifyCode"></smsTimer>
      </div>
      <div id="captcha-box"></div>
    </div>

    <!-- <p class="bot_tips mt10" v-show="$route.query.type !== 'xh'">
      若您的手机号码已变更，请先
      <a @click.stop="toModifyMobile" class="link">修改手机号码</a>
    </p> -->
  </div>
</template>
<script>
import('@/plugins/gt.js')
import smsTimer from '../../components/smsTimer'
import { queryDictionary, ajax, registerNativeBack } from '@/common/util'
import {
  sendMobileMsg,
  getImgCode,
  verifyImgCode
} from '@/service/comServiceNew'

export default {
  props: ['pageParam'],
  components: {
    smsTimer
  },
  data() {
    return {
      initData: {},
      startFlag: false, // 是否开始倒计时
      codeImgUrl: '', // 图片验证码图片url
      codeImgKey: '',
      imgCode: '', // 图形验证码
      verifyCode: '', // 用户输入的验证码
      smsCodeMaxLength: 6,
      smsNo: '', // 短信类型数据字典
      sendSmsFlag: '', // 是否已经发送了验证码
      initGeetestData: {},
      validateRes: {},
      captchaObj: null,
      isDowntime:false
    }
  },
  watch: {
    verifyCode() {
      if (this.verifyCode.length > this.smsCodeMaxLength) {
        this.verifyCode = String(this.verifyCode).slice(
          0,
          this.smsCodeMaxLength
        )
      }
    }
  },
  deactivated() {
    this.verifyCode = ''
    this.startFlag = false
    this.sendSmsFlag = false
  },
  activated() {
    registerNativeBack({
      callback: this.$parent.back
    })
    this.$store.commit('updateBusinessNextBtnStatus', '下一步') // 隐藏下一步按钮
    this.$store.commit('updateNextBtnDisabled', true) // 禁用下一步按钮
    this.checkInputComplete()
    this.getImgCode(true)
    this.initData = this.pageParam[0].synchronizedUserInfo
      ? JSON.parse(this.pageParam[0].synchronizedUserInfo)[0]
      : this.pageParam[0]
    // this.getImgCode(true)
    let that = this
    ajax({
      url:
        SERVER_URL.YGT_NEW_SERVER_SERVLET_API +
        '/register', // 加随机数防止缓存
      type: 'get', // type ---> 请求方式
      async: true, // async----> 同步：false，异步：true
      data: {
        //传入信息
        t: new Date().getTime(),
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        user_id: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        client_type: 'H5',
        ip: '127.0.0.1',
        mobile_no: $h.getSession('ygtUserInfo', { decrypt: false }).mobile,
        gt_flag: '1',
        gt_client_type: 'H5',
        gt_user_id: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        g_user_id: $h.getSession('ygtUserInfo', { decrypt: false }).userId
      },
      success: function(data) {
        //返回接受信息
        console.log(data)
        let result = JSON.parse(data)
        //宕机情况,还是放行让过
        if (result.success == '0') {
          // _hvueToast({
          //     icon: 'error',
          //     mes: '机制校验宕机当前走图片验证码模式'
          //   })
          that.isDowntime = true
          return
        }
        initGeetest(
          {
            product: 'bind',
            width: '300px',
            https: true,
            api_server: 'apiv6.geetest.com',
            gt: result.gt,
            challenge: result.challenge,
            offline: !result.success,
            new_captcha: result.new_captcha,
            onError: () => {
              console.log('onError')
              that.isDowntime = true
            }
          },
          function(captchaObj) {
            // captchaObj.appendTo(captchaBox)

            captchaObj
              .onReady(function() {
                console.log('准备')
              })
              .onSuccess(function() {
                console.log('成功')
                var validateRes = captchaObj.getValidate()
                ajax({
                  url:
                    SERVER_URL.YGT_NEW_SERVER_SERVLET_API +
                    '/validate?t=' +
                    new Date().getTime(), // 加随机数防止缓存
                  type: 'POST', // type ---> 请求方式
                  async: true, // async----> 同步：false，异步：true
                  data: {
                    user_id: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
                    client_type: 'H5',
                    //传入信息
                    geetest_challenge: encodeURIComponent(
                      validateRes.geetest_challenge
                    ),
                    geetest_seccode: encodeURIComponent(
                      validateRes.geetest_seccode
                    ),
                    geetest_validate: encodeURIComponent(
                      validateRes.geetest_validate
                    ),
                    challenge: encodeURIComponent(
                      validateRes.geetest_challenge
                    ),
                    seccode: encodeURIComponent(validateRes.geetest_seccode),
                    validate: encodeURIComponent(validateRes.geetest_validate)
                  },
                  success: function(data) {
                    //返回接受信息
                    console.log(data)
                    let result = JSON.parse(data)
                    if (result.success == '0') {
                      _hvueToast({
                        icon: 'error',
                        mes: result.msg
                      })
                      captchaObj.reset()
                    } else {
                      var params = {
                        ip:$h.getSession('LIP'),
                        flow_name: that.$route.query.type,
                        smsNo: that.smsNo || 'xh_sms_check',
                        businessCode: that.$route.query.type,
                        userId: $h.getSession('ygtUserInfo', { decrypt: false })
                          .userId,
                        mobile: that.initData.mobile
                      }
                      sendMobileMsg(params).then(res => {
                        if (res.error_no === '0') {
                          // 短信验证码发送成功，开始倒计时
                          // console.log(this)
                          that.startFlag = true
                          that.sendSmsFlag = true
                        } else {
                          _hvueAlert({
                            title: '提示',
                            mes: res.error_info,
                            callback: () => {
                              this.getImgCode()
                            }
                          })
                        }
                      })
                    }
                  }
                })
              })
              .onError(function() {
                console.log('错误')
                that.isDowntime = true
              })
            let smsButton = document.getElementById('smsButton')
            // 按钮提交事件
            smsButton.onclick = function() {
              // some code
              // 检测验证码是否ready, 验证码的onReady是否执行
              if (that.startFlag) return
              captchaObj.verify() //显示验证码
              // some code
            }
            // that.captchaObj = captchaObj
          }
        )
      }
    })
  },
  mounted() {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.sms_type', value: this.$parent.businessName },
      data => {
        this.smsNo = data.key
      }
    )
  },
  methods: {
    /** ************子组件公共方法定义****** */
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 判断是否已经发送了验证码
        if (!this.sendSmsFlag) {
          _hvueToast({ mes: '请先发送验证码' })
          reject(new Error({ mes: '请先发送验证码' }))
        }
        // 检查是否填写了验证码
        if (this.verifyCode === '') {
          _hvueToast({ mes: '请输入验证码' })
          reject(new Error({ mes: '请输入验证码' }))
        }
        if (this.verifyCode.length !== 6) {
          _hvueToast({ mes: '请输入正确位数的验证码' })
          reject(new Error({ mes: '请输入正确位数的验证码' }))
        }
        if (!/^\d{6}$/.test(this.verifyCode)) {
          _hvueToast({ mes: '请输入六位数字验证码' })
          reject(new Error({ mes: '请输入六位数字验证码' }))
        }
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        mobileKey: this.codeImgKey,
        imageCode: this.imgCode,
        mobile: this.initData.mobile,
        verifyCode: this.verifyCode,
        smsNo: this.smsNo || 'xh_sms_check',
        businessCode: this.$route.query.type
      }
      return params
    },
    putFormData() {
      let formData = {}
      return formData
    },
    /** ************子组件公共方法定义****** */
    // 获取图片验证码
    getImgCode(isInit) {
      getImgCode({}, {}).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0]
            this.codeImgUrl = results.imageCode
            this.codeImgKey = results.mobileKey
            this.clearInput('imgCode')
            if (isInit && !this.initData.mobile) {
              _hvueToast({
                icon: 'error',
                mes: '无法获取手机号'
              })
              return false
            }
            // this.$refs.imgCodeInput.focus()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 输入完整性检查
    checkInputComplete() {
      if (this.verifyInput()) {
        this.$store.commit('updateNextBtnDisabled', false) // 禁用下一步按钮
      } else {
        this.$store.commit('updateNextBtnDisabled', true) // 禁用下一步按钮
      }
    },
    // 输入内容校验
    verifyInput() {
      if (this.verifyCode) {
        return true
      }
      return false
    },
    // 清除输入内容
    clearInput(item) {
      this[item] = ''
      this.checkInputComplete()
    },
    // 校验图片验证码
    verifyImgCode(cb) {
      verifyImgCode({
        mobileKey: this.codeImgKey,
        imageCode: this.imgCode
      }).then(
        res => {
          if (res.error_no === '0') {
            cb && typeof cb === 'function' && cb()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
            this.getImgCode()
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 获取短信验证码
    getVerifyCode() {
      if (!this.initData.mobile) {
        _hvueToast({
          icon: 'error',
          mes: '无法获取手机号'
        })
        return false
      }
      if (this.isDowntime&&!this.imgCode) {
        _hvueToast({ mes: '请输入图形验证码' })
        // this.$refs.imgCodeInput.focus()
        return false
      }

      // 校验图片验证码
      this.verifyImgCode(() => {
        // 获取短信验证码
        var params = {
          flow_name: this.$route.query.type,
          smsNo: this.smsNo || 'xh_sms_check',
          businessCode: this.$route.query.type,
          userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
          mobile: this.initData.mobile
        }
        sendMobileMsg(params).then(res => {
          if (res.error_no === '0') {
            // 短信验证码发送成功，开始倒计时
            // console.log(this)
            this.startFlag = true
            this.sendSmsFlag = true
          } else {
            _hvueAlert({
              title: '提示',
              mes: res.error_info,
              callback: () => {
                this.getImgCode()
              }
            })
          }
        })
      })
    },
    //注册行为验证码
    registerSms() {
      var params = {
        flow_name: this.$route.query.type,
        smsNo: this.smsNo || 'xh_sms_check',
        businessCode: this.$route.query.type,
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId
      }
      registerSms(params).then(res => {
        if (res.error_no === '0') {
          let results = res.results ? res.results[0] : res.generateVerifyCode[0]
          this.initGeetestData = results
        } else {
          _hvueToast({
            title: '提示',
            mes: res.error_info
          })
          return false
        }
      })
    },
    initGeetestClick() {
      if (!this.initData.mobile) {
        _hvueToast({
          icon: 'error',
          mes: '无法获取手机号'
        })
        return false
      }
      // captchaObj.verify() //显示验证码
      //   ajax({
      //   url: SERVER_URL.YGT_NEW_SERVER_SERVLET_API+'/register?t=' + (new Date()).getTime(), // 加随机数防止缓存
      //   type: 'get', // type ---> 请求方式
      //   async: true, // async----> 同步：false，异步：true
      //   data: {
      //     //传入信息
      //     userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
      //     client_type: ''
      //   },
      //   success: function(data) {
      //     //返回接受信息
      //     console.log(data)
      //     let result = JSON.parse(data)
      //     if(!result.success=='0'){
      //       _hvueToast({
      //           icon: 'error',
      //           mes: result.error_info
      //         })
      //     }
      //     // let captchaBox = document.getElementById('captcha-box')
      //     initGeetest(
      //     {
      //       product: 'bind',
      //       gt: result.gt,
      //       challenge: result.challenge,
      //       offline: !result.initGeetest,
      //       new_captcha: true
      //     },
      //     function(captchaObj) {
      //       // captchaObj.appendTo(captchaBox)

      //       captchaObj
      //         .onReady(function() {
      //           //your code
      //           console.log('准备')
      //         })
      //         .onSuccess(function() {
      //            console.log('成功')
      //         })
      //         .onError(function() {
      //           //your code
      //           console.log('错误')
      //         })
      //         captchaObj.verify(); //显示验证码
      //     }
      //   )
      //   }
      // })

      // var captchaBox = document.getElementById('captcha-box')
      // initGeetest(
      //   {
      //     gt: initGeetest.gt,
      //     challenge: initGeetest.challenge,
      //     offline: !initGeetest.initGeetest,
      //     new_captcha: true
      //   },
      //   function(captchaObj) {
      //     captchaObj.appendTo(captchaBox)

      //     captchaObj
      //       .onReady(function() {
      //         //your code
      //       })
      //       .onSuccess(function() {
      //         var params = {
      //           flow_name: this.$route.query.type,
      //           smsNo: this.smsNo,
      //           businessCode: this.$route.query.type,
      //           userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
      //           mobile: this.initData.mobile
      //         }
      //         sendMobileMsg(params).then(res => {
      //           if (res.error_no === '0') {
      //             // 短信验证码发送成功，开始倒计时
      //             // console.log(this)
      //             this.startFlag = true
      //             this.sendSmsFlag = true
      //           } else {
      //             _hvueAlert({
      //               title: '提示',
      //               mes: res.error_info,
      //               callback: () => {}
      //             })
      //           }
      //         })
      //       })
      //       .onError(function() {
      //         //your code
      //       })
      //   }
      // )
    },
    // 跳转修改手机号
    toModifyMobile() {
      this.$router.go(-1)
      // this.$router.push({
      //   name: 'business',
      //   query: { type: 'xgsjh', name: '修改手机号' }
      // })
    }

  }
}
</script>

<template>
  <div class="user_main">
    <h5 class="com_title">请填写以下信息</h5>
    <div class="input_form mobile_form">
      <div class="ui field text" :class="{ code: item.type === 'code' }"
        v-for="(item, index) in inputModelList" :key="index">
        <label class="ui label">{{ item.name }}</label>
        <input
          v-model.trim="item.value"
          :maxlength="item.maxlength"
          type="text"
          class="ui input"
          :placeholder="item.placeholder"
          @input="checkInputComplete"
        />
        <a
          class="txt_close"
          @click.stop="clearInput(item)"
          v-show="item.value !== ''"
        ></a>
        <a v-if="item.type === 'code'" class="code_img" @click.stop="getImgCode">
          <img :src="codeImgUrl" />
        </a>
      </div>
    </div>
    <div class="ce_btn mt20">
      <a class="ui button block rounded" :class="{ disabled: nextStepBtnDisabled }" @click.stop="nextStep"
        >下一步</a
      >
    </div>
  </div>
</template>

<script>
import {
  queryUserFlowStatus,
  getImgCode,
  verifyImgCode
} from '@/service/comServiceNew'
import { checkInput } from '@/common/util'

export default {
  data() {
    return {
      handleResult: true,
      handleError: true,
      isLogin: $h.getSession('isLogin', { decrypt: false }) || false,
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      inputModelList: [ {
        type: 'account',
        name: '客户账号',
        value: '',
        maxlength: '15',
        format: 'num',
        placeholder: '请输入客户账号'
      }, {
        type: 'name',
        name: '真实姓名',
        value: '',
        maxlength: '15',
        minlength: '2',
        format: 'name',
        placeholder: '请输入真实姓名'
      }, {
        type: 'idno',
        name: '身份证号',
        value: '',
        minlength: '18',
        maxlength: '18',
        format: 'idno',
        placeholder: '请输入身份证号'
      }, {
        type: 'code',
        name: '图片验证码',
        value: '',
        maxlength: '4',
        minlength: '4',
        format: 'enNum',
        placeholder: '请输入图形验证码'
      } ],
      nextStepBtnDisabled: true,
      codeImgUrl: '', // 图片验证码图片url
      codeImgKey: ''
    }
  },
  activated() {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.nextStepBtnDisabled = true
    this.getImgCode() // 获取验证码
    // 清空输入框
    this.inputModelList.forEach(item => {
      item.value = ''
    })
    if (this.isLogin && this.ygtUserInfo.userId) {
      this.inputModelList.forEach(item => {
        if (item.type === 'account' && this.ygtUserInfo.clientId) {
          item.value = this.ygtUserInfo.clientId
        }
        if (item.type === 'name' && this.ygtUserInfo.name) {
          item.value = this.ygtUserInfo.name
        }
        if (item.type === 'idno' && this.ygtUserInfo.identityNum) {
          item.value = this.ygtUserInfo.identityNum
        }
      })
    }
  },
  methods: {
    // 获取图片验证码
    getImgCode() {
      getImgCode({}, {}).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0]
            this.codeImgUrl = results.imageCode
            this.codeImgKey = results.mobileKey
            this.inputModelList.forEach(item => {
              if (item.type === 'code') {
                this.clearInput(item)
              }
            })
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 校验图片验证码
    verifyImgCode() {
      let imgCode = ''
      this.inputModelList.forEach(item => {
        if (item.type === 'code') {
          imgCode = item.value
        }
      })
      verifyImgCode(
        { mobileKey: this.codeImgKey, imageCode: imgCode }
      ).then(
        res => {
          if (res.error_no === '0') {
            // 调用父组件的下一步事件
            this.$parent.emitNextEvent()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
            this.getImgCode()
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    clearInput(item) {
      item.value = ''
      this.checkInputComplete()
    },
    nextStep() {
      if (!this.nextStepBtnDisabled) {
        if (this.verifyAccountInput()) {
          this.verifyImgCode()
        }
      }
    },
    // 校验输入
    verifyAccountInput(isShowTip) {
      let flag = ''

      for (let s = 0; s < this.inputModelList.length; s++) {
        let el = this.inputModelList[s]
        flag = checkInput(el, isShowTip)
        if (flag !== '') {
          break
        }
      }
      if (flag === '') {
        return true
      }
      return false
    },
    // 检查输入完整性
    checkInputComplete() {
      if (this.inputModelList.every(item => {
        return item.value
      })) {
        this.nextStepBtnDisabled = false
      } else {
        this.nextStepBtnDisabled = true
      }
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let paramMap = {
        'account': 'clientId',
        'name': 'userName',
        'idno': 'identityNum',
        'code': 'code'
      }
      let params = {} // 提交需要的参数
      this.inputModelList.forEach(item => {
        if (paramMap[item.type]) {
          params[paramMap[item.type]] = item.value
        }
      })
      return params
    },
    putFormData() {
      let paramMap = {
        'account': 'clientId',
        'name': 'userName',
        'idno': 'identityNum',
        'code': 'code'
      }
      let formData = {} // 需要保存的表单数据
      this.inputModelList.forEach(item => {
        if (paramMap[item.type]) {
          formData[paramMap[item.type]] = item.value
        }
      })
      return formData
    },
    // 提交后对结果进行处理的方法
    async handleResultFunc(res) {
      this.ygtUserInfo = res.checkIdno[0]
      this.inputModelList.forEach(item => {
        if (item.type === 'name') {
          this.ygtUserInfo.name = item.value
        }
      })
      Object.assign(this.$parent.publicParam, this.ygtUserInfo)
      this.$parent.publicParam.serivalId = res.results[0].serivalId
      this.$parent.flow.serivalId = res.results[0].serivalId
      $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })
      $h.setSession('isLogin', false, { encrypt: false })

      // 查询表单状态
      const formStatusResult = await queryUserFlowStatus({
        userId: this.ygtUserInfo.userId,
        businessCode: this.$route.query.type
      })
      if (formStatusResult.error_no === '0') {
        let flowId = formStatusResult.results[0].flowId
        if (flowId) { // 有返回flowId则调用business的initFlow方法根据flowId去加载下一步
          this.$parent.initFlow()
          return false
        }

        // 初始化下一节点
        this.$parent.$emit('init')
      } else {
        _hvueAlert({ mes: formStatusResult.error_info })
      }
    },
    // 提交后报错处理
    handleErrorFunc() {
      this.getImgCode()
    }
  }
}
</script>

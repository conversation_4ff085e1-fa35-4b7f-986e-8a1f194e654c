<template>
  <div>
    <article class="content">
      <div v-show="videoState == 0">
        <h5 class="com_title spel">
          <i class="icon_ling"></i
          >根据监管要求，销户需要获取投资者人脸信息，并向投资者核实身份真实性、销户意愿真实性
        </h5>
        <div class="witness_box">
          <h5>请进行人脸识别来认证身份</h5>
          <p>为了确保本人操作我们将进行人脸识别</p>
          <div class="dx_tip_pic">
            <img src="@/assets/images/video_face2.png" />
          </div>
          <div class="dx_tipbox">
            <p>视频环节请注意以下事项：</p>
            <ul>
              <li>
                <i class="icon"><img src="@/assets/images/fc_tp01.png"/></i
                ><span>确保光线清晰</span>
              </li>
              <li>
                <i class="icon"><img src="@/assets/images/fc_tp02.png"/></i
                ><span>远离嘈杂环境</span>
              </li>
              <li>
                <i class="icon"><img src="@/assets/images/fc_tp03.png"/></i
                ><span>不能带帽子</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="fc_sbbox" v-show="videoState == 5">
        <div class="fc_imgbox ing">
          <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
        </div>
        <h5>识别中…</h5>
      </div>
      <div class="fc_sbbox" v-show="videoState == 1">
        <div class="fc_imgbox error">
          <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
        </div>
        <h5 class="error">检测不通过</h5>
        <p>自拍照与证件照不是同一人</p>
      </div>
      <div class="fc_sbbox" v-show="videoState == 2">
        <div class="fc_imgbox ok">
          <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
        </div>
        <h5>检测识别通过</h5>
      </div>
    </article>
    <div class="ce_btn">
      <a v-throttle class="p_button" @click="start" v-show="videoState == 0"
        >开始认证</a
      >
      <a v-throttle class="p_button" @click="start" v-show="videoState == 1"
        >重新认证</a
      >
      <a
        v-throttle
        class="p_button"
        @click="oneWitnessEvent"
        v-show="videoState == 2"
        >开始录制</a
      >
    </div>
  </div>
</template>

<script>
import {
  faceCcompare,
  videoUpload,
  videoBase64Upload,
  getOneWitnessToken
} from '@/service/comServiceNew'
import { goBackXcApp } from '@/common/util'
export default {
  name: 'oneWayVideoPage',

  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      businessCode: this.$route.query.type,
      videoState: 0, //0活体准备中1活体检测失败2活体通过
      liveFailCount: 0,
      uploadRequest: '', // 上传视频 请求（用于取消）
      videoPath: '',
      jwtToken: '',
      flowNo: '',
      oneWayPass: '0',
      actionType: '',
      actionArr: ['眨眼', '点头', '摇头', '张嘴'],
      actionArrEn: ['blink', 'nod', 'yaw', 'mouth']
    }
  },
  methods: {
    // 返回
    pageBack() {
      _hvueConfirm({
        mes: '单向视频认证未完成，是否返回首页？',
        opts: [
          {
            txt: '取消',
            color: false
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 返回首页
              $hvue.env == 'ths'
                ? goBackXcApp()
                : this.$router.push({ name: 'index' })
            }
          }
        ]
      })
    },
    start() {
      if (this.jwtToken) {
        this.livingThing()
      } else {
        _hvueAlert({
          mes: '抱歉，token失效,重新获取token',
          callback: () => {
            this.getJwtToken()
          }
        })
      }
    },
    livingThing() {
      let randomAction = this.getRandAction(2)
      let actionType = randomAction
      this.actionArr.forEach((item, i) => {
        if (randomAction.includes(item)) {
          actionType = actionType.replace(item, this.actionArrEn[i])
        }
      })
      let param = {
        fixedAction: '眨眼',
        randomAction: randomAction,
        randomActionCount: 3,
        singleCheckTime: 15
      }
      this.actionType = actionType
      console.log('actionType', this.actionType)
      invokeNative('8008', '000', param, data => {
        console.log('回调结果', data)
        data = JSON.parse(data)
        this.livingThingCallBack(data)
      })
    },
    async getJwtToken() {
      const data = await getOneWitnessToken({
        serivalId: this.$parent.publicParam.serivalId,
        userId: this.ygtUserInfo.userId,
        clientId: this.ygtUserInfo.clientId
      })
      if (data.error_no == 0) {
        this.jwtToken = data.DataSet[0].jwtToken
        this.flowNo = data.DataSet[0].flowNo
      } else {
        _hvueAlert({ mes: data.error_info })
        return
      }
    },
    uploadImag(base64) {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
      var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //android终端
      let origin = '3'
      if (isiOS) {
        origin = '2'
      } else if (isAndroid) {
        origin = '1'
      }
      faceCcompare({
        businessCode: this.businessCode,
        actionType: this.actionType,
        base64Image: encodeURI(
          base64.indexOf('base64,') > 0 ? base64.split('base64,')[1] : base64
        ),
        userId: this.ygtUserInfo.userId,
        jwtToken: this.jwtToken,
        serivalId: this.$parent.publicParam.serivalId,
        flowNo: this.flowNo,
        origin: origin
      }).then(res => {
        if (res.error_no == '0') {
          let result = res.DataSet[0]
          if (
            result.pass == '1' ||
            result.pass == 'true' ||
            result.pass == true
          ) {
            this.videoState = 2
          } else {
            this.liveFailCount++
            if (this.liveFailCount >= 3) {
              _hvueAlert({
                mes: '抱歉，人脸比对多次不通过，转双向视频',
                callback: () => {
                  this.oneWayPass = '0'
                  this.nextClick()
                }
              })
              return
            } else {
              _hvueAlert({
                mes: '抱歉，人脸比对不通过，重新检测',
                callback: () => {
                  this.videoState = 1
                }
              })
              return
            }
          }
        } else {
          _hvueAlert({
            mes: res.error_info,
            callback: () => {
              this.videoState = 1
            }
          })
          return
        }
      })
    },
    //活体检测回调
    livingThingCallBack(data) {
      if (data.code == '0') {
        console.log('上传中')
        this.uploadImag(data.imageData)
      } else {
        this.liveFailCount++
        if (this.liveFailCount >= 3) {
          _hvueAlert({
            mes: '抱歉，人脸比对多次不通过，转双向视频',
            callback: () => {
              this.oneWayPass = '0'
              this.nextClick()
            }
          })
          return
        } else {
          _hvueAlert({
            mes: data.errorInfo || '活体检测失败',
            callback: () => {}
          })
          return
        }
      }
    },
    //开启视频录制
    oneWitnessEvent() {
      let param = {
        jwtToken: this.jwtToken,
        readContext: `请问您是${this.ygtUserInfo.name}本人，已阅读并了解《非现场注销账户业务须知》内容及风险，自愿办理账户注销业务吗？`,
        tipsContext: `我是${this.ygtUserInfo.name}本人，已阅读并了解《非现场注销账户业务须知》内容及风险，自愿办理账户注销业务。`,
        bizName: '销户'
      }
      invokeNative('8009', '000', param, data => {
        console.log('单项视频录制结果', data)
        data = JSON.parse(data)

        this.oneWayVideoCallBack(data)
      })
    },
    //单项视频回调
    oneWayVideoCallBack(data) {
      if (data.code == '0') {
        this.videoPath = data.videoPath
        this.videoBase64Upload()
      } else if (data.code == '3') {
        _hvueAlert({
          mes: '抱歉，人脸比对多次不通过，转双向视频',
          callback: () => {
            this.oneWayPass = '0'
            this.nextClick()
          }
        })
        return
      }
    },
    videoBase64Upload() {
      videoBase64Upload({
        jwtToken: this.jwtToken,
        userId: this.ygtUserInfo.userId,
        businessCode: this.businessCode,
        videoPath: this.videoPath,
        serivalId: this.$parent.publicParam.serivalId
      }).then(res => {
        if (res.error_no == '0') {
          console.log('单向视频上传成功')
          this.oneWayPass = '1'
          this.nextClick()
        } else {
          this.isVideoPage = false
          _hvueAlert({
            mes: res.error_info
          })
          return
        }
      })
    },
    getRandAction(num) {
      let actionArr = JSON.parse(JSON.stringify(this.actionArr))
      let resultArr = []
      if (!num) num = 1
      if (num > actionArr.length) num = actionArr.length
      for (let i = 0; i < num; i++) {
        var rand = Math.floor(Math.random() * actionArr.length)
        resultArr.push(actionArr.splice(rand, 1))
      }
      return resultArr.join('|')
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    nextClick() {
      this.$parent.emitNextEvent()
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {
        oneWayPass: this.oneWayPass
      }
      return params
    },
    putFormData() {
      let formData = {} // 需要保存的表单数据
      return formData
    }
  },
  activated() {
    window.androidAppBack =  this.pageBack
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.getJwtToken()
  },
  deactivated() {
    this.videoState = 0
    this.liveFailCount = 0
  }
}
</script>

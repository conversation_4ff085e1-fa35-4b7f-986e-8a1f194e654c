<!-- 协议列表组件2- 单独的协议列表，带必读属性 -->
<template>
  <div>
    <!--头部 -->
    <headComponent headerTitle="协议签署"></headComponent>
    <div class="agree_main">
      <!-- <h5 class="title">请认真阅读并签署以下协议：</h5> -->
      <ul class="protocol_list">
        <li
          :class="item.isRead ? 'read' : 'unread'"
          v-for="(item, index) in pageParam"
          :key="index"
        >
          <a
            @click.prevent="
              toDetail(
                item.agreementId,
                item.agreeName,
                item.agreementType,
                index
              )
            "
            >《{{ item.agreeName }}》</a
          >
        </li>
      </ul>
    </div>
    <p class="bottom_tips" v-show="bottom_tips">{{ bottom_tips }}</p>
    <div class="bottom_check">
      <div class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: isChecked }"
          @click.stop="readProtocolCheck"
        ></span>
        <label>我已阅读并同意签署以上协议</label>
      </div>
    </div>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent' // 头部
import { goBackXcApp, registerNativeBack } from '@/common/util'
import { submitBeforCheck } from '@/service/comServiceNew'
export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data() {
    return {
      keywords: [],
      isChecked: false,
      agreementIds: [],
      signAgreeMd5s: [],
      bottom_tips: '',
      tips: ''
    }
  },
  created() {
    this.$store.commit('updateNextBtnDisabled', !this.isChecked) // 禁用下一步按钮
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    console.log('协议签署 注册返回事件1')
    registerNativeBack({
      callback: this.pageBack
    })
  },
  mounted() {
    this.$store.commit('updateNextBtnDisabled', !this.isChecked) // 禁用下一步按钮
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    console.log('协议签署 注册返回事件2')
    registerNativeBack({
      callback: this.pageBack
    })
  },
  activated() {
    this.$store.commit('updateNextBtnDisabled', !this.isChecked) // 禁用下一步按钮
    // window.androidAppBack = this.pageBack
    console.log('协议签署 注册返回事件3')
    registerNativeBack({
      callback: this.pageBack
    })
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到协议', title: '提示' })
      return
    }
    this.keywords = this.$parent.keywords || []
    this.agreementIds = []
    this.signAgreeMd5s = []
    let hasRead = $h.getSession('agreementIds') || ''
    for (let index = 0; index < this.pageParam.length; index++) {
      const element = this.pageParam[index]
      if (hasRead.includes(element.agreementId)) {
        element.isRead = true
      } else {
        element.isRead = false
      }
      this.$set(this.pageParam, index, element)
      this.agreementIds.push(element.agreementId)
      let signAgreeMd5 = {
        xyid: element.agreementId,

        md5: element.agreeMd5
      }
      this.signAgreeMd5s.push(signAgreeMd5)
    }
    if (this.$route.query.type === 'lryykh') {
      this.tips = '本人已阅读该风险揭示书及特别提示，已了解融资融券业务相关风险'
    }
    if (
      this.$route.query.type === 'cybkt' &&
      $h.getSession('isOpenCredit') === 1
    ) {
      this.bottom_tips =
        '如您涉及阅读完成《融资融券业务创业板开通风险揭示书》，点选下方“已阅读并知晓以上协议”选择框，则表示您已同意该揭示书内“确认开通融资融券业务信用账户创业板交易权限”的表述内容符合您本人的真实意愿。'
    }

    this.$bus.on('isReadAgreement', agreementIndex => {
      this.agreementIsRead(agreementIndex)
    })
    this.$bus.on('addKeywords', keyword => {
      if (this.keywords && this.keywords[0] && this.keywords[0][0]) {
        this.keywords[0][0] = Object.assign({}, this.keywords[0][0], keyword)
        return
      }
      this.keywords = [[keyword]]
    })
  },
  deactivated() {
    this.$bus.off('isReadAgreement')
    this.$bus.off('addKeywords')
  },
  destroyed() {
    this.$bus.off('isReadAgreement')
    this.$bus.off('addKeywords')
    $h.clearSession('isOpenCredit')
  },
  methods: {
    // 返回
    pageBack() {
      _hvueConfirm({
        mes: '是否放弃本次业务办理？',
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              console.log('协议签署 注册返回事件')
              registerNativeBack({
                callback: this.pageBack
              })
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 返回首页
              $hvue.env == 'ths'
                ? goBackXcApp()
                : this.$router.push({ name: 'index' })
            }
          }
        ]
      })
    },
    toDetail(agreementId, agreementName, agreementType, index) {
      if (!this.keywords || !this.keywords[0] || !this.keywords[0][0]) {
        this.keywords = this.$parent.keywords || []
      }
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: {
          agreementId: agreementId,
          agreementTitle: agreementName,
          tips: this.tips,
          agreementIndex: index,
          type: this.$route.query.type,
          agreementType: agreementType,
          keyWords: this.keywords[0] && JSON.stringify(this.keywords[0][0])
        }
      })
    },
    agreementIsRead(agreementIndex) {
      this.$set(this.pageParam[agreementIndex], 'isRead', true)
    },
    readProtocolCheck() {
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        if (!element.isRead) {
          this.isChecked = false
          _hvueToast({ mes: '请先阅读完所有协议' })
          return
        }
      }
      this.isChecked = !this.isChecked
      if (this.isChecked) {
        this.$store.commit('updateNextBtnDisabled', false) // 禁用下一步按钮
      } else {
        this.$store.commit('updateNextBtnDisabled', true) // 禁用下一步按钮
      }
    },
    checkSubmit() {
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        if (!element.isRead) {
          this.isChecked = false
          _hvueAlert({ title: '温馨提示', mes: '请先阅读完所有协议' })
          return
        }
      }
      if (!this.isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选同意按钮' })
        return false
      }
      return true
    },

    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          const { serivalId } = this.$parent.publicParam
          const flowName = this.$parent.flowName
          submitBeforCheck({
            serivalId,
            flowName
          })
            .then(({ error_no, error_info, DataSet }) => {
              if (error_no === '0') {
                const skipFlag = DataSet[0].skipFlag
                if (skipFlag === '0') {
                  resolve()
                } else {
                  _hvueAlert({
                    mes: '流程异常，请重新办理业务。',
                    callback: () => {
                      this.$router.replace({
                        name: 'index'
                      })
                    }
                  })
                  reject(new Error('流程异常，请重新办理业务。'))
                }
              } else {
                _hvueAlert({
                  mes: error_info,
                  callback: () => {
                    this.$router.replace({
                      name: 'index'
                    })
                  }
                })
                reject(new Error(error_info))
              }
            })
            .catch(e => {
              reject(e)
            })
        } else {
          reject(new Error({ mes: '表单校验不通过' }))
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        agreementId: this.agreementIds.join(','),
        keyWords: this.keywords[0] && JSON.stringify(this.keywords[0][0]),
        agreements: JSON.stringify(this.signAgreeMd5s)
      }
      return params
    },
    putFormData() {
      let formData = {
        // signProtocol: { agreementId: this.agreementIds.join(',') }
        signProtocol: { agreementId: this.agreementIds.join(',') }
      }
      return formData
    }
  }
}
</script>

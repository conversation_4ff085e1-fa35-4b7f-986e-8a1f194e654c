<!--适当性匹配-->
<template>
  <div>
    <div class="approp_box">
      <div class="approp_result">
        <h5>
          <i class="icon">
            <img :src="!+pageParam[0].matchingFlag ? matchImg : notMatchImg">
          </i>
          您的适当性评估{{ !+pageParam[0].matchingFlag ? '匹配' : '不匹配' }}
        </h5>
      </div>
      <div class="approp_info">
        <ul>
          <li>
            <span class="tit">个人风险承受能力等级</span>
            <p>{{pageParam[0].riskLevelDesc}}</p>
          </li>
          <li>
            <span class="tit">拟接受服务</span>
            <p>{{pageParam[0].businessName}}</p>
          </li>
          <li>
            <span class="tit">服务风险等级</span>
            <p>{{pageParam[0].proRiskLevelDesc}}</p>
          </li>
          <!-- <li>
            <span class="tit">投资品种</span>
            <p>{{pageParam[0].investTypeDesc}}</p>
          </li> -->
        </ul>
      </div>
      <div class="approp_tips" v-if="!+pageParam[0].isStrongMatch || !+pageParam[0].matchingFlag || +pageParam[0].minRankFlag">
        <p style="text-align:center;">本公司在此郑重提醒</p>
        <template v-if="!+pageParam[0].matchingFlag">
        <p>本司已经向您充分揭示了该金融产品或服务的风险。您的风险承受能力等级、拟投资期限、投资品种、预期收益等投资目标与该金融产品或服务风险等级、投资期限、投资品种相匹配。</p>
        <p>本司就上述适当性评估结果与您进行确认，并建议您审慎考察该产品或服务的特征及风险，进行充分风险评估，自行做出投资决定。</p>
        </template>
        <template v-else>
        <p>本司已经向您充分揭示了该金融产品或服务的风险。您的风险承受能力等级、拟投资期限、投资品种、预期收益等投资目标与该金融产品或服务风险等级、投资期限、投资品种不匹配。该服务可能无法满足您预期的流动性需求或导致其他额外风险；投资该项产品，可能导致高出您自身承受能力的损失。</p>
        <p>本司就上述情况向您做出提示，并建议您应当审慎考察该产品或服务的特征及风险，自行做出充分风险评估。若您经审慎考虑后，仍坚持投资该产品，请点击确认</p>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import matchImg from '@/assets/images/appro_ok.png'
import notMatchImg from '@/assets/images/appro_error.png'

export default {
  props: ['pageParam'],
  data () {
    return {
      matchImg: matchImg,
      notMatchImg: notMatchImg,
      isChecked: false
    }
  },
  computed: {},
  activated () {
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    setTimeout(() => {
      this.$bus.emit('sdxParam', this.pageParam[0]) // 通过vuebus调用
    }, 200)
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {
        sdxCheck: {
          matchingFlag: this.pageParam[0].matchingFlag,
          riskLevelDesc: this.pageParam[0].riskLevelDesc,
          riskScore: parseInt(this.pageParam[0].paperScore),
          proRiskLevelDesc: this.pageParam[0].proRiskLevelDesc // 服务风险等级
        }
      }
      return formData
    },
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    }
  }
}
</script>

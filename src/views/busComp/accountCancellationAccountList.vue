/* * 选择账户 - 销户 * @Date: 2020-08-20 19:35:17 * @Last Modified by: Way *
@Last Modified time: 2020-11-02 16:07:57 */

<template>
  <div>
    <div class="xh_acct_box">
      <div class="xh_acct_cont">
        <div class="acct_ctitem">
          <ul class="acct_ctlist">
            <li>
              <span
                class="icon_check "
                :class="{ checked: isCloseAll }"
                @click.stop="closeAll"
              ></span>
              <p>客户号：{{ clientId }}</p>
            </li>
          </ul>
        </div>
        <p class="imp_txttips">
          若选客户号即一站式账户，该客户号关联的所有股东账户、场内基金账户、基金账户、OTC账户、资金账户默认全销。
        </p>
      </div>

      <div
        class="acct_ctitem"
        v-if="accountCancellationStockAccountList.length > 0"
      >
        <h5 class="title" :class="isShowStockList ? 'on' : ''">
          证券账户
          <i @click.stop="isShowStockList = !isShowStockList" class="arrow"></i>
        </h5>
        <ul class="acct_ctlist" v-show="isShowStockList">
          <li
            v-for="(d, index) in accountCancellationStockAccountList"
            :key="index"
          >
            <span
              class="icon_check "
              @click.stop="selClick(d)"
              :class="{ checked: selectedAccounts.indexOf(d) != -1 }"
            ></span>
            <p>{{ getMarketName(d.market) }}:{{ d.account }}</p>
            <div class="dt_span">
              同步中登
              <div class="switch">
                <input
                  type="checkbox"
                  :checked="isCheckZdFlag(d)"
                  @click.stop="syncHandle(d)"
                />
                <div class="switch-inner">
                  <div class="switch-arrow"></div>
                </div>
              </div>
            </div>
            <p v-show="d.holderStatus === dormantAcc" class="li_txttips">
              账户小额休眠，须同步注销中登账号
            </p>
          </li>
        </ul>
        <!-- <div class="switch_item">
          <p>同步中登销户</p>
          <div class="switch">
            <input type="checkbox" @change="chooseZdClick($event)" />
            <div class="switch-inner">
              <div class="switch-arrow"></div>
            </div>
          </div>
        </div> -->
        <p class="imp_txttips">
          {{ SynchronizeLoginPrompt }}
        </p>
      </div>

      <div
        class="acct_ctitem"
        v-if="accountCancellationFundAccountList.length > 0"
      >
        <h5 class="title" :class="isShowFundList ? 'on' : ''">
          基金账户
          <i @click.stop="isShowFundList = !isShowFundList" class="arrow"></i>
        </h5>
        <ul class="acct_ctlist" v-show="isShowFundList">
          <li
            v-for="(d, index) in accountCancellationFundAccountList"
            @click.stop="selClick(d)"
            :key="index"
          >
            <span
              class="icon_check"
              :class="{ checked: selectedAccounts.indexOf(d) != -1 }"
            ></span>
            <p>{{ d.account }}：{{ d.accountName }}</p>
          </li>
        </ul>
      </div>
      <div class="acct_ctitem" v-if="accountCancellationAssetList.length > 0">
        <h5 class="title" :class="isShowAssetList ? 'on' : ''">
          资金账户
          <i @click.stop="isShowAssetList = !isShowAssetList" class="arrow"></i>
        </h5>
        <ul class="acct_ctlist" v-show="isShowAssetList">
          <li
            v-for="(item, index) in accountCancellationAssetList"
            :key="index"
          >
            <span
              class="icon_check "
              :class="{ checked: selectedAccounts.indexOf(item) != -1 }"
              @click.stop="selClick(item)"
            ></span>
            <p>{{ item.mainFlag == '1' ? '主' : '辅' }}：{{ item.account }}</p>
            <div
              class="bank_input"
              @click.stop
              autocomplete="off"
              v-show="item.needPassword"
            >
              <span class="tit">银行密码：</span>
              <input
                class="t1"
                type="password"
                maxlength="6"
                placeholder="输入银行密码"
                v-model="item.bankPassword"
              />
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { ecbEncrypt } from 'thinkive-hvue/plugin/sm/sm4'
import { queryBankList } from '@/service/comServiceNew'
import { registerNativeBack } from '@/common/util'
export default {
  props: ['pageParam'],
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      clientId: '', // 客户号
      selectedAccounts: [], // 已选择的账户
      isCloseAll: false, // 是否全部注销
      chooseZd: '0', // 默认是0
      // 客户号
      accountCancellationClientList: [],
      // 资金账号
      accountCancellationAssetList: [],
      // 股东户
      accountCancellationStockAccountList: [],
      // 基金户
      accountCancellationFundAccountList: [],
      // otc
      accountCancellationOtcAccountList: [],
      accountLength: 0,
      bankInfoList: [],
      isShowStockList: true,
      isShowFundList: true,
      isShowAssetList: true,
      dormantAcc: '6' // 小额休眠字段值
    }
  },
  activated () {
    // invokeNative('8011', '000', {}, data => {
    //   console.log('8011首页回调结果', data)
    //   data = JSON.parse(data)
    // })
    // window.androidAppBack = this.$parent.back
    registerNativeBack({
      callback: this.pageBack
    })
    this.$store.commit('updateFlowParamDelay', true) // 开启延迟提交参数
    this.isCloseAll = false
    this.selectedAccounts = []
    this.chooseZd = '0'
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到信息', title: '提示' })
      return
    }

    // 获取银行信息列表
    queryBankList({}).then(res => {
      const results = res.newBankVerify || res.results
      if (results.length > 0) {
        this.bankInfoList = results
        this.accountListHandler()
      }
    })
  },
  deactivated () {
    this.$store.commit('updateFlowParamDelay', false) // 关闭延迟提交参数
  },
  computed: {
    // 计算属性的 getter
    SynchronizeLoginPrompt: function() {
      // `this` 指向 vm 实例
      return window.SynchronizeLoginPrompt
        ? window.SynchronizeLoginPrompt
        : '开启同步中登系统销户后，若销户成功则后续将无法再使用此账户交易。深A账户可下挂至多家券商，注销深A账户会导致下挂在其他券商的该账户不可用，请您仔细确认。'
    }
  },
  methods: {
    getMarketName(m) {
      if (m == '10') {
        return '沪A'
      } else if (m == '98') {
        return '沪封基'
      } else if (m == '99') {
        return '深封基'
      } else {
        return '深A'
      }
    },
    pageBack () {
      this.$parent.back()
    },
    isCheckZdFlag (item) {
      // this.selectedAccounts.forEach((ite, index) => {
      //   if (ite.account == item.account) {

      //   }

      // })

      return item.chooseZd && this.selectedAccounts.indexOf(item) != -1
    },
    chooseZdClick (e) {
      if (e.target.checked === false) {
        this.chooseZd = '0'
      } else {
        this.chooseZd = '1'
      }
    },
    // 转换结果集格式
    accountListHandler () {
      this.accountCancellationClientList = this.pageParam[0]
        .accountCancellationClientList
        ? JSON.parse(this.pageParam[0].accountCancellationClientList)
        : []
      this.accountCancellationAssetList = this.pageParam[0]
        .accountCancellationAssetList
        ? JSON.parse(this.pageParam[0].accountCancellationAssetList)
        : []
      this.accountCancellationStockAccountList = this.pageParam[0]
        .accountCancellationStockAccountList
        ? JSON.parse(this.pageParam[0].accountCancellationStockAccountList)
        : []
      this.accountCancellationFundAccountList = this.pageParam[0]
        .accountCancellationFundAccountList
        ? JSON.parse(this.pageParam[0].accountCancellationFundAccountList)
        : []
      this.accountCancellationOtcAccountList = this.pageParam[0]
        .accountCancellationOtcAccountList
        ? JSON.parse(this.pageParam[0].accountCancellationOtcAccountList)
        : []
      this.clientId =
        this.accountCancellationClientList[0].clientId ||
        this.accountCancellationClientList[0].account

      for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
        let el = this.accountCancellationAssetList[i]
        el.checked = false
        if (
          this.bankInfoList.some(item => {
            return el.bankNo && item.bankNo === el.bankNo && item.isPwd === '1'
          })
        ) {
          el.needPassword = true
        }
        this.$set(this.accountCancellationAssetList, i, el)
      }
      for (
        let i = 0;
        i < this.accountCancellationStockAccountList.length;
        i++
      ) {
        let el = this.accountCancellationStockAccountList[i]
        el.checked = false
        el.chooseZd = false
        this.$set(this.accountCancellationStockAccountList, i, el)
      }
      for (let i = 0; i < this.accountCancellationFundAccountList.length; i++) {
        let el = this.accountCancellationFundAccountList[i]
        el.checked = false
        this.$set(this.accountCancellationFundAccountList, i, el)
      }
      /* for (let i = 0; i < this.accountCancellationOtcAccountList.length; i++) {
        let el = this.accountCancellationOtcAccountList[i]
        el.checked = false
        this.$set(this.accountCancellationOtcAccountList, i, el)
      } */
    },
    syncHandle (item) {
      // item.chooseZd = !item.chooseZd;
      let chooseZd = !item.chooseZd
      // this.accountCancellationStockAccountList.forEach((ite, index) => {
      //   if (ite.account == item.account) {
      //     ite.chooseZd = chooseZd
      item.chooseZd = chooseZd
      //   }
      //   this.$set(this.accountCancellationStockAccountList, index, ite)
      // })

      if (this.selectedAccounts.length <= 0) {
        item.chooseZd = true
        this.selectedAccounts.push(item)
      } else {
        let flag = false
        for (let index = 0; index < this.selectedAccounts.length; index++) {
          const element = this.selectedAccounts[index]
          if (element.account == item.account) {
            element.chooseZd = chooseZd
            this.$set(this.selectedAccounts, index, element)
            return false
          } else {
            flag = true
          }
        }
        if (flag) {
          item.chooseZd = true
          this.selectedAccounts.push(item)
        }
      }

      let selStockNum = 0
      this.selectedAccounts.forEach(it => {
        if (it.accountType === '2') {
          selStockNum++
        }
      })
      if (selStockNum == this.accountCancellationStockAccountList.length) {
        this.closeAll()
      }
    },
    // 点击选择账户
    selClick (item) {
      if (item.accountType !== '0' && item.accountType !== '2') {
        return
      }
      // 如客户勾选注销客户号时，则所有客户号下的账户自动全选，且不可取消勾选；
      if (this.isCloseAll) return
      let accountList = this.accountCancellationClientList
        .concat(this.accountCancellationAssetList)
        .concat(this.accountCancellationStockAccountList)
        .concat(this.accountCancellationFundAccountList)
        .concat(this.accountCancellationOtcAccountList)
      // 勾选主资金账号，除客户号外其他账号都勾选
      if (
        item.accountType === '1' &&
        item.mainFlag === '1' &&
        !this.selectedAccounts.includes(item)
      ) {
        this.closeAll()
        return
      }
      // 取消勾选主资金账号，其他账号都取消勾选
      /* if (
        item.accountType === '1' &&
        item.mainFlag === '1' &&
        this.selectedAccounts.includes(item)
      ) {
        this.closeAll()
        return
      }
      取消勾选其他账号，则取消勾选主资金账号
      if (item.accountType !== '1' && this.selectedAccounts.includes(item)) {
        this.isCloseAll = false
        for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
          let el = this.accountCancellationAssetList[i]
          if (el.mainFlag === '1') this.selectedAccounts.remove(el)
        }
      } */
      if (this.selectedAccounts.includes(item)) {
        if (item.accountType === '0') {
          // 选中客户号
          this.closeAll()
        }
        if (item.accountType === '2') {
          // 证券账户
          this.selectedAccounts.remove(item)
          for (let i = 0; i < accountList.length; i++) {
            let el = accountList[i]
            if (el.accountType !== '2') this.selectedAccounts.remove(el)
          }
          this.isCloseAll = false
          /* for (let i = 0; i < this.accountCancellationStockAccountList.length; i++) {
            let el = this.accountCancellationStockAccountList[i]
            if (el.account == item.account) {
              el.chooseZd = false
            }
            this.$set(this.accountCancellationStockAccountList, i, el)
          }
          this.isCloseAll = false
          this.closeAll() */
        }
      } else {
        if (item.accountType === '0' || item.accountType === '2') {
          if (item.accountType === '2') {
            item.chooseZd = true
          }
          this.selectedAccounts.push(item)
        }
      }
      let selStockNum = 0
      this.selectedAccounts.forEach(item => {
        if (item.accountType === '2') {
          selStockNum++
        }
      })
      /* 1）如客户号下有状态正常的OTC或基金户则可勾选注销沪A、深A账户（单选或多选均可）；
        2）如客户号下除股东账户外，没有OTC或基金户，则注销全部股东账户时，系统自动勾选注销客户号
        3）如客户号下除股东账户外，OTC或基金户均为注销中，则视为情况2，注销全部股东账户时，系统自动勾选注销客户号 */
      if (item.accountType === '2') {
        const checkList = this.accountCancellationFundAccountList.concat(this.accountCancellationOtcAccountList)
        const jjFlag = checkList.some(
          ({ holderStatus }) => holderStatus === '0'
        )
        if (
          !jjFlag &&
          selStockNum === this.accountCancellationStockAccountList.length
        ) {
          this.closeAll()
        }
      }
    },
    // 点击全部注销
    closeAll (isClickAll) {
      this.isCloseAll = !this.isCloseAll
      this.setAllOpen(this.isCloseAll, isClickAll)
    },
    setAllOpen (flag, isClickAll) {
      isClickAll = !!isClickAll
      for (let i = 0; i < this.accountCancellationAssetList.length; i++) {
        let el = this.accountCancellationAssetList[i]
        let method = flag ? 'push' : 'remove'
        this.selectedAccounts[method](el)
      }
      for (
        let i = 0;
        i < this.accountCancellationStockAccountList.length;
        i++
      ) {
        let el = this.accountCancellationStockAccountList[i]
        let method = flag ? 'push' : 'remove'

        if (isClickAll) {
          el.chooseZd = true
        }
        this.selectedAccounts[method](el)
      }
      for (let i = 0; i < this.accountCancellationFundAccountList.length; i++) {
        let el = this.accountCancellationFundAccountList[i]
        let method = flag ? 'push' : 'remove'
        this.selectedAccounts[method](el)
      }
      /* for (let i = 0; i < this.accountCancellationOtcAccountList.length; i++) {
        let el = this.accountCancellationOtcAccountList[i]
        let method = flag ? 'push' : 'remove'
        this.selectedAccounts[method](el)
      } */
      this.selectedAccounts = [...new Set(this.selectedAccounts)] // 去重
    },
    checkSubmit () {
      if (this.selectedAccounts.length === 0) {
        _hvueToast({
          mes: '请选择账户'
        })
        return false
      }
      // 选择了股东户销户则判断是否选择了中登销户
      for (let i = 0; i < this.selectedAccounts.length; i++) {
        let el = this.selectedAccounts[i]
        if (el.accountType === '2') {
          el.zdCancellationFlag = el.chooseZd == true ? '1' : '0'
          if (el.chooseZd == true) {
            this.chooseZd = '1'
          }
        }
        this.$set(this.selectedAccounts, i, el)
      }
      // if (
      //   this.chooseZd === '' &&
      //   this.selectedAccounts.some(item => {
      //     return item.accountType === '2'
      //   })
      // ) {
      //   _hvueToast({
      //     mes: '请选择是否中登销户'
      //   })
      //   return false
      // }
      // 选择资金账户时校验银行卡密码输入
      const selectedCapitalAccount = this.selectedAccounts.filter(item => {
        return this.accountCancellationAssetList.some(subItem => {
          return subItem.account === item.account
        })
      })
      if (
        selectedCapitalAccount.length > 0 &&
        selectedCapitalAccount.some(item => {
          return item.needPassword && !item.bankPassword
        })
      ) {
        _hvueToast({
          mes: '请输入银行卡密码'
        })
        return false
      }
      if (
        selectedCapitalAccount.length > 0 &&
        selectedCapitalAccount.some(item => {
          return item.needPassword && !item.bankPassword.match(/^[0-9]{6}$/)
        })
      ) {
        _hvueToast({
          mes: '银行卡密码格式错误，请重新输入'
        })
        return false
      }
      let _this = this
      const dormantAccZDFlag = _this.selectedAccounts.some(
        ({ holderStatus, chooseZd, accountType }) =>
          accountType === '2' && holderStatus === _this.dormantAcc && !chooseZd
      )
      if (dormantAccZDFlag) {
        _hvueConfirm({
          mes:
            '（沪A/深A）股东账号状态为小额休眠，须同时注销中登账号或临柜激活。请确认是否需要同时注销中登账户。',
          opts: [
            {
              txt: '取消',
              color: false
            },
            {
              txt: '注销中登',
              color: true,
              callback: () => {
                _this.accountCancellationStockAccountList = _this.accountCancellationStockAccountList.map(
                  d => {
                    d.chooseZd = d.holderStatus === _this.dormantAcc
                    return d
                  }
                )
                _this.$parent.emitNextEvent()
              }
            }
          ]
        })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          resolve()
        } else {
          reject(new Error({ mes: '表单校验不通过' }))
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      const accountTypeMap = {
        '0': '客户号',
        '1': '资金账户',
        '2': '证券账户',
        '3': '基金账户'
      }
      const sm4Key = $hvue.config.sm4Key
      this.selectedAccounts.forEach(item => {
        this.accountCancellationAssetList.some(subItem => {
          if (subItem.account === item.account && subItem.needPassword) {
            item.bankPassword = ecbEncrypt(
              sm4Key.substring(8, sm4Key.length - 8),
              subItem.bankPassword
            )
          }
        })
      })
      let selectedAccount = this.selectedAccounts
      if (this.isCloseAll) {
        selectedAccount.push(...this.accountCancellationClientList)
      }
      let cancelAccountList = []
      selectedAccount
        .sort((a, b) => {
          return +a.accountType - +b.accountType
        })
        .map(item => {
          return {
            accountType: accountTypeMap[item.accountType],
            account: item.account
          }
        })
        .forEach(item => {
          if (
            cancelAccountList.every(subItem => {
              return item.accountType !== subItem.accountType
            })
          ) {
            cancelAccountList.push(item)
          } else {
            cancelAccountList.forEach(subItem => {
              if (item.accountType === subItem.accountType) {
                subItem.account += `、${item.account}`
              }
            })
          }
        })
      cancelAccountList = cancelAccountList
        .map(item => {
          return `${item.accountType}：${item.account}`
        })
        .join('；')

      // 业务请求参数封装
      let params = {
        mobile: this.ygtUserInfo.mobile,
        accountInfo: JSON.stringify(selectedAccount),
        zdCancellationFlag: this.chooseZd,
        isOneStop: this.isCloseAll ? '1' : '0',
        cancelAccountList
      }
      return params
    },
    // 表单参数打包
    putFormData () {
      let selectedAccount = this.selectedAccounts
      if (this.isCloseAll) {
        selectedAccount.push(...this.accountCancellationClientList)
      }
      let formData = {
        cancellationChooseAccount: JSON.stringify(selectedAccount)
      }
      return formData
    }
  }
}
</script>

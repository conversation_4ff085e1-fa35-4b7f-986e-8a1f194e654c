<template>
  <div>
    <headComponent headerTitle="视频见证"></headComponent>
    <div v-show="isOneVideo">
      <article class="content">
        <div v-show="videoState == 0">
          <h5 class="com_title spel">
            <i class="icon_ling"></i
            >根据监管要求，销户需要获取投资者人脸信息，并向投资者核实身份真实性、销户意愿真实性
          </h5>
          <div class="witness_box">
            <h5>请进行人脸识别来认证身份</h5>
            <p>为了确保本人操作我们将进行人脸识别</p>
            <div class="dx_tip_pic">
              <img src="@/assets/images/video_face2.png" />
            </div>
            <div class="dx_tipbox">
              <p>视频环节请注意以下事项：</p>
              <ul>
                <li>
                  <i class="icon"><img src="@/assets/images/fc_tp01.png"/></i
                  ><span>确保光线清晰</span>
                </li>
                <li>
                  <i class="icon"><img src="@/assets/images/fc_tp02.png"/></i
                  ><span>远离嘈杂环境</span>
                </li>
                <li>
                  <i class="icon"><img src="@/assets/images/fc_tp03.png"/></i
                  ><span>不能带帽子</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="fc_sbbox" v-show="videoState == 5">
          <div class="fc_imgbox ing">
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <h5>识别中…</h5>
        </div>
        <div class="fc_sbbox" v-show="videoState == 1">
          <div class="fc_imgbox error">
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <h5 class="error">检测不通过</h5>
          <p>自拍照与证件照不是同一人</p>
        </div>
        <div class="fc_sbbox" v-show="videoState == 2">
          <div class="fc_imgbox ok">
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <h5>检测识别通过</h5>
        </div>
        <div class="fc_sbbox" v-show="videoState == 6">
          <div class="fc_imgbox ing">
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <h5>上传中</h5>
        </div>
      </article>
      <div class="ce_btn">
        <a
          v-throttle
          class="p_button"
          :class="{
            disabled: isSubmitting
          }"
          @click="getJwtToken(0)"
          v-show="videoState == 0"
          >开始认证</a
        >
        <a
          v-throttle
          class="p_button"
          :class="{
            disabled: isSubmitting
          }"
          @click="getJwtToken(0)"
          v-show="videoState == 1"
          >重新认证</a
        >
        <a
          v-throttle
          class="p_button"
          :class="{
            disabled: isSubmitting
          }"
          @click="getJwtToken(1)"
          v-show="videoState == 2 || videoState == 7"
          >开始录制</a
        >
      </div>
    </div>
    <div>
      <videoPage
        @videoCallBack="videoPageCallBack"
        :pageParam="pageParam"
        v-if="!isOneVideo"
      ></videoPage>
    </div>

    <!-- 人脸识别协议弹窗 -->
    <div v-if="showAgreementDialog" class="face_agreement_popup">
      <div class="popup_overlay"></div>
      <div class="popup_container">
        <div class="popup_content">
          <div class="content_title">尊敬的客户：</div>
          <div class="content_text">
            我们将向您申请收集您的人脸信息进行身份验证，在此之前请仔细阅读《<span class="agreement_link" @click="viewAgreement">东吴证券股份有限公司人脸识别验证服务协议（线上业务办理）</span>》，如您同意协议内容，请点击“同意”。我们将按照法律法规要求，采取各种合理必要的措施保障您的个人信息安全。
          </div>
          <div class="content_text">
            如果您不同意本协议的任何内容，或者无法准确理解条款含义，请不要签署本协议。但如果您不签署本协议进行人脸识别验证将可能导致业务办理失败。
          </div>
        </div>
        <div class="popup_buttons">
          <button class="btn_disagree" @click="disagreeAgreement">不同意</button>
          <button class="btn_agree" @click="agreeAgreement">同意</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import {
  faceCcompare,
  videoUpload,
  videoBase64Upload,
  getOneWitnessToken
} from '@/service/comServiceNew'
import { goBackXcApp, registerNativeBack } from '@/common/util'
import videoPage from '@/components/videoPage'
export default {
  props: ['pageParam'],
  name: 'oneOrTwoVideoPage',
  components: {
    headComponent,
    videoPage
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      businessCode: this.$route.query.type,
      videoState: 0, //0活体准备中1活体检测失败2活体通过
      liveFailCount: 0,
      uploadRequest: '', // 上传视频 请求（用于取消）
      videoPath: '',
      jwtToken: '',
      flowNo: '',
      regFlowNo: '',
      oneWayPass: '0',
      actionType: '',
      actionArr: ['眨眼', '点头', '摇头', '张嘴'],
      actionArrEn: ['blink', 'nod', 'yaw', 'mouth'],
      isOneVideo: true,
      publicParam: this.$parent.publicParam,
      flow: this.$parent.flow,
      isSubmitting: false,
      showAgreementDialog: true // 控制协议弹窗显示，页面加载时自动显示
    }
  },
  methods: {
    rejectFlow(rejectName) {
      this.$parent.rejectFlow(rejectName)
    },
    rollback(backStepName) {
      this.$parent.rollback(backStepName)
    },
    // 返回
    pageBack() {
      _hvueConfirm({
        mes: '视频认证未完成，是否返回首页？',
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              console.log('oneOrTwoVideoPage确认弹窗 - 注册返回方法')
              registerNativeBack({
                callback: this.pageBack
              })
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 返回首页
              $hvue.env == 'ths'
                ? goBackXcApp()
                : this.$router.push({ name: 'index' })
            }
          }
        ]
      })
    },
    start() {
      if (this.jwtToken) {
        this.livingThing()
      } else {
        _hvueAlert({
          mes: '抱歉，token失效,重新获取token',
          callback: () => {
            this.isSubmitting = false
            this.getJwtToken(0)
          }
        })
      }
    },
    livingThing() {
      let randomAction = this.getRandAction(2)
      let actionType = randomAction
      this.actionArr.forEach((item, i) => {
        if (randomAction.includes(item)) {
          actionType = actionType.replace(item, this.actionArrEn[i])
        }
      })
      let param = {
        fixedAction: '眨眼',
        randomAction: randomAction,
        randomActionCount: 3,
        singleCheckTime: 15
      }
      this.actionType = actionType
      console.log('actionType', this.actionType)
      invokeNative('8008', '000', param, data => {
        this.isSubmitting = false
        console.log('回调结果', data)
        data = JSON.parse(data)
        this.livingThingCallBack(data)
      })
    },
    async getJwtToken(type) {
      if (this.isSubmitting) return
      this.isSubmitting = true
      // 设置定时器，3秒后自动重置提交状态
      setTimeout(() => {
        this.isSubmitting = false
      }, 3000)
      try {
        const data = await getOneWitnessToken({
          serivalId: this.$parent.publicParam.serivalId,
          userId: this.ygtUserInfo.userId,
          clientId: this.ygtUserInfo.clientId
        })
        if (data.error_no == 0) {
          this.jwtToken = data.DataSet[0].jwtToken
          this.flowNo = data.DataSet[0].flowNo
          if (type == 0) {
            this.start()
          } else if (type == 1) {
            this.oneWitnessEvent()
          }
        } else {
          _hvueAlert({ mes: data.error_info })
          this.isSubmitting = false
        }
      } catch (error) {
        this.isSubmitting = false
        _hvueAlert({ mes: error })
      }
    },
    uploadImag(base64) {
      var u = navigator.userAgent
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
      var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //android终端
      let origin = '3'
      if (isiOS) {
        origin = '2'
      } else if (isAndroid) {
        origin = '1'
      }
      faceCcompare({
        businessCode: this.businessCode,
        actionType: this.actionType,
        base64Image: encodeURI(
          base64.indexOf('base64,') > 0 ? base64.split('base64,')[1] : base64
        ),
        userId: this.ygtUserInfo.userId,
        jwtToken: this.jwtToken,
        serivalId: this.$parent.publicParam.serivalId,
        flowNo: this.flowNo,
        origin: origin
      }).then(res => {
        if (res.error_no == '0') {
          let result = res.DataSet[0]
          if (
            result.pass == '1' ||
            result.pass == 'true' ||
            result.pass == true
          ) {
            this.videoState = 2
            console.log(this.videoState)
          } else {
            this.liveFailCount++
            if (this.liveFailCount >= 3) {
              _hvueAlert({
                mes: '抱歉，人脸比对多次不通过，转双向视频',
                callback: () => {
                  this.oneWayPass = '0'
                  // this.nextClick()
                  this.isOneVideo = false
                }
              })
              return
            } else {
              // _hvueAlert({
              //   mes: '抱歉，人脸比对不通过，重新检测',
              //   callback: () => {
              //     this.videoState = 1
              //   }
              // })
              // return
              this.videoState = 1
              console.log(this.videoState)
              return
            }
          }
        } else {
          _hvueAlert({
            mes: res.error_info,
            callback: () => {
              this.videoState = 1
              console.log(this.videoState)
            }
          })
          return
        }
      })
    },
    //活体检测回调
    livingThingCallBack(data) {
      if (data.code == '0') {
        console.log('上传中')
        this.uploadImag(data.imageData)
      } else {
        this.liveFailCount++
        if (this.liveFailCount >= 3) {
          _hvueAlert({
            mes: '抱歉，人脸比对多次不通过，转双向视频',
            callback: () => {
              this.oneWayPass = '0'
              // this.nextClick()
              this.isOneVideo = false
            }
          })
          return
        } else {
          _hvueAlert({
            mes: data.errorInfo || '活体检测失败',
            callback: () => {}
          })
          return
        }
      }
    },
    //开启视频录制
    oneWitnessEvent() {
      // this.videoState = 7
      console.log(this.videoState)
      let param = {
        jwtToken: this.jwtToken,
        terminalDomainName: location.host,
        // readContext: `请问您是${this.ygtUserInfo.name}本人。并已知晓证券市场风险、阅读且充分理解销户协议条款,自愿在东吴证券销户的吗?`,
        readContext: `请问您是${this.ygtUserInfo.name}本人，已阅读并了解《非现场注销账户业务须知》内容及风险，自愿办理账户注销业务吗？`,
        // tipsContext: `我是${this.ygtUserInfo.name}已知晓证券市场风险,已阅读并充分理解网上销户协议条款，自愿选择在东吴证券销户，并承诺所提供的信息及证件真实、合法、有效。`,
        tipsContext: `我是${this.ygtUserInfo.name}本人，已阅读并了解《非现场注销账户业务须知》内容及风险，自愿办理账户注销业务。`,
        bizName: '销户'
      }
      invokeNative('8009', '000', param, data => {
        this.isSubmitting = false
        console.log('单项视频录制结果', data)
        data = JSON.parse(data)

        this.oneWayVideoCallBack(data)
      })
    },
    //单项视频回调
    oneWayVideoCallBack(data) {
      console.info('oneWayVideoCallBack ==== ', data)
      if (data.code == '0') {
        this.videoState = '6'
        this.videoPath = data.videoPath
        this.videoBase64Upload()
      } else if (data.code == '3') {
        _hvueAlert({
          mes: '抱歉，人脸比对多次不通过，转双向视频',
          callback: () => {
            this.oneWayPass = '0'
            // this.nextClick()
            this.isOneVideo = false
          }
        })
        return
      } else {
        this.videoState = '0'
      }
    },
    videoBase64Upload() {
      this.oneWayPass = '1'
      this.nextClick()
      /* videoBase64Upload({
        jwtToken: this.jwtToken,
        userId: this.ygtUserInfo.userId,
        businessCode: this.businessCode,
        videoPath: this.videoPath,
        serivalId: this.$parent.publicParam.serivalId
      }).then(res => {
        if (res.error_no == '0') {
          console.log('单向视频上传成功')
          this.oneWayPass = '1'
          this.nextClick()
        } else {
          this.videoState = '0'
          console.log(this.videoState)
          this.isVideoPage = false
          _hvueAlert({
            mes: res.error_info
          })
          return
        }
      }) */
    },
    getRandAction(num) {
      let actionArr = JSON.parse(JSON.stringify(this.actionArr))
      let resultArr = []
      if (!num) num = 1
      if (num > actionArr.length) num = actionArr.length
      for (let i = 0; i < num; i++) {
        var rand = Math.floor(Math.random() * actionArr.length)
        resultArr.push(actionArr.splice(rand, 1))
      }
      return resultArr.join('|')
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    videoPageCallBack({ regFlowNo, toOneVideo = false }) {
      if (toOneVideo === true) {
        this.isOneVideo = true
        return
      }
      console.info('videoPage组件返回信息 ==== ', regFlowNo)
      this.regFlowNo = regFlowNo
      this.nextClick()
    },
    nextClick() {
      this.$parent.emitNextEvent()
    },
    // 显示协议弹窗
    showAgreementPopup() {
      this.showAgreementDialog = true
    },
    // 关闭协议弹窗
    closeAgreementDialog() {
      this.showAgreementDialog = false
    },
    // 查看协议详情
    viewAgreement() {
      // 这里可以跳转到协议详情页面或者打开新的弹窗显示协议内容
      _hvueAlert({
        mes: '协议详情功能待实现'
      })
    },
    // 不同意协议
    disagreeAgreement() {
      this.showAgreementDialog = false
      _hvueAlert({
        title: '操作失败',
        mes: '线上业务办理需要使用您的人脸信息进行身份验证。因您对《东吴证券股份有限公司人脸识别验证服务协议（线上业务办理）》仍有疑问，可线下至就近营业部柜面继续办理。'
      })
    },
    // 同意协议
    agreeAgreement() {
      this.showAgreementDialog = false
      // 同意协议后继续原有的认证流程
      this.getJwtToken(0)
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {}
      if (this.oneWayPass === '1') {
        params.userId = this.ygtUserInfo.userId
        params.jwtToken = this.jwtToken
        params.videoPath = this.videoPath
        params.serivalId = this.$parent.publicParam.serivalId
        params.videoType = '1'
      } else {
        params.regFlowNo = this.regFlowNo
        params.videoType = '2'
      }
      console.info('oneOrTwoVideoPage reqParamPackage ==== ', params)
      return params
    },
    putFormData() {
      // 需要保存的表单数据
      let formData = {}
      if (this.oneWayPass === '1') {
        formData.userId = this.ygtUserInfo.userId
        formData.jwtToken = this.jwtToken
        formData.videoPath = this.videoPath
        formData.serivalId = this.$parent.publicParam.serivalId
        formData.videoType = '1'
      } else {
        formData.regFlowNo = this.regFlowNo
        formData.videoType = '2'
      }
      console.info('oneOrTwoVideoPage putFormData ==== ', formData)
      return formData
    }
  },
  activated() {
    console.log('oneOrTwoVideoPage - 注册返回方法')
    registerNativeBack({
      callback: this.pageBack
    })
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    let isOneWayVideo = this.pageParam[0].isOneWayVideo
    if (isOneWayVideo == '1') {
      this.isOneVideo = true
    } else {
      this.isOneVideo = false
    }
    // window.androidAppBack = this.pageBack
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    // this.getJwtToken(3)
    console.log(this.videoState)
  },
  deactivated() {
    this.videoState = 0
    this.liveFailCount = 0
    this.isOneVideo = true
  }
}
</script>

<style scoped>
/* 人脸识别协议弹窗样式 - 严格按照图片样式 */
.face_agreement_popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.popup_overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.popup_container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  border-radius: 0.12rem 0.12rem 0 0;
  padding: 0.24rem 0.2rem 0.2rem;
  box-shadow: 0 -0.02rem 0.1rem rgba(0, 0, 0, 0.1);
}

.popup_header {
  text-align: center;
  margin-bottom: 0.2rem;
}

.header_icon {
  width: 0.6rem;
  height: 0.6rem;
  margin: 0 auto 0.12rem;
  border-radius: 50%;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header_icon img {
  width: 0.4rem;
  height: 0.4rem;
  object-fit: cover;
}

.popup_header h3 {
  font-size: 0.18rem;
  font-weight: 500;
  color: #333333;
  margin: 0;
  line-height: 0.24rem;
}

.popup_content {
  margin-bottom: 0.24rem;
}

.content_title {
  font-size: 0.16rem;
  font-weight: 500;
  color: #333333;
  margin-bottom: 0.12rem;
  line-height: 0.22rem;
}

.content_text {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #666666;
  margin-bottom: 0.12rem;
  text-align: justify;
}

.content_text:last-child {
  margin-bottom: 0;
}

.agreement_link {
  color: #007AFF;
  text-decoration: none;
  cursor: pointer;
}

.agreement_link:active {
  opacity: 0.7;
}

.popup_buttons {
  display: flex;
  gap: 0.12rem;
}

.btn_disagree,
.btn_agree {
  flex: 1;
  height: 0.44rem;
  border: none;
  border-radius: 0.06rem;
  font-size: 0.16rem;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s;
}

.btn_disagree {
  background: #f5f5f5;
  color: #666666;
}

.btn_agree {
  background: #007AFF;
  color: #ffffff;
}

.btn_disagree:active,
.btn_agree:active {
  opacity: 0.7;
}
</style>

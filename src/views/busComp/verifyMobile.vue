<!-- 找回账户-手机号验证 -->
<template>
  <div>
    <h5 class="com_title" v-show="canChangeMobile">请填写您预留的手机号码</h5>
    <div class="input_form mobile_form">
      <div class="ui field text">
        <label class="ui label">手机号</label>
        <input
          ref="mobileInput"
          type="text"
          class="ui input"
          placeholder="请输入手机号"
          maxlength="11"
          v-model="mobile"
          :disabled="!canChangeMobile"
          @input="checkInputComplete"
        />
        <a
          class="txt_close"
          @mousedown.stop.prevent="clearInput('mobile')"
          v-show="mobile != '' && canChangeMobile"
        ></a>
      </div>
      <div class="ui field text code">
        <label class="ui label">图片验证码</label>
        <input
          ref="imgCodeInput"
          type="text"
          class="ui input"
          placeholder="请输入图片验证码"
          maxlength="4"
          v-model="imgCode"
          @input="checkInputComplete"
        />
        <a class="code_img" href="javascript:;" @click="getImgCode">
          <img :src="codeImgUrl">
        </a>
        <a
          class="txt_close"
          @mousedown.stop.prevent="clearInput('imgCode')"
          v-show="imgCode != ''"
        ></a>
      </div>
      <div class="ui field text code">
        <label class="ui label">验证码</label>
        <input
          ref="verifyCodeInput"
          type="number"
          class="ui input"
          placeholder="请输入手机验证码"
          maxlength="6"
          v-model="verifyCode"
          @input="checkInputComplete"
        />
        <a
          class="txt_close"
          @mousedown.stop.prevent="clearInput('verifyCode')"
          v-show="verifyCode != ''"
        ></a>
        <smsTimer v-model="startFlag" @sendSms="getVerifyCode"></smsTimer>
      </div>
    </div>
    <!-- <p class="bot_tips mt10">温馨提示：如果预留手机号不正确或无法接收验证码，请致电40088-xxxxxx转人工服务或者前往就近营业部办理</p> -->
    <div class="ce_btn mt20">
      <a class="ui button block rounded" :class="{ disabled: nextStepBtnDisabled }" @click.stop="nextStep"
        >下一步</a
      >
    </div>
  </div>
</template>
<script>
import smsTimer from '../../components/smsTimer'
import { queryDictionary } from '@/common/util'
import {
  sendMobileMsg,
  getImgCode,
  verifyImgCode } from '@/service/comServiceNew'

export default {
  props: ['pageParam'],
  components: {
    smsTimer
  },
  data() {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      handleError: true,
      initData: {},
      canChangeMobile: true,
      mobile: '',
      codeImgUrl: '', // 图片验证码图片url
      codeImgKey: '',
      imgCode: '', // 图形验证码
      startFlag: false, // 是否开始倒计时
      verifyCode: '', // 用户输入的验证码
      smsCodeMaxLength: 6,
      smsNo: '', // 短信类型数据字典
      sendSmsFlag: '', // 是否已经发送了验证码
      nextStepBtnDisabled: true
    }
  },
  watch: {
    verifyCode() {
      if (this.verifyCode.length > this.smsCodeMaxLength) {
        this.verifyCode = String(this.verifyCode).slice(
          0,
          this.smsCodeMaxLength
        )
      }
    }
  },
  activated() {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.initData = this.pageParam[0]
    this.mobile = this.pageParam[0].newMobileNo || this.pageParam[0].mobile || this.ygtUserInfo.mobile || ''
    if (this.pageParam[0].newMobileNo || this.pageParam[0].mobile) {
      this.canChangeMobile = false
    }
    this.getImgCode(true)
  },
  mounted() {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.sms_type', value: this.$parent.businessName },
      data => {
        this.smsNo = data.key
      }
    )
  },
  methods: {
    /** ************子组件公共方法定义****** */
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        userId: this.ygtUserInfo.userId,
        mobile: this.mobile,
        verifyCode: this.verifyCode,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type
      }
      return params
    },
    putFormData() {
      let formData = {}
      return formData
    },
    /** ************子组件公共方法定义****** */
    // 获取图片验证码
    getImgCode(isInit) {
      getImgCode({}, {}).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0]
            this.codeImgUrl = results.imageCode
            this.codeImgKey = results.mobileKey
            this.clearInput('imgCode')
            if (isInit && !this.mobile) {
              this.$refs.mobileInput.focus()
              return false
            }
            this.$refs.imgCodeInput.focus()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        }, err => {
          console.log(err)
        }
      )
    },
    // 校验图片验证码
    verifyImgCode(cb) {
      verifyImgCode(
        { mobileKey: this.codeImgKey, imageCode: this.imgCode }
      ).then(
        res => {
          if (res.error_no === '0') {
            cb && typeof cb === 'function' && cb()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
            this.getImgCode()
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 输入完整性检查
    checkInputComplete() {
      if (this.verifyInput()) {
        this.nextStepBtnDisabled = false
      } else {
        this.nextStepBtnDisabled = true
      }
    },
    // 输入内容校验
    verifyInput() {
      if (this.verifyCode && this.imgCode && this.mobile) {
        return true
      }
      return false
    },
    clearInput(item) {
      this[item] = ''
      this.checkInputComplete()
    },
    getVerifyCode() {
      if (!this.mobile) {
        _hvueToast({ mes: '请输入手机号' })
        this.$refs.mobileInput.focus()
        return false
      }
      if (!this.imgCode) {
        _hvueToast({ mes: '请输入图形验证码' })
        this.$refs.imgCodeInput.focus()
        return false
      }
      this.verifyImgCode(() => {
        // 获取短信验证码
        var params = {
          flow_name: this.$route.query.type,
          smsNo: this.smsNo,
          businessCode: this.$route.query.type,
          userId: this.ygtUserInfo.userId,
          mobile: this.mobile
        }
        sendMobileMsg(params).then(res => {
          if (res.error_no === '0') {
            // 短信验证码发送成功，开始倒计时
            this.startFlag = true
            this.sendSmsFlag = true
            this.$refs.verifyCodeInput.focus()
          } else {
            _hvueAlert({ title: '提示', mes: res.error_info })
          }
        })
      })
    },
    // 校验输入内容
    verifyInputValidity() {
      if (!this.mobile) {
        _hvueToast({ mes: '请输入手机号' })
        this.$refs.mobileInput.focus()
        return false
      }
      if (!this.imgCode) {
        _hvueToast({ mes: '请输入图形验证码' })
        this.$refs.imgCodeInput.focus()
        return false
      }
      // 判断是否已经发送了验证码
      if (!this.sendSmsFlag) {
        _hvueToast({ mes: '请先发送验证码' })
        return false
      }
      // 判断短信验证码是否过期
      if (this.sendSmsFlag && !this.startFlag) {
        _hvueToast({ mes: '短信验证码已过期，请重新获取' })
        return false
      }
      // 检查是否填写了验证码
      if (this.verifyCode === '') {
        _hvueToast({ mes: '请输入手机验证码' })
        this.$refs.verifyCodeInput.focus()
        return false
      }
      if (this.verifyCode.length !== 6) {
        _hvueToast({ mes: '请输入正确位数的手机验证码' })
        this.$refs.verifyCodeInput.focus()
        return false
      }
      return true
    },
    // 下一步
    nextStep() {
      if (!this.nextStepBtnDisabled) {
        if (this.verifyInputValidity()) {
          // 调用父组件的下一步事件
          this.$parent.emitNextEvent()
        }
      }
    },
    // 提交后报错处理
    handleErrorFunc() {
      this.getImgCode()
    },
    deactivated() {
      this.startFlag = false
    }
  }
}
</script>

<template>
  <div style="flex:1;overflow:hidden;">
    <article class="content">
      <div class="agree_main flex_vertical">
        <h5 class="title">请选择您想要开通的基金公司账户：</h5>
        <div class="fund_acctbox">
          <ul ref="fund_account_list">
            <template v-for="(item, index) in pageParam">
              <li :key="index">
                <span
                  @click.stop="item.openStatus === '0' && accSelEvent(item)"
                  :class="{
                    icon_check: item.openStatus === '0',
                    checked: selectedAccounts.includes(item)
                  }">
                  {{ item.exchangeName || item.fundCompany }}
                </span>
                <em v-if="item.openStatus === '1'">已开通：{{ item.transAccount }}</em>
              </li>
            </template>
          </ul>
        </div>
        <p class="no_moretips">
          <span>没有更多内容啦</span>
        </p>
      </div>
    </article>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      handleResult: true,
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      keywords: {},
      chooseAccount: [], // 需要保存的表单数据
      selectedAccounts: [], // 已选择的账户
      allClickFlag: false
    }
  },
  activated () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到基金列表', title: '提示' })
    }
    this.$store.commit('updateBusinessNextBtnStatus', '马上开通') // 隐藏下一步按钮
    this.$store.commit('updateIsComponentsFlexVertical', true)
    this.$store.commit('updateSelectedAccountsLength', 0) // 已勾选账户总数，存在vuex中

    // 注册 通过vuebus 接受agreementContent按钮组件的 参数修改账户勾选状态
    this.$bus.on('changeAllFlag', allFlag => {
      this.allClickFlag = allFlag
      let a = this.pageParam
      for (let r = 0; r < a.length; r++) {
        let accounts = a[r]
        if (accounts.openStatus === '0') {
          if (allFlag) {
            this.accSelEvent(accounts, 'open')
          } else {
            this.accSelEvent(accounts, 'close')
          }
        }
      }
    })

    this.keywords = {
      address: this.ygtUserInfo.address,
      education: this.ygtUserInfo.education,
      occupation: this.ygtUserInfo.occupation,
      fundCompanyNameList: this.selectedAccounts.length > 0
        ? this.selectedAccounts.map(item => {
          return item.fundCompany
        }).join('、') : ''
    }
    this.$parent.keywords = [[ this.keywords ]]
  },
  deactivated () {
    this.$store.commit('updateIsComponentsFlexVertical', false)
    this.$store.commit('updateSelectedAccountsLength', 0) // 已勾选账户总数，存在vuex中
    this.$bus.off('changeAllFlag') // 事件销毁，防止多次触发
  },
  destroyed () {
    this.$parent.keywords = []
    this.selectedAccounts = []
    this.$store.commit('updateIsComponentsFlexVertical', false)
    this.$store.commit('updateSelectedAccountsLength', 0) // 已勾选账户总数，存在vuex中
    this.$bus.off('changeAllFlag') // 事件销毁，防止多次触发
  },
  watch: {},
  methods: {
    accSelEvent (item, type) {
      if (type === 'close') {
        if (this.selectedAccounts.includes(item)) {
          this.selectedAccounts.remove(item)
        }
      } else if (type === 'open') {
        if (!this.selectedAccounts.includes(item)) {
          this.selectedAccounts.push(item)
        }
      } else {
        if (this.selectedAccounts.includes(item)) {
          this.selectedAccounts.remove(item)
          this.$store.commit('updateAllFundCheckClickFlag', false) // 是否全选，存在vuex中
        } else {
          this.selectedAccounts.push(item)
        }
      }
      // 是否全选，存在vuex中
      if (
        this.selectedAccounts.length ===
        this.$refs.fund_account_list.children.length
      ) {
        this.$store.commit('updateAllFundCheckClickFlag', true)
      }
      this.$store.commit(
        'updateSelectedAccountsLength',
        this.selectedAccounts.length
      ) // 以勾选账户总数，存在vuex中

      this.keywords = Object.assign({}, this.keywords, {
        fundCompanyNameList: this.selectedAccounts.map(item => {
          return item.fundCompany
        }).join('、')
      })
      this.$bus.emit('addKeywords', this.keywords)
      this.$parent.keywords = [[ this.keywords ]]
    },
    checkSubmit () {
      if (this.selectedAccounts.length === 0) {
        _hvueToast({
          mes: '请选择账户'
        })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let accountList = this.selectedAccounts
      let accountType = accountList.map(item => {
        return item.fundCompany
      }).join(',')
      let arr = accountList.map(item => {
        return {
          fundCompany: item.fundCompany,
          fundCompanyName: item.fundCompany,
          fundCode: item.fundCode
        }
      })
      accountList.map(item => {
        return item.fundCompany
      }).join(',')
      this.chooseAccount = arr
      let params = {
        fundCompany: accountType
      } // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = { chooseAccount: this.chooseAccount.map(item => {
        return {
          fundCompany: item.fundCode,
          fundCompanyName: item.fundCompanyName
        }
      }) } // 需要保存的表单数据
      return formData
    },
    // 提交后对结果进行处理的方法
    async handleResultFunc(res) {
      this.selectedAccounts = []
      // 处理完成后，继续执行下一步
      this.$parent.$emit('init')
    }
  }
}
</script>

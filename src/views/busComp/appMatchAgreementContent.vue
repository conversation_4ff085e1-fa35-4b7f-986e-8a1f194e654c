<template>
  <div>
    <!-- 新三板暂时性需求：不匹配时不允许线上办理，提示到营业部办理 -->
    <p
      v-if="sdxParam.matchingFlag === '1' && businessCode === 'xsbkt'"
      class="bot_tips"
    >您的适当性不匹配，请临柜办理。</p>

    <div v-else-if="!+sdxParam.isStrongMatch || !+sdxParam.matchingFlag || +sdxParam.minRankFlag"
      class="rule_check">
      <span class="icon_check" :class="{ checked: isChecked }" @click.stop="isChecked = !isChecked"></span>
      <label>
        已知晓并确认签署
        <a
          href="javascript:void(0)"
          v-for="(item, index) in pageParam"
          @click.stop="toDetail(item.agreementId, item.agreeName)"
          :key="index"
        >《{{ item.agreeName }}》</a>
      </label>
    </div>

    <div class="ce_btn">
      <a
        v-if="!+sdxParam.isStrongMatch || !+sdxParam.matchingFlag || +sdxParam.minRankFlag"
        class="ui button block rounded"
        @click.stop="nextStep"
      >我已确认</a>
      <a class="ui button block rounded border mt10" @click.stop="toIndex">放弃办理</a>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      isChecked: false,
      agreementIds: [],
      sdxParam: {},
      businessCode: this.$route.query.type
    }
  },
  created () {
    this.$bus.on('sdxParam', param => {
      this.sdxParam = param
    })
  },
  methods: {
    toDetail (agreementId, agreementName) {
      if (this.sdxParam.businessName) {
        // 查看协议详情
        this.$router.push({
          name: 'agreementDetail',
          query: { agreementId: agreementId, agreementTitle: agreementName, keyWords: JSON.stringify(this.sdxParam) }
        })
      }
    },
    checkSubmit () {
      if (!this.isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选同意按钮' })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        this.agreementIds.push(element.agreementId)
      }
      // 业务请求参数封装
      let params = {
        agreementId: this.agreementIds.join(','), keyWords: JSON.stringify(this.sdxParam)
      }
      return params
    },
    putFormData () {
      let formData = {
        signProtocol: { agreementId: this.agreementIds.join(',') }
      }
      return formData
    },
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    }
  },
  destroyed () {
    this.$bus.off('sdxParam')
  }
}
</script>

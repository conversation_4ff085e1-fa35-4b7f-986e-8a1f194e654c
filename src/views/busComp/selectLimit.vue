<!-- 新三板开通-选择开通受限交易 -->
<template>
  <div>
    <h5 class="com_title">是否开通受限交易权限</h5>
    <ul class="select_levellist">
      <li>
        <div class="tit" @click.stop="iconChecked = !iconChecked">
          <h5>受限交易权限</h5>
          <span class="icon_check" :class="{checked : iconChecked}"></span>
        </div>
        <div class="cont">
          <p>受限交易权限开通才能买入受限股票，否则只能进行卖出</p>
        </div>
      </li>
    </ul>
    <h5 v-if="pageParam.length>0" class="com_title">受限股票详情</h5>
    <div v-if="pageParam.length>0" class="stock_table">
      <table border="0" width="100%" cellpadding="0" cellspacing="0">
        <tbody>
          <tr>
            <th>股票名称</th>
            <th>股票代码</th>
            <!-- <th>所属层级</th> -->
          </tr>
          <tr v-for="(item,index) in pageParam" :key="index">
            <td>{{item.stockName}}</td>
            <td>{{item.stockCode}}</td>
            <!-- <td>{{item.level}}</td> -->
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      iconChecked: false //是否勾选，默认否
    };
  },
  activated() {
    // if (this.pageParam.length < 1) {
    //   _hvueAlert({ mes: "未查询到受限股票列表", title: "提示" });
    // }
  },
  methods: {
    checkSubmit() {
      if (!this.iconChecked) {
        _hvueToast({
          mes: "请选择是否开通受限交易权限"
        });
        return false;
      }
      return true;
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // if (this.checkSubmit()) {
        // 可以下一步
        resolve();
        // }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {
        limitFlag: this.iconChecked ? "1" : "0" //是否开通受限股权限
      }; // 提交需要的参数
      return params;
    },
    putFormData() {
      let formData = {
        selectLimitRights: {
          openType: "7",
          limitFlag: this.iconChecked ? "1" : "0" //是否开通受限股权限
        }
      }; // 需要保存的表单数据
      return formData;
    }
  }
};
</script>

<!-- 免冠照上传组件 -->
<template>
  <div class="content" v-cloak>
    <div>
      <div class="photo_page white_bg">
        <div class="mug_shot_box">
          <p>请您手持您本人身份证原件拍照，确保身份证不遮挡五官，且五官清晰可辨，请勿遮挡身份证信息</p>
          <div class="mug_shot_item">
            <a
              class="re_btn"
              href="javascript:;"
              v-show="headshot"
              @click.stop="rePhoto('imgUploader')"
              >重拍</a
            >
            <span class="line"><i></i><i></i><i></i><i></i></span>
            <input
              ref="imgUploader"
              v-show="platform === '0' && !headshot"
              type="file" accept="image/*" capture="camera"
              class="file_uploader"
              @change="getImgBrowser('imgUploader')"
            >
            <div class="pic" :class="{ active: headshot }"><img :src="headshot" /></div>
            <a
              class="btn"
              href="javascript:;"
              v-show="!headshot"
              @click.stop="typePop('show')"
              >点击拍照</a
            >
          </div>
        </div>
      </div>
      <div class="ce_btn" v-show="headshot">
        <a class="ui button block rounded" @click.stop="nextStep">下一步</a>
      </div>
    </div>

    <!-- 选择上传方式 -->
    <div v-show="showType" style="display:none;">
      <div class="ui dialog-overlay"></div>
      <div class="upload_btn">
        <ul>
          <li>
            <a @click.stop="getImgNative('pai')">拍摄</a>
          </li>
          <li>
            <a @click.stop="getImgNative('phone')">从相册选择上传</a>
          </li>
        </ul>
        <div class="cancel_btn">
          <a @click.stop="typePop('hide')">取消</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { uploadImg } from '@/service/comServiceNew'
import { function60002 } from 'h5CallNative'
import { getPlatformValue } from 'thinkive-hvue'
import { compressImg } from '@/common/util'

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  name: 'uploadHeadshot',
  data() {
    return {
      serivalId: this.$parent.publicParam.serivalId,
      platform: '', // 操作渠道
      showType: false, // 是否展示上传方法选择框
      headshotMap: {
        tips: '',
        examplePhoto: require('@/assets/images/gesture/woman1.jpg'),
        wrongExamplePhoto: [
          require('@/assets/images/gesture/w14.png'),
          require('@/assets/images/gesture/w12.png'),
          require('@/assets/images/gesture/w13.png'),
          require('@/assets/images/gesture/w11.png')
        ],
        uploaded: false
      },
      headshot: '', // 免冠照照片
      backParam: []
    }
  },
  activated() {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  computed: {},
  watch: {},
  mounted() {
    window.phoneBackBtnCallBack = this.pageBack
    window.imgCallBack = this.getImgCallBack
    this.platform = getPlatformValue()
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      return {}
    },
    /**
     * 请求表单参数打包
     */
    putFormData() {
      let headImage = this.headshotMap.uploadInfo
      let formData = {
        headImage: headImage
      } // 需要保存的表单数据
      return formData
    },
    typePop(isShow) {
      this.showType = isShow === 'show'
    },
    checkSubmit() {
      if (!this.headshotMap.uploaded || !this.headshotMap.uploadInfo) {
        _hvueToast({
          mes: '请按要求拍摄并上传个人免冠照'
        })
        return false
      }
      return true
    },
    // 重拍
    rePhoto(typeName) {
      if (this.platform !== '0') {
        this.typePop('show')
      } else {
        this.$refs[typeName].click()
      }
    },
    // 调用原生上传图片
    getImgNative (actionType) {
      // 调用壳子的上传图片组件
      let param = {
        action: actionType, // phone：相册；pai:拍照界面
        imgType: '3', // 需上传图片类型 3大头照 4 身份证正面 5 反面
        url: SERVER_URL.YGT_NEW_SERVER
      }
      let result = function60002(param)
      this.showType = false
      if (result.error_no && result.error_no !== 0) {
        _hvueToast({ mes: result.error_info || '操作失败' })
      }
    },
    // 浏览器获取照片并转为base64
    getImgBrowser (typeName, type) {
      const inputDOM = this.$refs[typeName]
      let uploadedImg = inputDOM.files[0]
      const reader = new FileReader()
      const _this = this

      reader.onloadend = function (e) {
        let base64 = e.target.result
        console.log('original size: ', base64.length / 1024)
        if (base64.length > 512000) {
          let imgObj = new Image()
          imgObj.src = base64
          imgObj.onload = function () {
            base64 = compressImg(imgObj, 512000 / base64.length)
            _this.getImgCallBack({ base64 })
          }
          return
        }
        _this.getImgCallBack({ base64 })
      }
      if (uploadedImg) {
        console.log(uploadedImg.size)
        if (uploadedImg.size > 10485760) {
          _hvueToast({ mes: '上传的图片大小不得超过10MB' })
          return
        }
        reader.readAsDataURL(uploadedImg)
      }
    },
    // 原生拍照后回调h5的方法
    getImgCallBack(imgInfo) {
      if (imgInfo.ocrInfo) {
        // app端 已由原生上传图片
        this.updateShowPhoto(imgInfo.base64)
      } else {
        // h5本地上传base64到bus
        imgInfo.base64 = this.filterBase64Pre(imgInfo.base64)
        let param = {
          file_type: 'image',
          file_name: `${
            $h.getSession('ygtUserInfo', { decrypt: false }).userId
          }_${parseInt(Math.random() * 10e16)}.jpg`,
          isOcr: 0,
          mediaCode: '211',
          file_data: encodeURIComponent(imgInfo.base64),
          serivalId: this.serivalId,
          // businessCode: this.$route.query.type,
          businessCode: 'czmm',
          flow_current_step: 'uploadHeadshot'
        }
        uploadImg(param)
          .then(data => {
            if (data.error_no === '0') {
              this.headshotMap.uploadInfo = data.uploadInfo[0]
              this.updateShowPhoto(imgInfo.base64)
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch(e => {
            this.updateShowPhoto(imgInfo.base64)
            _hvueToast({ mes: e.message })
          })
      }
    },
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    // 更新已拍照片
    updateShowPhoto(base64) {
      this.headshot = 'data:image/jpeg;base64,' + base64
      this.$set(
        this.headshotMap,
        'uploadedPhoto',
        'data:image/jpeg;base64,' + base64
      )
      this.$set(this.headshotMap, 'uploaded', true)
    },
    // 返回
    pageBack() {
      _hvueConfirm({
        mes: '免冠照上传未完成，是否返回上一步？',
        opts: [
          {
            txt: '取消',
            color: false
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 调用父类返回
              this.$parent.back()
            }
          }
        ]
      })
    },
    nextStep() {
      // 调用父组件的下一步事件
      this.$parent.emitNextEvent()
    }
  }
}
</script>
<style>
.hui-datetime-input {
  width: auto;
}
</style>

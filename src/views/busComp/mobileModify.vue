<template>
  <div>
    <div class="phone_info">
      <p>您当前手机号码</p>
      <div class="tel">{{ ygtUserInfo.mobile | formatMobileNo }}</div>
    </div>
    <h5 class="com_title">请输入新手机号码不得与原手机号码相同：</h5>
    <div class="input_form mobile_form">
      <div class="ui field text" :class="{ code: key === 'code' || key === 'msgCode' }"
        v-for="(item, key) in inputModelComputed" :key="key">
        <label class="ui label">{{ item.name }}</label>
        <input
          v-model.trim="item.value"
          :maxlength="item.maxlength"
          type="text"
          class="ui input"
          :placeholder="item.placeholder"
          @input="checkInputComplete"
        />
        <a
          class="txt_close"
          @click.stop="clearInput(item)"
          v-show="item.value !== ''"
        ></a>
        <a v-if="key === 'code'" class="code_img" @click.stop="getImgCode">
          <img :src="codeImgUrl" />
        </a>
        <smsTimer v-if="key === 'msgCode'" v-model="startFlag" @sendSms="sendMsg"></smsTimer>
      </div>
    </div>
    <h5 class="com_title">请选择手机号类型：</h5>
    <div class="check_list--horizontal">
      <div class="rule_check">
        <span class="icon_check" :class="{ checked: !isOtherPerson }" @click.stop="checkIsOtherPerson(false)"></span>
        <label>本人手机号</label>
      </div>
      <div class="rule_check">
        <span class="icon_check" :class="{ checked: isOtherPerson }" @click.stop="checkIsOtherPerson(true)"></span>
        <label>非本人手机号</label>
      </div>
    </div>
    <div class="input_form mobile_form" v-show="isOtherPerson">
      <div class="ui field text" :class="{ code: key === 'code' }"
        v-for="(item, key) in otherInputModelList" :key="key">
        <label class="ui label">{{ item.name }}</label>
        <input
          v-model.trim="item.value"
          :maxlength="item.maxlength"
          type="text"
          class="ui input"
          :placeholder="item.placeholder"
          :disabled="item.disabled"
          @input="!item.disabled && checkInputComplete()"
        />
        <a
          class="txt_close"
          @click.stop="clearInput(item)"
          v-show="!item.disabled && item.value !== ''"
        ></a>
        <a v-if="key === 'code'" class="code_img" @click.stop="getImgCode">
          <img :src="codeImgUrl" />
        </a>
      </div>
    </div>
    <div class="ce_btn mt20">
      <a
        class="ui button block rounded"
        :class="{disable : showDisableClass}"
        @click.stop="submitMobile"
      >下一步</a>
    </div>
  </div>
</template>

<script>
import smsTimer from '@/components/smsTimer'
import { checkInput, queryDictionary } from '@/common/util'
import { sendMobileMsg, checkMobileMsg } from '@/service/comServiceNew'

export default {
  props: ['pageParam'],
  components: {
    smsTimer
  },
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      submitParam: {},
      smsNo: '', // 修改手机号短信类型数据字典
      startFlag: false,
      sendStatu: 1, // 1: 显示发送按钮 2: 显示倒计时 3:显示重新发送
      isIdentityVerify: true, // 是否需要中登实名认证
      inputModel: {
        mobile: {
          name: '手机号',
          value: '',
          maxlength: '11',
          minlength: '11',
          format: 'phone',
          placeholder: '请输入新手机号'
        },
        msgCode: {
          name: '短信验证码',
          value: '',
          maxlength: '6',
          minlength: '6',
          format: 'num',
          placeholder: '请输入6位短信验证码'
        }
      },
      otherInputModelList: {
        name: {
          name: '机主姓名',
          value: '',
          maxlength: '15',
          minlength: '2',
          format: 'name',
          placeholder: '请输入机主姓名'
        },
        idCardType: {
          name: '证件类型',
          value: '身份证',
          disabled: true,
          minlength: '18',
          maxlength: '18',
          format: 'idno',
          placeholder: '请选择证件类型'
        },
        identityNum: {
          name: '身份证号',
          value: '',
          minlength: '18',
          maxlength: '18',
          format: 'idno',
          placeholder: '请输入身份证号'
        }
      },
      showDisableClass: false,
      isOtherPerson: false // 是否非本人手机号
    }
  },
  computed: {
    inputModelComputed () {
      let result = {}
      for (let i in this.inputModel) {
        if (this.isIdentityVerify && i === 'msgCode') {
          continue
        }
        result[i] = this.inputModel[i]
      }
      return result
    }
  },
  mounted () {
    // 查询短信类型数据字典
    queryDictionary(
      { type: 'ismp.sms_type', value: this.$parent.businessName || this.$route.query.name },
      data => {
        this.smsNo = data.key
      }
    )
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.ygtUserInfo.mobile = this.pageParam[0].mobile
    $h.setSession('ygtUserInfo', this.ygtUserInfo, {encrypt: false})
    this.checkInputComplete()
  },
  deactivated () {
    this.clearCountDown()
  },
  methods: {
    // 检查输入完整性
    checkInputComplete () {
      let mobile = this.inputModel.mobile
      let code = this.inputModel.code
      let flag = checkInput(mobile, false)
      if (flag !== '') {
        this.showDisableClass = true
        return true
      }
      if (this.isIdentityVerify) {
        if (this.isOtherPerson) {
          for (let i in this.otherInputModelList) {
            if (this.otherInputModelList[i].disabled) {
              continue
            }
            let checkResult = checkInput(this.otherInputModelList[i], false)
            flag = flag || checkResult
          }
          if (flag !== '') {
            this.showDisableClass = true
            return true
          }
        }
        this.showDisableClass = false
        return false
      }
      flag = checkInput(code, false)
      if (flag !== '') {
        this.showDisableClass = true
        return true
      }
      if (this.sendStatu === 1) {
        this.showDisableClass = true
        return true
      }
      this.showDisableClass = false
      return false
    },
    // 勾选是否非本人
    checkIsOtherPerson (flag) {
      this.isOtherPerson = flag
      this.checkInputComplete()
    },
    // 发送验证码
    sendMsg () {
      let _mobile = this.inputModel.mobile
      let flag = checkInput(_mobile)
      if (flag !== '') {
        return
      }
      if (_mobile.value === this.ygtUserInfo.mobile) {
        _hvueToast({ mes: '新手机号与旧手机号一样，无需修改' })
        return
      }
      sendMobileMsg({
        flow_name: this.$route.query.type,
        userId: this.ygtUserInfo.userId,
        mobile: _mobile.value,
        smsNo: this.smsNo,
        businessCode: this.$route.query.type
      })
        .then(data => {
          if (data.error_no === '0') {
            // 开启倒计时
            // this.startCountDown()
            this.startFlag = true
            this.sendStatu = 2
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    clearCountDown() {
      this.startFlag = false
      this.sendStatu = 1
    },
    clearInput(item) {
      item.value = ''
      this.checkInputComplete()
    },
    // 检测验证码
    checkMsg (submitParam) {
      checkMobileMsg(submitParam)
        .then(data => {
          if (data.error_no === '0') {
            this.submitParam = submitParam
            $h.setSession('newMobile', this.inputModel.mobile.value)
            // 调用父组件下一步事件
            this.$parent.emitNextEvent()
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    /**
     * 提交前校验格式，组装参数
     */
    submitMobile () {
      if (this.checkInputComplete()) {
        return
      }
      if (this.inputModel.mobile.value === this.ygtUserInfo.mobile) {
        _hvueToast({ mes: '输入的手机号与原手机号相同，请更换' })
        return
      }
      let submitParam = {
        userId: this.ygtUserInfo.userId,
        mobileNo: this.inputModel.mobile.value,
        businessCode: this.$route.query.type
      }
      if (!this.isIdentityVerify) {
        Object.assign(submitParam, {
          verifyCode: this.inputModel.msgCode.value,
          smsNo: this.smsNo
        })
        this.checkMsg(submitParam)
        return
      }

      Object.assign(submitParam, {
        selfFlag: this.isOtherPerson ? '2' : '1'
      })
      Object.assign(submitParam, {
        ownerName: this.isOtherPerson
          ? this.otherInputModelList.name.value
          : this.ygtUserInfo.name,
        ownerIdentityNum: this.isOtherPerson
          ? this.otherInputModelList.identityNum.value
          : this.ygtUserInfo.identityNum,
        ownerIdentityType: '00'
      })
      this.submitParam = submitParam
      // 调用父组件下一步事件
      this.$parent.emitNextEvent()
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      let params = this.submitParam // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {} // 需要保存的表单数据
      if (!this.isIdentityVerify) {
        formData.newMobileInfo = {
          oldMobile: this.ygtUserInfo.mobile,
          newMobile: this.inputModel.mobile.value
        }
      } else {
        formData.newMobileInfo = {
          mobileNo: this.inputModel.mobile.value,
          ownerName: this.isOtherPerson
            ? this.otherInputModelList.name.value
            : this.ygtUserInfo.name,
          ownerIdentityNum: this.isOtherPerson
            ? this.otherInputModelList.identityNum.value
            : this.ygtUserInfo.identityNum,
          ownerIdentityType: '00',
          selfFlag: this.isOtherPerson ? '2' : '1'
        }
      }

      return formData
    },
    // 执行下一步事件
    doCheck (backParam) {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    }
  }
}
</script>

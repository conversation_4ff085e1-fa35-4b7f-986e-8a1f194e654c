<!-- 身份证上传组件 -->
<template>
  <div v-cloak>
    <!--头部 -->
    <headComponent headerTitle="证件上传" v-show="isShowHead"></headComponent>
    <div class="content" v-if="!showSelBox">
      <h5 class="com_title spel" v-show="isUploadPage">
        <i class="icon_ling"></i>根据监管要求，销户需要提供身份证进行实名认证
      </h5>
      <div class="info_ctpage">
        <div class="upload_cont">
          <div
            class="upload_pic"
            v-show="isUploadPage"
            @click.stop="typePop(true, '4')"
          >
            <div class="pic" :class="frontside.uploaded ? 'finish' : ''">
              <img :src="frontside.src" />
            </div>
            <div class="info">
              <input
                ref="frontUploaderCamera"
                type="file"
                accept="image/*"
                capture="camera"
                class="file_uploader"
                @change="getImgBrowser('frontUploaderCamera', '4')"
              />
              <input
                ref="frontUploaderAlbum"
                type="file"
                accept="image/png,image/jpeg,image/gif,image/jpg"
                class="file_uploader"
                @change="getImgBrowser('frontUploaderAlbum', '4')"
              />
              <a class="btn">点击拍摄人像面</a>
            </div>
          </div>

          <div class="input_form" v-show="!isUploadPage">
            <div class="input_text">
              <span class="tit">姓名</span>
              <input
                class="t1"
                type="text"
                maxlength="14"
                v-model.trim="ocrUserInfo.userName"
              />
              <a
                class="txt_close"
                @click.stop="ocrUserInfo.userName = ''"
                v-show="ocrUserInfo.userName != ''"
              ></a>
            </div>
            <div class="input_text">
              <span class="tit">身份证号</span>
              <input
                class="t1"
                type="text"
                maxlength="18"
                v-model.trim="ocrUserInfo.identityNum"
                @input="changeIdnoX"
              />
              <a
                class="txt_close"
                @click.stop="ocrUserInfo.identityNum = ''"
                v-show="ocrUserInfo.identityNum != ''"
              ></a>
            </div>
            <div class="input_text">
              <span class="tit">性别</span>
              <input
                class="t1"
                type="text"
                style="color: #666;"
                readonly
                maxlength="2"
                v-model="getSexName"
              />
            </div>
            <div class="input_text">
              <span class="tit">民族</span>
              <div class="dropdown" @click.stop="showSelBox = true">
                {{ ocrUserInfo.ethnicName }}
              </div>
            </div>
            <div class="input_text">
              <span class="tit">证件地址</span>
              <multLineInput
                class="tarea1 needsclick"
                v-model.trim="ocrUserInfo.papersAddr"
                :maxlength="64"
              ></multLineInput>
            </div>
            <div class="input_text">
              <span class="tit">签发机关</span>
              <input
                class="t1"
                type="text"
                maxlength="32"
                v-model.trim="ocrUserInfo.signOffice"
              />
            </div>
            <div class="input_text">
              <span class="tit">起始日期</span>
              <h-datetime
                class="dropdown"
                title="起始日期"
                type="date"
                v-model.trim="ocrUserInfo.validityBegin"
                start-year="1900"
                :end-year="new Date().getFullYear()"
              ></h-datetime>
            </div>
            <div class="input_text">
              <span class="tit">结束日期 </span>
              <span class="tit" v-show="longTimeChecked">长期 </span>
              <h-datetime
                v-show="!longTimeChecked"
                :readonly="longTimeChecked"
                class="dropdown"
                title="结束日期"
                type="date"
                v-model.trim="ocrUserInfo.validityEnd"
                :start-year="new Date().getFullYear()"
                end-year="3000"
              ></h-datetime>
              <div class="switch">
                <input
                  type="checkbox"
                  :checked="longTimeChecked"
                  @change="longTimeChecked = !longTimeChecked"
                />
                <div class="switch-inner">
                  <div class="switch-arrow"></div>
                </div>
              </div>
            </div>

            <!-- <div class="input_text">
              <div class="tit">有效期</div>
              <div class="date_input">
                <h-datetime
                  class="dropdown"
                  title="起始日期"
                  type="date"
                  v-model.trim="ocrUserInfo.validityBegin"
                  start-year="1900"
                  :end-year="new Date().getFullYear()"
                ></h-datetime>
                <span class="line"></span>
                <h-datetime
                  :readonly="longTimeChecked"
                  class="dropdown"
                  title="结束日期"
                  type="date"
                  v-model.trim="ocrUserInfo.validityEnd"
                  :start-year="new Date().getFullYear()"
                  end-year="3000"
                ></h-datetime>
                <span class="long_span">
                  <span
                    class="icon_check"
                    :class="{ checked: longTimeChecked }"
                    @click.stop="longTimeChecked = !longTimeChecked"
                    >长期</span
                  >
                </span>
              </div>
            </div> -->
          </div>
        </div>

        <div class="upload_cont">
          <div
            class="upload_pic"
            v-show="isUploadPage"
            @click.stop="typePop(true, '5')"
          >
            <div class="pic" :class="backside.uploaded ? 'finish' : ''">
              <img :src="backside.src" />
            </div>
            <div class="info">
              <input
                ref="backUploaderCamera"
                type="file"
                accept="image/*"
                capture="camera"
                class="file_uploader"
                @change="getImgBrowser('backUploaderCamera', '5')"
              />
              <input
                ref="backUploaderAlbum"
                type="file"
                accept="image/png,image/jpeg,image/gif,image/jpg"
                class="file_uploader"
                @change="getImgBrowser('backUploaderAlbum', '5')"
              />
              <a class="btn">点击拍摄国徽面</a>
            </div>
          </div>

          <!-- <div class="input_form" v-show="!isUploadPage">

          </div> -->
        </div>

        <div class="photo_tips">
          <h5 class="title"><span>拍摄规范</span></h5>
          <ul>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_tip_img01.png" />
              </div>
              <span class="ok">标准拍摄</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_tip_img02.png" />
              </div>
              <span class="error">边角缺失</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_tip_img03.png" />
              </div>
              <span class="error">照片模糊</span>
            </li>
            <li>
              <div class="pic">
                <img src="@/assets/images/sl_tip_img04.png" />
              </div>
              <span class="error">反光强烈</span>
            </li>
          </ul>
        </div>
        <!-- <div class="ce_btn" v-show="frontside.uploaded && backside.uploaded">
          <a class="ui button block rounded" @click.stop="nextStep"
            >确认并提交</a
          >
        </div> -->
        <div class="ce_btn">
          <a v-throttle class="p_button" @click.stop="nextStep">确认</a>
        </div>
      </div>

      <!-- 选择上传方式 -->
      <div v-show="showType" style="display:none;">
        <div class="ui dialog-overlay" @click.stop="showType = false"></div>
        <div class="upload_btn">
          <ul>
            <li>
              <a @click.stop="getImg('pai')">拍摄</a>
            </li>
            <li>
              <a @click.stop="getImg('phone')">从相册选择上传</a>
            </li>
          </ul>
          <div class="cancel_btn">
            <a @click.stop="typePop(false, '')">取消</a>
          </div>
        </div>
      </div>
    </div>
    <selBox
      v-if="showSelBox"
      v-model.trim="showSelBox"
      title="民族"
      category="ismp.ethnicname"
      :defaultStr="ocrUserInfo.ethnic"
      @selCallback="selCallback"
    ></selBox>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import {
  idCardToSex,
  idCardToBirthday,
  queryDictionary,
  compressImg,
  registerNativeBack,
  goBackXcApp
} from '@/common/util'
import { uploadImg } from '@/service/comServiceNew'
import { getPlatformValue } from 'thinkive-hvue'
import selBox from '@/components/selBox'
import multLineInput from '@/components/multLineInput'
export default {
  components: {
    headComponent,
    selBox,
    multLineInput
  },
  props: ['pageParam'],
  name: 'uploadIdCardInit',
  data() {
    return {
      handleResult: true,
      platform: '', // 操作渠道
      showType: false, // 是否展示上传方法选择框
      imgType: '', // 上传图片类型 4上传人像面 5上传国徽面
      mediaCode: '',
      showSelBox: false, // 民族
      frontside: {
        src: require('@/assets/images/idcard_ic01.png'),
        uploaded: false
      },
      backside: {
        src: require('@/assets/images/idcard_ic02.png'),
        uploaded: false
      },
      longTimeChecked: false,
      ocrUserInfo: $h.getSession('ocrUserInfo') || {
        validityBegin: '',
        validityEnd: ''
      },
      backParam207: {},
      backParam208: {},
      isUploadPage: true, // 是否上传页面
      address: '',
      isShowHead: true
    }
  },
  computed: {
    serivalId() {
      return this.$parent.publicParam.serivalId
    },
    getSexName() {
      let identityNum = this.ocrUserInfo.identityNum
      this.$set(this.ocrUserInfo, 'birthday', idCardToBirthday(identityNum))
      this.$set(this.ocrUserInfo, 'sex', idCardToSex(identityNum))
      return this.ocrUserInfo.sex === 0 ? '女' : '男'
    }
  },
  watch: {
    longTimeChecked(newVal) {
      if (newVal) {
        if (this.ocrUserInfo.validityEnd !== '3000-12-31') {
          this.$set(
            this.ocrUserInfo,
            'orcValidityEnd',
            this.ocrUserInfo.validityEnd
          )
        }
        this.$set(this.ocrUserInfo, 'validityEnd', '3000-12-31')
        this.longTimeChecked = true
      } else {
        this.$set(
          this.ocrUserInfo,
          'validityEnd',
          this.ocrUserInfo.orcValidityEnd
        )
        this.longTimeChecked = false
      }
    }
  },
  activated() {
    // 注册返回操作
    console.log('activated 注册返回方法')
    registerNativeBack({
      callback: this.pageBack
    })
    if (!$h.getSession('ocrUserInfo')) {
      this.initPage()
    }
    // window.phoneBackBtnCallBack = this.pageBack
    // window.androidAppBack = this.pageBack
    window.imgCallBack = this.getImgCallBack
    this.platform = getPlatformValue()
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.isShowHead = true
  },
  deactivated() {
    this.isUploadPage = true
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        } else {
          reject(new Error({ mes: '表单校验不通过' }))
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = this.ocrUserInfo // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = {
        uploadIdCard: [this.backParam207, this.backParam208],
        ocrUserInfo: this.ocrUserInfo
      } // 需要保存的表单数据
      return formData
    },
    // 提交后对结果进行处理的方法
    async handleResultFunc(res) {
      // this.initPage()
      // 处理完成后，继续执行下一步
      this.$parent.$emit('init')
    },

    // 初始化页面
    initPage() {
      this.clearFileInput()
      this.showType = false // 是否展示上传方法选择框
      this.imgType = '' // 上传图片类型 4上传人像面 5上传国徽面
      this.mediaCode = ''
      this.showSelBox = false // 民族
      this.frontside = Object.assign(
        {},
        {
          src: require('@/assets/images/idcard_ic01.png'),
          uploaded: false
        }
      )
      this.backside = Object.assign(
        {},
        {
          src: require('@/assets/images/idcard_ic02.png'),
          uploaded: false
        }
      )
      this.longTimeChecked = false
      this.ocrUserInfo = {
        validityBegin: '',
        validityEnd: ''
      }
      this.backParam207 = {}
      this.backParam208 = {}
    },
    // 转换身份证号中的小写x
    changeIdnoX() {
      if (this.ocrUserInfo.identityNum) {
        this.ocrUserInfo.identityNum = this.ocrUserInfo.identityNum.replace(
          'x',
          'X'
        )
      }
    },
    // 点击上传图片
    typePop(isShow, type) {
      this.mediaCode = type == '4' ? '207' : '208'
      this.imgType = type
      if (isShow) {
        if ($hvue.env !== 'ths') {
          if (type === '4') {
            this.$refs.frontUploaderAlbum.click()
          }
          if (type === '5') {
            this.$refs.backUploaderAlbum.click()
          }
          return
          // if ($hvue.iBrowser.ios) {
          //   if (type === '4') {
          //     this.$refs.frontUploaderAlbum.click()
          //   }
          //   if (type === '5') {
          //     this.$refs.backUploaderAlbum.click()
          //   }
          //   return
          // }
        }
      }
      this.getImg('pai')
    },
    selCallback(a) {
      this.$set(this.ocrUserInfo, 'ethnicName', a.data.value)
      this.$set(this.ocrUserInfo, 'ethnic', a.data.key)
    },
    // 选择上传图片方式
    getImg(actionType) {
      this.getImgNative(actionType)
      return
      // if (this.platform !== '0') {
      //   this.getImgNative(actionType)
      //   return
      // }
      if ($hvue.iBrowser.ios) {
        if (this.mediaCode === '207') {
          // 正面
          this.$refs.frontUploaderAlbum.click()
        }
        if (this.mediaCode === '208') {
          // 反面
          this.$refs.backUploaderAlbum.click()
        }
        return
      }
      if ($hvue.iBrowser.android) {
        if (actionType === 'pai') {
          if (this.mediaCode === '207') {
            // 正面
            this.$refs.frontUploaderCamera.click()
          }
          if (this.mediaCode === '208') {
            // 反面
            this.$refs.backUploaderCamera.click()
          }
        }
        if (actionType === 'phone') {
          if (this.mediaCode === '207') {
            // 正面
            this.$refs.frontUploaderAlbum.click()
          }
          if (this.mediaCode === '208') {
            // 反面
            this.$refs.backUploaderAlbum.click()
          }
        }
        return
      }
    },
    // 调用原生上传图片
    // getImgNative(actionType) {
    //   let param = {
    //     action: actionType, // phone：相册；pai:拍照界面
    //     imgType: this.imgType, // 需上传图片类型 4 身份证正面 5 反面
    //     url: SERVER_URL.YGT_NEW_SERVER
    //   }
    //   let result = function60014(param)
    //   this.showType = false
    //   if (result.error_no && result.error_no !== 0) {
    //     _hvueToast({ mes: result.error_info })
    //   }

    // },
    // 调用原生上传图片
    getImgNative(actionType) {
      let param = {
        /*action: actionType, // phone：相册；pai:拍照界面
        imgType: this.imgType, // 需上传图片类型 4 身份证正面 5 反面
        url: SERVER_URL.YGT_NEW_SERVER*/

        isCanChoosePhoto: '1',
        sideType: this.imgType === '4' ? '1' : '2'
      }

      invokeNative('8007', '000', param, data => {
        console.log('回调结果', data)
        this.showType = false
        this.getImgCallBack(data)
      })
    },
    // 浏览器获取照片并转为base64
    getImgBrowser(typeName, type) {
      const inputDOM = this.$refs[typeName]
      let uploadedImg = inputDOM.files[0]
      const reader = new FileReader()
      const _this = this

      this.imgType = type
      this.mediaCode = type === '4' ? '207' : '208'
      reader.onloadend = function(e) {
        let base64 = e.target.result
        console.log('original size: ', base64.length / 1024)
        if (base64.length > 512000) {
          let imgObj = new Image()
          imgObj.src = base64
          imgObj.onload = function() {
            base64 = compressImg(imgObj, 512000 / base64.length)
            _this.getImgCallBack({ base64 })
          }
          return
        }
        _this.getImgCallBack({ base64 })
      }
      if (uploadedImg) {
        // console.log(uploadedImg.size)
        if (uploadedImg.size > 10485760) {
          _hvueToast({ mes: '上传的图片大小不得超过10MB' })
          return
        }
        reader.readAsDataURL(uploadedImg)
      }
    },
    // 将base64上传
    getImgCallBack(imgInfo) {
      let imgInfObj = {}
      console.log('getImgCallBack注册返回事件')
      registerNativeBack({
        callback: this.pageBack
      })
      const userInfo = $h.getSession('ygtUserInfo', { decrypt: false })
      if ($hvue.env == 'ths') {
        imgInfObj = JSON.parse(imgInfo)
        imgInfObj.base64 = imgInfObj.originalImg
        if (!imgInfObj.base64) {
          _hvueToast({ mes: '未识别的身份证图片' })
          return
        }
      } else {
        imgInfObj.base64 = imgInfo.base64
      }

      imgInfObj.base64 = this.filterBase64Pre(
        imgInfObj.base64 || imgInfObj.frontBase64 || imgInfObj.backBase64
      )
      if (
        $hvue.env == 'ths' &&
        this.mediaCode == '207' &&
        imgInfObj.type != '1'
      ) {
        _hvueToast({ mes: '请上传正面身份证' })
        return
      } else if (
        $hvue.env == 'ths' &&
        this.mediaCode == '208' &&
        imgInfObj.type != '2'
      ) {
        _hvueToast({ mes: '请上传反面身份证' })
        return
      }
      let param = {
        file_type: 'image',
        file_name: `${userInfo ? userInfo.userId : ''}.jpg`,
        isOcr: 0,
        mediaCode: this.mediaCode,
        file_data: encodeURIComponent(imgInfObj.base64),
        serivalId: this.serivalId,
        businessCode: this.$route.query.type,
        flow_current_step: 'uploadIdCard'
      }
      uploadImg(param)
        .then(data => {
          if (data.error_no === '0') {
            if (this.mediaCode === '207') {
              this.backParam207 = data.uploadInfo[0]
              let ocrInfo = {
                birthday: '',
                stepNo: '',
                updateDate: '',
                address: '',
                validityEnd: '',
                serivalId: '',
                gender: '',
                signOffice: '',
                nation: '',
                code: '',
                businessCode: '',
                validityBegin: '',
                identityType: '',
                name: '',
                id: '',
                createDate: ''
              }
              $hvue.env != 'ths' && Object.assign(imgInfObj, ocrInfo)
            } else {
              this.backParam208 = data.uploadInfo[0]
              let ocrInfo = {
                birthday: '',
                issue: '',
                stepNo: '',
                updateDate: '',
                address: '',
                valid: '',
                validityEnd: '',
                serivalId: '',
                sex: '',
                signOffice: '',
                ethnicname: '',
                identityNum: '',
                businessCode: '',
                validityBegin: '',
                identityType: '',
                name: '',
                id: '',
                papersAddr: '',
                createDate: ''
              }
              $hvue.env != 'ths' && Object.assign(imgInfObj, ocrInfo)
            }
            this.echoOrcInfo(imgInfObj.base64, imgInfObj)
          } else {
            return Promise.reject(new Error(data.error_info))
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    // 将bus返回ocr识别信息做回显
    echoOrcInfo(base64, result) {
      if (result.code) {
        this.$set(this.ocrUserInfo, 'identityNum', result.code)
        this.$set(this.ocrUserInfo, 'birthday', idCardToBirthday(result.code))
        this.$set(this.ocrUserInfo, 'sex', idCardToSex(result.code))
        this.$set(this.ocrUserInfo, 'userName', result.name)
        let ethnicName =
          result.nation.indexOf('族') > -1
            ? result.nation
            : result.nation
            ? result.nation + '族'
            : '汉族'
        this.$set(this.ocrUserInfo, 'ethnicName', ethnicName)
        let that = this
        queryDictionary(
          { type: 'ismp.ethnicname', value: this.ocrUserInfo.ethnicName },
          function(d) {
            that.$set(that.ocrUserInfo, 'ethnic', d.key)
            // that.ocrUserInfo.ethnic = d.key;
          }
        )
        // this.$set(this.ocrUserInfo, 'papersAddr', result.address)
        this.address = result.address
        this.$nextTick()
        // reSetHeight
        this.frontside.uploaded = true
        this.frontside.src = 'data:image/jpeg;base64,' + base64
      } else if (result.valid) {
        this.$set(this.ocrUserInfo, 'signOffice', result.issue)
        let valid = result.valid
        valid = valid.split('-')
        let validityBegin = valid[0]
        validityBegin =
          validityBegin.substr(0, 4) +
          '-' +
          validityBegin.substr(4, 2) +
          '-' +
          validityBegin.substr(6)
        let validityEnd = valid[1]
        if (validityEnd == '长期') {
          validityEnd = '3000-12-31'
          this.longTimeChecked = true
        } else {
          validityEnd =
            validityEnd.substr(0, 4) +
            '-' +
            validityEnd.substr(4, 2) +
            '-' +
            validityEnd.substr(6)
          this.$set(this.ocrUserInfo, 'orcValidityEnd', validityEnd)
          this.$set(this.ocrUserInfo, 'validityEnd', validityEnd)
          this.longTimeChecked = false
        }
        this.$set(this.ocrUserInfo, 'validityBegin', validityBegin)
        this.$set(this.ocrUserInfo, 'validityEnd', validityEnd)

        this.backside.uploaded = true
        this.backside.src = 'data:image/jpeg;base64,' + base64
      } else {
        _hvueToast({ mes: '未识别的身份证图片' })
      }
    },
    // 去除base64头
    filterBase64Pre(ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    // 提交校验
    checkSubmit() {
      // return true
      // if (!this.frontside.uploaded || !this.backside.uploaded) {
      //   _hvueToast({
      //     mes: '请上传身份证'
      //   })
      //   return false
      // }
      if (!this.ocrUserInfo.userName) {
        _hvueToast({
          mes: '姓名不能为空'
        })
        return false
      }
      if (
        !/^[\u4E00-\u9FA5]{2,14}([·•]?[\u4E00-\u9FA5]+)*$/.test(
          this.ocrUserInfo.userName
        )
      ) {
        _hvueToast({
          mes: '姓名格式不正确'
        })
        return false
      }
      if (!this.ocrUserInfo.identityNum) {
        _hvueToast({
          mes: '身份证号不能为空'
        })
        return false
      }
      if (!/^([\d]{17}[\dXx]|[\d]{15})$/.test(this.ocrUserInfo.identityNum)) {
        _hvueToast({
          mes: '身份证号格式不正确'
        })
        return false
      }

      if (!this.ocrUserInfo.ethnic) {
        _hvueToast({
          mes: '民族不能为空'
        })
        return false
      }
      if (!this.ocrUserInfo.papersAddr) {
        _hvueToast({
          mes: '住址不能为空'
        })
        return false
      }
      if (
        this.ocrUserInfo.papersAddr.length < 8 ||
        this.ocrUserInfo.papersAddr > 64
      ) {
        _hvueToast({
          mes: '住址格式不正确'
        })
        return false
      }
      if (!this.ocrUserInfo.signOffice) {
        _hvueToast({
          mes: '发证机关不能为空'
        })
        return false
      }
      if (!$h.isCn(this.ocrUserInfo.signOffice)) {
        _hvueToast({
          mes: '发证机关格式不正确'
        })
        return false
      }

      let beginDate = this.ocrUserInfo.validityBegin
      let endDate = this.ocrUserInfo.validityEnd
      let dateExp = /\d{4}-\d{2}-\d{2}/
      // 判断有效期格式是否正确
      if (
        !dateExp.test(beginDate) ||
        !dateExp.test(endDate) ||
        Date.parse(beginDate.replace(/\./g, '-')) > Date.now() ||
        beginDate === endDate
      ) {
        _hvueToast({
          mes: '请设置正确的身份证有效期限'
        })
        return false
      }
      // 判断身份证是否过期
      if (Date.parse(endDate.replace(/\./g, '-')) < Date.now()) {
        _hvueToast({
          mes: '您的身份证已过期'
        })
        return false
      }
      return true
    },
    // 返回
    pageBack() {
      if (!this.isUploadPage) {
        this.isUploadPage = true
        registerNativeBack({
          callback: this.pageBack
        })
        return
      }
      _hvueConfirm({
        mes: '身份信息确认未完成，是否返回上一步？',
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              registerNativeBack({
                callback: this.pageBack
              })
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 调用父类返回
              if (this.$route.query.type == 'xh') {
                let formStatus = $h.getSession('formStatus')
                if (formStatus == '4') {
                  $hvue.env == 'ths'
                    ? goBackXcApp()
                    : this.$router.push({ name: 'index' })
                } else {
                  this.$parent.rollback('chooseAccount')
                  // this.isShowHead = false
                }
              } else {
                // this.isShowHead = false
                this.$parent.back()
              }
            }
          }
        ]
      })
    },
    // 调用父组件的下一步事件
    nextStep() {
      if (this.isUploadPage) {
        if (!this.frontside.uploaded || !this.backside.uploaded) {
          _hvueToast({
            mes: '请上传身份证'
          })
          return false
        }
        this.isUploadPage = false
        this.$set(this.ocrUserInfo, 'papersAddr', this.address)
      } else {
        this.$parent.emitNextEvent()
      }
    },
    // 清空input中的值，避免无法触发change事件
    clearFileInput() {
      this.$refs.frontUploaderCamera.value = null
      this.$refs.backUploaderCamera.value = null
      this.$refs.frontUploaderAlbum.value = null
      this.$refs.backUploaderAlbum.value = null
    }
  }
}
</script>
<style>
.hui-datetime-input {
  width: auto;
}
</style>

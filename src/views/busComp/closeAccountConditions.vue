/*
 * 销户账号条件检测--非接口版
 * @Author: Way
 * @Date: 2020-08-22 12:50:29
 * @Last Modified by: Way
 * @Last Modified time: 2020-08-22 12:59:38
 */
<template>
  <div>
    <div class="xh_mustlist">
        <ul>
            <li>
                <h5>股东账户</h5>
                <div class="cont">
                    <p>无股票持仓，无新股申购，无未到期回购，无未到账红股，当日无交易或委托记录(T-2 日无港股通交易记录)</p>
                    <p>账户未开通以下交易权限:</p>
                    <p>1. 债券合格投资者   2. 约定购回权限   3. 报价回购权限4. 股票质押权限   5. 港股通权限</p>
                </div>
            </li>
            <li>
            <h5>基金账户</h5>
                <div class="cont">
                    <p>无基金份额，无在途交易(申购、认购、赎回)，</p>
                    <p>未签约现金宝之类理财产品</p>
                </div>
            </li>
            <li>
                <h5>资金账户、三方存管</h5>
                <div class="cont">
                    <p>资金账户余额为0，T日无证券交易，无银证转账流水，无未到期回购，无待交收资金，资金账户状态正常</p>
                    <p>无资金冻结，利息积数为0，股东账户、基金账户满足销户条件</p>
                </div>
            </li>
            <li>
                <h5>客户号</h5>
                <div class="cont">
                    <p>股东账户满足销户条件，基金账户满足销户条件，资金账户满足销户条件</p>
                </div>
            </li>
        </ul>
    </div>
    <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="doCheck">我已确认</a>
        <a class="ui button block rounded border mt15" @click.stop="back">放弃办理</a>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {}
  },
  activated () {
  },
  methods: {
    back () {
      this.$router.push({ name: 'index' })
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        param1: '',
        param2: ''
      }
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>
<style>
.a_link {
  height: 0.24rem;
  line-height: 0.24rem;
  position: absolute;
  top: 0.11rem;
  right: 0.15rem;
  font-size: 0.13rem;
  color: #285fc1;
}
</style>

<template>
  <div class="pword_box">
    <div class="pword_setcheck">
      <p>交易密码，资金密码相同</p>
      <div class="ui switch">
        <input type="checkbox" :checked="isModifyAllPwd" @change="isModifyAllPwd = !isModifyAllPwd">
        <div class="ui switch-inner">
          <div class="ui switch-arrow"></div>
        </div>
      </div>
    </div>
    <div class="input_form mt10">
      <passwordInput
        v-model="inputModel.oldPwd.value"
        inputTitle="旧密码"
        :maxLength="inputModel.maxlength"
      ></passwordInput>
    </div>
    <div class="input_form mt10">
      <passwordInput
        v-model="inputModel.newPwd.value"
        inputTitle="新密码"
        :maxLength="inputModel.maxlength"
        :canShowPwd="false"
      ></passwordInput>
      <passwordInput
        v-model="inputModel.reNewPwd.value"
        inputTitle="再次输入"
        placeholder="请再次输入新密码"
        :maxLength="inputModel.maxlength"
        :canShowPwd="false"
      ></passwordInput>
    </div>
    <div class="ce_btn mt10">
      <a class="ui button block rounded" @click.stop="submitPwd">确认修改</a>
    </div>
  </div>
</template>

<script>
import { checkInput } from '@/common/util'
import { ecbEncrypt } from 'thinkive-hvue/plugin/sm/sm4'
import passwordInput from '@/components/passwordInput'

export default {
  props: ['pageParam'],
  components: { passwordInput },
  data() {
    return {
      handleResult: true,
      fundAccount: $h.getSession('ygtUserInfo', { decrypt: false }).fundAccount,
      submitParam: [],
      passwordType: '',
      isModifyAllPwd: true,
      showPwd: false,
      pwdShow: false,
      type: '',
      inputModel: {
        maxlength: 6,
        oldPwd: {
          name: '旧密码',
          value: '',
          format: 'num'
        },
        newPwd: {
          name: '新密码',
          value: '',
          format: 'pwd'
        },
        reNewPwd: {
          name: '再次输入新密码',
          value: '',
          format: 'pwd'
        }
      }
    }
  },
  computed: {},
  activated () {
    if (this.pageParam.length < 1) {
      _hvueAlert({
        mes: '未获取到密码类型，请重新选择',
        title: '提示',
        callback: () => {
          this.$parent.back()
        }
      })
    }
    this.passwordType = this.pageParam[0].passwordType
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  deactivated () {
    for (let i in this.inputModel) {
      if (this.inputModel[i].value) {
        this.inputModel[i].value = ''
      }
    }
  },
  methods: {
    // 检查新密码合规性
    checkCompliance (newVal, oldVal, newAgain) {
      if (newVal.value !== newAgain.value) {
        _hvueToast({
          mes: '两次新密码输入不一致，请检查'
        })
        return false
      }
      if (oldVal && newVal.value === oldVal.value) {
        _hvueToast({
          mes: '新密码与旧密码输入一致，请检查'
        })
        return false
      }
      return true
    },
    /**
     * type: 1-资金密码修改 2-交易密码修改 1,2-同步修改资金和交易密码
     */
    submitPwd () {
      if (checkInput(this.inputModel.oldPwd)) { // 检查旧密码
        return
      }
      if (checkInput(this.inputModel.newPwd)) { // 检查新密码
        return
      }
      if (checkInput(this.inputModel.reNewPwd)) { // 检查再次输入新密码
        return
      }
      if (!this.checkCompliance(
        this.inputModel.newPwd,
        this.inputModel.oldPwd,
        this.inputModel.reNewPwd
      )) {
        return
      }
      const sm4Key = $hvue.config.sm4Key
      this.submitParam = [{
        userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
        passwordType: this.passwordType,
        isLinkage: +this.isModifyAllPwd,
        oldPassword: ecbEncrypt(
          sm4Key.substring(8, sm4Key.length - 8),
          this.inputModel.oldPwd.value
        ),
        newPassword: ecbEncrypt(
          sm4Key.substring(8, sm4Key.length - 8),
          this.inputModel.newPwd.value
        )
      }]

      this.$parent.emitNextEvent()
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        $h.setSession('passwordType', this.isModifyAllPwd && !this.passwordType.includes('2')
          ? this.passwordType + ',2'
          : this.passwordType)
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params // 提交需要的参数

      params = this.submitParam[0]
      return params
    },
    putFormData () {
      let formData // 需要保存的表单数据

      formData = {
        pwdModify: this.submitParam[0]
      }
      return formData
    },
    handleResultFunc () {
      let noticeText = ''
      if (this.passwordType === '2') {
        noticeText = '交易密码修改成功，您需重新登录'
      }
      if (this.passwordType === '1') {
        noticeText = '资金密码修改成功'
      }
      if (this.isModifyAllPwd) {
        noticeText = '交易、资金密码修改成功，您需重新登录'
      }
      _hvueToast({
        icon: 'success',
        mes: noticeText,
        callback: () => {
          if (this.passwordType === '2' || this.isModifyAllPwd) {
            $h.clearSession('ygtUserInfo')
            $h.clearSession('isLogin')
            $h.clearSession('ssoInfo')
          }
          this.submitParam = []
          // 处理完成后，继续执行下一步
          this.$parent.$emit('init')
        }
      })
    }
  }
}
</script>

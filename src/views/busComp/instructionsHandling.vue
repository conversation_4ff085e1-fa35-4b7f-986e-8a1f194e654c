<template>
  <div v-if="flowName === 'gdhjg'">
    <div class="notice_box">
      <div class="pic"><img src="@/assets/images/not_ic13.png"></div>
      <h5>您可通过此业务新开上海A股、深圳A股、上海场内基金、深圳场内基金四类账户。</h5>
    </div>
    <div class="bus_tips">
      <h5>开通说明</h5>
        <ul>
          <li>若您某个类型的账户已经开满了三户则不能再进行增开，如您上海A股账户已经开通过三户，则不能继续开通上海A股账户。</li>
          <li>在我司已经开通过某个类型的账户则不能再进行增开，比如您在我司已经指定了上海A股账户，则不能继续开通上海A股账户。</li>
          <li>业务需要在交易日<span class="ared">9：00~16：00，16：30~18：55</span>进行办理</li>
          <li>请准备好您的个人身份证</li>
        </ul>
      </div>
  </div>
  <div v-else-if="flowName === 'zkgdh'">
    <div class="notice_box">
      <div class="pic"><img src="@/assets/images/not_ic13.png"></div>
      <h5>您可通过此业务新开上海A股、深圳A股账户。</h5>
    </div>
    <div class="bus_tips" v-html="instructionsHandling"></div>
  </div>
  <div v-else>
    <div class="notice_box">
      <div class="pic"><img src="@/assets/images/not_ic08.png" /></div>
    </div>
    <div v-html="instructionsHandling"></div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      flowName: this.$parent.flowName,
      instructionsHandling: ''
    }
  },
  activated() {
    this.$store.commit('updateBusinessNextBtnStatus', '马上办理') // 修改下一步按钮

    this.instructionsHandling = this.pageParam[0].instructionsHandling
    if (this.instructionsHandling.includes('button')) {
      // 有自己的按钮
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    }
  },
  methods: {
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      let params = {} // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = {} // 需要保存的表单数据
      return formData
    },
    // 调用父组件的下一步事件
    nextStep(event) {
      if (event.target.title === 'nextStep') {
        // 如果点击到了
        // 拿着id参数执行着相关的操作
        this.$parent.emitNextEvent()
      }
    },
    toBusiness(businessCode) {
      let allBusinessArr = $h.getSession('allBusinessArr') || []
      let toPage
      if (allBusinessArr.length > 0) {
        allBusinessArr.forEach(item => {
          item.businessMenu.forEach(item => {
            if (item.businessCode === businessCode) {
              toPage = item
            }
          })
        })
      }
      $h.setSession('toPage', JSON.stringify(toPage), { encrypt: false })
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped>
</style>

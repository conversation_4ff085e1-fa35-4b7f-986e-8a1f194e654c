<template>
  <div class="user_main">
    <h5 class="com_title">请选择想要修改的密码类型</h5>
    <ul class="type_selectlist">
      <li v-for="(item, index) in typeList" :key="index" @click="checkItem(item)">
        <h5>{{ item.name }}</h5>
        <p>{{ item.info }}</p>
        <span class="icon_check" :class="{ checked: item.checked }"></span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      passwordType: '',
      isSingleChoice: true,
      typeList: [
        {
          type: '2',
          name: '修改交易密码',
          info: '用于登录及交易',
          checked: false
        },
        {
          type: '1',
          name: '修改资金密码',
          info: '用于银证转账',
          checked: false
        }
      ]
    }
  },
  activated() {
    this.passwordType = ''
    this.$store.commit('updateBusinessNextBtnStatus', '下一步') // 隐藏下一步按钮
  },
  methods: {
    checkItem(item) {
      if (this.isSingleChoice) {
        this.typeList.forEach(item => {
          item.checked = false
        })
      }
      item.checked = !item.checked
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        let itemChecked = this.typeList.some(item => {
          return item.checked
        })
        if (itemChecked) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      this.typeList.forEach(item => {
        if (item.checked) {
          this.passwordType += `${item.type},`
        }
      })
      this.passwordType = this.passwordType.slice(0, -1)
      // 业务请求参数封装
      let params = {
        passwordType: this.passwordType
      } // 提交需要的参数
      return params
    },
    putFormData() {
      let formData = {} // 需要保存的表单数据
      return formData
    }
  }
}
</script>

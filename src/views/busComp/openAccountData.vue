<!-- 增开股东户-股东账户列表（暂未使用） -->
<template>
  <div>
    <div v-show="pageParam[0].isOpenAccountFlag=='false'" class="cond_tips">
      <p>{{pageParam[0].reason}}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  data () {
    return {
    }
  },
  activated () {
    if (this.pageParam[0].isOpenAccountFlag !== 'false') {
      this.$store.commit('updateNextBtnText', '继续开户')
    } else {
      this.$store.commit('updateNextBtnText', '返回首页')
      this.$store.commit('updateNextBtnCss', true)
    }
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.pageParam[0].isOpenAccountFlag !== 'false' || process.env.NODE_ENV === 'development') {
          // 可以下一步
          resolve()
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>

<!-- 增开股东户-股东账户列表 -->
<template>
  <div>
    <div v-show="szaAccountList.length > 0" class="cond_box">
      <h5 class="title">您已开通的深A账户</h5>
      <ul class="acct">
        <li v-for="(item, index) in szaAccountList" :key="index">
          <div class="cont">
            <p>
              <span>{{ item.marketName }} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus, item.isExists)">
                {{ getAccountState(item.isExists, item.holderName, item.market) }}
              </em>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="shaAccountList.length > 0" class="cond_box">
      <h5 class="title">您已开通的沪A账户</h5>
      <ul class="acct">
        <li v-for="(item, index) in shaAccountList" :key="index">
          <div class="cont">
            <p>
              <span>{{ item.marketName }} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus, item.isExists)">
                {{ getAccountState(item.isExists, item.holderName, item.market) }}
              </em>
            </p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      szaAccountList: [],
      shaAccountList: [],
      nextFlag: false
    }
  },
  activated () {
    let list = this.pageParam
    this.szaAccountList = []
    this.shaAccountList = []
    list.forEach(item => {
      if (item.market === '00') {
        this.szaAccountList.push(item)
      } else if (item.market === '10') {
        this.shaAccountList.push(item)
      }
    })
  },
  methods: {
    getAccountStyle (holderStatus, isExists) {
      if (isExists === '0' || holderStatus === '0') { // 已指定
        return 'normal_span'
      } else {
        return 'assign_span'
      }
    },
    getAccountState (isExists, holderName, market) {
      if (isExists === '0') { // 已指定
        return market === '00' ? holderName : '已指定'
      } else {
        return holderName
      }
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 可以下一步
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {}
      return params
    },
    putFormData () {
      let formData = {}
      return formData
    }
  }
}
</script>

<!-- 增开股东户-勾选市场 -->
<template>
  <div>
    <div class="agree_main">
      <h5 class="title">请选择以下需要开通的账户：</h5>
      <ul class="account_list">
        <!-- 对于中登已经开满三户或本地已开的类型禁止选择 -->
        <li v-for="(item, index) in pageParam"
          :key="index"
          v-show="item.status === '1'"
          :class="{ disable: !checkAccountTypeAvailable(item) }">
          <p>
            <span
              class="icon_radio"
              @click.stop="accSelEvent(item, index)"
              :class="{ checked: selectedItem.includes(item) }"
            >{{ item.itemName }}</span>
          </p>
          <span v-show="!checkAccountTypeAvailable(item)" class="status">{{ getUnavailableReason(item) }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      selectedItem: []
    }
  },
  activated() {
    this.selectedItem = []
  },
  methods: {
    // 不可选原因
    getUnavailableReason(item) {
      return item ? '我司已有账户' : '已开满三户'
    },
    // 检查市场是否可选
    checkAccountTypeAvailable(item) {
      return true
    },
    accSelEvent(item, index) {
      if (this.selectedItem.some(selectedItem => {
        return selectedItem.itemValue === item.itemValue
      })) {
        this.selectedItem.remove(index)
      } else {
        this.selectedItem.push(item)
      }
    },
    checkSubmit() {
      if (this.selectedItem.length === 0) {
        _hvueToast({
          mes: '请选择开通市场'
        })
        return false
      }
      return true
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      const params = {
        marketDesc: this.selectedItem.map(item => { return item.itemName }).join(','),
        accountType: this.selectedItem.map(item => { return item.itemValue }).join(',')
      }
      return params
    },
    putFormData() {
      const arr = this.selectedItem.map(item => { return { market: item.itemValue } })
      const formData = {
        chooseAccount: arr
      }
      return formData
    }
  }
};
</script>

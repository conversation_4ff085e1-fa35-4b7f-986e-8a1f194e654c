<template>
  <div>
    <div class="wtclass_main">
      <h5 class="com_title">请选择以下您想要修改的委托方式：</h5>
      <ul class="wtclass_list">
        <li v-for="(item, index) in entrustList" :key="index">
          <span>{{item.entrustName}}</span>
          <em v-show="(!+item.allowOpen && !+item.entrustStatus) ||
            (!+item.allowClose && +item.entrustStatus)" class="info">此委托方式无法线上修改</em>
          <div
            class="ui switch"
            :class="{ checked: +item.checkStatus }"
            @click.stop="checkEntrustType(item, index)"
          >
            <div class="ui switch-inner">
              <div class="ui switch-arrow"></div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <h-popup v-model="confirmPopupShow" width="88%" position="center">
      <div class="popup_box">
        <div class="popup_header">
          <h4>请阅读以下协议</h4>
        </div>
        <div class="popup_content center">
          <a href="javascript:;" v-for="(item, index) in thisEntrustType.agreementList" :key="index"
            @click="toDetail(item.agreementId, item.agreeName, index)"
          >《{{ item.agreeName }}》</a>
        </div>
        <div class="popup_btn_box">
          <a href="javascript:;" @click="cancelModify">取消</a>
          <a class="primary" href="javascript:;"
            @click="modifyState"
          >确定开通</a>
        </div>
      </div>
    </h-popup>
  </div>
</template>

<script>
import { getProtocolByType, signAgreement } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  data () {
    return {
      entrustList: [],
      oriEntrustWay: '', // 原委托方式
      submitParams: {},
      thisEntrustType: {},
      confirmPopupShow: false
    }
  },
  mounted () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到委托方式', title: '提示' })
      return
    }
    this.$store.commit('updateBusinessNextBtnStatus', '确认修改')
    this.$store.commit('updateNextBtnDisabled', true)
    $h.clearSession('agreementIds')

    let entrusts = JSON.parse(this.pageParam[0].entrustList)
    let userEntrust = JSON.parse(this.pageParam[0].userEntrust)

    this.entrustList = []
    this.entrustList.push(...entrusts.map((item) => {
      return { ...item, checkStatus: item.entrustStatus }
    }))
    this.oriEntrustWay = userEntrust[0].entrustWay
  },
  activated () {
    const readAgreements = $h.getSession('agreementIds')
    if (readAgreements) {
      this.entrustList.forEach(entrustItem => {
        if (entrustItem.agreementList && entrustItem.agreementList.length > 0) {
          entrustItem.agreementList.forEach(item => {
            if (readAgreements.split(',').includes(item.agreementId)) {
              item.isRead = true
            }
          })
        }
      })
    }
  },
  methods: {
    // 选择委托方式
    checkEntrustType(item, index) {
      if (!+item.allowOpen && !+item.entrustStatus) {
        _hvueToast({ mes: '该委托方式不允许网上办理开通' })
        return
      }
      if (!+item.allowClose && +item.entrustStatus) {
        _hvueToast({ mes: '该委托方式不允许网上办理关闭' })
        return
      }
      this.thisEntrustType = item
      if (+item.needAgree && !+item.checkStatus && !+item.entrustStatus) {
        this.getProtocol(this.thisEntrustType)
        this.confirmPopupShow = true
        return
      }
      this.modifyState()
    },
    // 修改委托方式
    modifyState () {
      // 重新赋值当前办理的委托方式对象
      if (!+this.thisEntrustType.entrustStatus && !+this.thisEntrustType.checkStatus) { // 需要签署协议
        if (!this.thisEntrustType.agreementList.every(item => {
          return item.isRead
        })) {
          _hvueToast({ mes: '请先打开协议完成阅读' })
          return
        }
      }
      if (+this.thisEntrustType.checkStatus) {
        this.thisEntrustType.agreementList = []
      }
      this.$set(this.thisEntrustType, 'checkStatus',
        +this.thisEntrustType.checkStatus ? '0' : '1')

      if (this.checkModify(false)) {
        this.$store.commit('updateNextBtnDisabled', false)
      } else {
        this.$store.commit('updateNextBtnDisabled', true)
      }
      this.confirmPopupShow = false
    },
    cancelModify () {
      this.thisEntrustType.agreementList = []
      this.confirmPopupShow = false
    },
    // 检查是否有更改的委托项
    checkModify (showNotice) {
      const changedEntrust = this.entrustList.filter(item => {
        return item.checkStatus !== item.entrustStatus
      })
      if (changedEntrust.length === 0) {
        showNotice && _hvueToast({ mes: '委托方式未做改变，无需修改' })
        return false
      }

      return true
    },
    // 打包提交参数
    getSubmitParam() {
      const changedEntrust = this.entrustList.filter(item => {
        return item.checkStatus !== item.entrustStatus
      })
      if (changedEntrust.length === 0) {
        return false
      }

      let readAgreementList = []
      this.entrustList.forEach(entrustItem => {
        if (entrustItem.agreementList && entrustItem.agreementList.length > 0) {
          readAgreementList.push(...entrustItem.agreementList.filter(item => {
            return item.isRead && readAgreementList.every(agreement => {
              return agreement.agreementId !== item.agreementId
            })
          }))
        }
      })
      const params = { // 提交需要的参数
        oriEntrustWay: this.oriEntrustWay, // 原有已开通的所有委托方式
        entrustWay: changedEntrust.map(item => { return item.entrustCode }).join(''), // 现在需要改变的委托方式
        agreementId: readAgreementList.map(item => { return item.agreementId }).join(',') // 协议编号
      }
      return params
    },
    // 查看协议详情
    toDetail (agreementId, agreementName, index) {
      this.$router.push({
        name: 'agreementDetail',
        query: {
          agreementId: agreementId,
          agreementTitle: agreementName,
          agreementIndex: index,
          type: this.$route.query.type
        }
      })
    },
    getProtocol (entrustItem) {
      this.$set(entrustItem, 'agreementList', [])
      getProtocolByType({
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        queryType: '0',
        queryValue: entrustItem.agreeType
      }, {}).then(res => {
        if (res.error_no === '0') {
          let results = res.results || res.DataSet
          const agreementList = []
          for (let i = 0; i < results.length; i++) {
            let el = results[i]
            const readAgreements = $h.getSession('agreementIds')
            el.isShow = true
            el.isRead = readAgreements ? readAgreements.split(',').includes(el.agreementId) : false
            agreementList.push(el)
          }
          this.$set(entrustItem, 'agreementList', agreementList)
        } else {
          _hvueToast({
            icon: 'error',
            mes: res.error_info
          })
        }
      }, err => {
        console.log(err)
      })
    },
    signAgreement () {
      return new Promise((resolve, reject) => {
        let readAgreementList = []
        this.entrustList.forEach(entrustItem => {
          if (entrustItem.agreementList && entrustItem.agreementList.length > 0) {
            readAgreementList.push(...entrustItem.agreementList.filter(item => {
              return item.isRead && readAgreementList.every(agreement => {
                return agreement.agreementId !== item.agreementId
              })
            }))
          }
        })
        if (readAgreementList.length > 0) {
          signAgreement({
            userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
            agreementId: readAgreementList.map(item => { return item.agreementId }).join(','),
            serivalId: this.$parent.publicParam.serivalId,
            flow_current_step: this.$parent.flow.currentStepInfo.flow_current_step_name
          }).then(res => {
            if (res.error_no === '0') {
              resolve()
            } else {
              _hvueToast({
                icon: 'error',
                mes: res.error_info
              })
              reject(new Error(res.error_info))
            }
          }, err => {
            console.log(err)
            reject(err)
          })
          return
        }
        resolve()
      })
    },
    /** ******************************************* 子组件公共方法定义 start *************************************/
    doCheck () {
      // 执行下一步前的校验
      return new Promise(async (resolve, reject) => {
        if (this.checkModify(true)) {
          await this.signAgreement()
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      const params = this.getSubmitParam() // 提交需要的参数
      return params
    },
    putFormData () {
      let formData = {
        entrust: this.getSubmitParam()
      } // 需要保存的表单数据

      let readAgreementList = []
      this.entrustList.forEach(entrustItem => {
        if (entrustItem.agreementList && entrustItem.agreementList.length > 0) {
          readAgreementList.push(...entrustItem.agreementList.filter(item => {
            return item.isRead && readAgreementList.every(agreement => {
              return agreement.agreementId !== item.agreementId
            })
          }))
        }
      })
      if (readAgreementList.length > 0) {
        formData.signProtocol = { agreementId: readAgreementList.map(item => { return item.agreementId }).join(',') }
      }
      return formData
    },
    // 提交后对结果进行处理的方法
    handleResultFunc (res) {
      // 处理完成后，继续执行下一步
      this.$parent.emitNextEvent()
    }
    /** ******************************************* 子组件公共方法定义 end *************************************/
  }
}
</script>

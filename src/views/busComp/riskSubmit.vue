<template>
  <div v-cloak>
    <div class="test_result" :style="{'padding-bottom':divHeight + 'px'}">
      <div class="test_level risk">
        <div class="test_canvas">
          <span style="display:none;" ref="testData">{{custTypeCheck.paperScore}}</span>
          <div class="info">
            <strong>{{custTypeCheck.riskLevelDesc}}</strong>
            <!-- <span>{{custTypeCheck.riskLevel}}</span> -->
          </div>
          <canvas ref="testCanvas" width="320" height="320"></canvas>
        </div>
        <p>
          <a class="re_testbtn" @click.stop="reTest">重新测评</a>
        </p>
      </div>
      <div class="approp_info spel">
        <ul>
          <li>
            <span class="tit">拟投资期限</span>
            <p>{{custTypeCheck.investTermDesc}}</p>
          </li>
          <li>
            <span class="tit">拟投资品种</span>
            <p>{{custTypeCheck.investTypeDesc}}</p>
          </li>
        </ul>
      </div>
      <div class="approp_tips" v-html="content"></div>
    </div>
    <div ref="bottom_check" class="bottom_check">
      <div class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: signProtocolCheck }"
          @click.stop="signProtocolCheck = !signProtocolCheck"
        ></span>
        <label>我已知晓并同意签署</label>
      </div>
      <div class="ce_btn">
        <a class="ui button block rounded" @click.stop="nextStep">确定</a>
      </div>
    </div>
  </div>
</template>

<script>
import { getProtocolById } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  name: 'riskSubmit',
  data () {
    return {
      riskState: 1, // 测评是否过期：1未过期，2已过期，3快过期
      content: '', // 协议
      signProtocolCheck: false,
      custTypeCheck: {},
      agreementId: '',
      divHeight: '0'
    }
  },
  mounted () {
    this.$nextTick(function () {
      // Code that will run only after the
      // entire view has been rendered
      let canvas = this.$refs.testCanvas
      let ctx = canvas.getContext('2d')
      let W = canvas.width
      let H = canvas.height
      let deg = 14,
        new_deg = 0,
        dif = 0
      let loop, re_loop
      let isEffect = this.pageParam.isEffective == 1,
        deColor,
        dotColor
      let t_data

      // 判断是否过期
      if (isEffect) {
        deColor = '#ffffff'
        dotColor = '#B80205'
        t_data = this.$refs.testData.innerHTML
      } else {
        deColor = '#fdf3e2'
        dotColor = 'transparent'
        t_data = 0
      }

      function init () {
        ctx.clearRect(0, 0, W, H)
        ctx.beginPath()
        ctx.strokeStyle = deColor
        ctx.lineWidth = 5
        ctx.arc(
          W / 2,
          H / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          (Math.PI * 39) / 18,
          false
        )
        ctx.stroke()

        let r = (((2.4 * 100) / 86) * (deg - 14) * Math.PI) / 180
        ctx.beginPath()
        ctx.strokeStyle = dotColor
        ctx.lineWidth = 5
        ctx.arc(
          W / 2,
          H / 2 + 30,
          130,
          (Math.PI * 5) / 6,
          r + (Math.PI * 5) / 6,
          false
        )
        ctx.stroke()

        let r2 =
          (((2.4 * 100) / 86) * (deg - 14) * Math.PI) / 180 + (Math.PI * 1) / 3
        ctx.beginPath()
        ctx.fillStyle = dotColor
        ctx.arc(
          W / 2 - 130 * Math.sin(r2),
          H / 2 + 30 + 130 * Math.cos(r2),
          7,
          -180,
          true
        )
        ctx.fill()
      }
      function draw () {
        new_deg = t_data
        dif = new_deg - deg
        loop = setInterval(to, 500 / dif)
      }
      function to () {
        if (deg == new_deg) {
          clearInterval(loop)
        }
        if (deg < new_deg) {
          deg++
        }
        init()
      }
      draw()
    })
  },
  activated () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.divHeight = this.$refs.bottom_check.clientHeight
    this.custTypeCheck = JSON.parse(this.pageParam[0].custTypeCheck)
    this.custTypeCheck = this.custTypeCheck[0]
    let agree = JSON.parse(this.pageParam[0].agreementContent)
    this.agreementId = agree[0].agreementId
    this.getProtocol()
  },
  methods: {
    getProtocol () {
      let param = {
        agreementId: this.agreementId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId,
        keyWords: JSON.stringify({'riskLevelDesc': this.custTypeCheck.riskLevelDesc})
      }
      getProtocolById(param)
        .then(data => {
          if (data.error_no === '0') {
            let results = data.results || data.DataSet
            this.content = results[0].agreeContent
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    // 重新测评
    reTest () {
      // 调用父组件返回上一步方法
      this.$parent.back({})
    },
    checkSubmit () {
      if (!this.signProtocolCheck) {
        _hvueToast({ mes: '您还未勾选同意协议' })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = Object.assign({}, this.custTypeCheck, {
        agreementId: this.agreementId,
        keyWords: JSON.stringify({'riskLevelDesc': this.custTypeCheck.riskLevelDesc})
      })
      return params
    },
    putFormData () {
      let formData = {
        riskSubmit: this.custTypeCheck,
        signProtocol: {
          agreementId: this.agreementId
        }
      }
      return formData
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    }
  }
}
</script>

<style scoped>
.bottom_check {
  position: fixed;
  bottom: 0;
  border-top: 1px solid #f9f9f9;
  z-index: 999;
  background: white;
  width: 100%;
}
</style>

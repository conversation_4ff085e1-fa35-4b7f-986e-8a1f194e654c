<template>
  <div>
    <div v-show="showUserInfo">
      <p style="font-size:0.14rem;color:#999;padding:0.1rem 0.15rem 0;">以下标*号的为必填项，请完善：</p>
      <div class="ste_cont" v-for="(item, index) in infoList" :key="index">
        <ul>
          <li v-for="(subItem, subIndex) in item"
            :key="subIndex"
            @click.stop="!subItem.isModifying && subItem.editable && editInfoItem(subIndex)">
            <span class="tit"><em class="imp" v-if="subItem.isRequired">*</em>{{ subItem.name }}</span>
            <div class="cont">
              <span :class="{ default: subItem.isModifying }">
                {{ subItem.isModifying ? updateField[subIndex].value : subItem.value }}
              </span>
            </div>
            <em v-show="subItem.isModifying" class="red_status">修改中</em>
            <span v-show="!subItem.isModifying && subItem.editable" class="lk"></span>
          </li>
        </ul>
      </div>
    </div>

    <inputBox
      v-if="inputBox.show"
      v-model="inputBox.show"
      :title="inputBox.title"
      :checkType="inputBox.checkType"
      :minLength="inputBox.minLength"
      :maxLength="inputBox.maxLength"
      :value="inputBox.value"
      :placeholder="inputBox.placeholder"
      :inputTips="inputBox.inputTips"
      @selCallback="selCallback"
    ></inputBox>

    <selBox
      v-if="selBox.show"
      v-model="selBox.show"
      :title="selBox.title"
      :defaultStr="selBox.defaultStr"
      :category="selBox.category"
      :initData="selBox.initData"
      @selCallback="selCallback"
    ></selBox>

    <provincesPicker
      v-if="pickerShow"
      v-model="pickerShow"
      :address="infoList[4].address.value"
      :province="infoList[4].address.province"
      :city="infoList[4].address.city"
      :area="infoList[4].address.area"
      :detailed="infoList[4].address.detailed"
      @selCallback="selCallback"
    ></provincesPicker>

    <secRelation
      v-if="showSecRelationInput"
      v-model="showSecRelationInput"
      :name="infoList[5].secRelationName.value"
      :relation="infoList[5].secRelationName.relation"
      :relationKey="infoList[5].secRelationName.relationKey"
      :mobile="infoList[5].secRelationName.mobile"
      @selCallback="selCallback"
    ></secRelation>

    <selBox v-model="controlPersonBoxShow" title="实际控制人">
      <div class="wtclass_main">
        <ul class="wtclass_list">
          <li>
            <span>账户实际控制人是本人</span>
            <div
              class="ui switch"
              @click.stop="controlPersonCall('controlPerson')"
              :class="{
                checked: infoList[6].controlPerson.selected === 0
              }"
            >
              <div class="ui switch-inner">
                <div class="ui switch-arrow"></div>
              </div>
            </div>
          </li>
          <li>
            <span>账户实际受益人是本人</span>
            <div
              class="ui switch"
              @click.stop="controlPersonCall('benefitPerson')"
              :class="{
                checked: infoList[6].benefitPerson.selected === 0
              }"
            >
              <div class="ui switch-inner">
                <div class="ui switch-arrow"></div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <p class="bot_tips"></p>
      <div class="ce_btn mt20">
        <a class="ui button rounded block" @click.stop="selCallback">确定</a>
      </div>
    </selBox>

    <selCreditRecord
      v-if="showCreditRecordBox"
      v-model="showCreditRecordBox"
      :title="selBox.title"
      :defaultStr="selBox.defaultStr"
      :category="selBox.category"
      :initData="selBox.initData"
      @selCallback="selCallback"
    ></selCreditRecord>

    <selBox v-model="showTaxBox" title="选择税收居民身份">
      <div class="tax_checklist">
        <ul>
          <li
            v-for="(item, index) in taxTypeData"
            :class="{ checked: index === 0 }"
            v-throttle
            @click.stop="selTaxClick(index)"
            :key="index"
          >
            <h5>{{ item.type }}</h5>
            <p>{{ item.info }}</p>
          </li>
        </ul>
      </div>
      <!-- <div class="rule_check">
        <span
          class="icon_check"
          :class="{ checked: selTaxChecked }"
          @click.stop="selTaxChecked = !selTaxChecked"
        ></span>
        <label
          >本人确认_上述信息真实、准确和完整，且在这些信息发生变更时，将在30日内通知贵机构，否则，本人讲承担由此造成的不利后果。</label
        >
      </div> -->
      <div class="ce_btn mt20">
        <a class="ui button rounded block" @click.stop="selCallback">确定</a>
      </div>
      <div class="tax_infotips">
        <p>说明:</p>
        <p>
          中国税收居民是指在中国境内有住所，或者无住所而在境内居住满一年的个人，不包括香港、澳门、台湾地区的税收居民。本表所称非居民是指中国税收居民以外的个人。《非居民金融账户涉税信息尽职调查管理办法》相关信息详见国家税务总局网站(
          <a
            href="http://www.chinatax.gov.cn/n810341/n810755/c2623078/content.html"
            >http://www.chinatax.gov.cn/n810341/n810755/c2623078/content.html</a
          >)
        </p>
      </div>
    </selBox>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import selBox from '@/components/selBox' // 标准选择
import inputBox from '@/components/inputBox' // 标准输入
import provincesPicker from '@/components/provincesPicker' // 地址选择
import selCreditRecord from '@/components/selCreditRecord' // 不良诚信记录选择
import secRelation from '@/components/secRelation' // 紧急联系人输入
import { queryDictionary } from '@/common/util'
import { fillRiskLevel, formatIdno, formatDate } from '@/common/filter'

export default {
  props: {
    pageParam: {
      type: Array
    }
  },
  components: {
    headComponent,
    selBox,
    inputBox,
    provincesPicker,
    selCreditRecord,
    secRelation
  },
  data() {
    return {
      userInfoModify: {}, // 提交表单的参数
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      editInfoKey: '',
      infoList: [{
        riskLevel: {
          name: '风险测评',
          value: '',
          editable: false
        }
      }, {
        name: {
          name: '姓名',
          value: '',
          editable: false
        },
        identityNum: {
          name: '身份证号',
          value: '',
          editable: false
        },
        validityDate: {
          name: '身份证有效期',
          value: '',
          editable: false
        },
        papersAddr: {
          name: '证件地址',
          value: '',
          editable: false
        }
      }, {
        branchName: {
          name: '营业部',
          value: '',
          editable: false
        },
        branchAddress: {
          name: '营业部地址',
          value: '',
          editable: false
        },
        branchTel: {
          name: '营业部联系方式',
          value: '',
          editable: false
        }
      }, {
        mobile: {
          name: '手机号',
          isRequired: true,
          value: '',
          editable: true,
          linkPage: {
            name: 'business',
            query: { type: 'xgsjh', name: '修改手机号' }
          }
        },
        telephone: {
          name: '固定电话',
          value: '',
          editable: true,
          editType: 'standardInput',
          checkType: 'tel',
          maxLength: 20
        }
      }, {
        education: {
          name: '学历',
          isRequired: true,
          value: '',
          editable: true,
          type: 'ismp.adapter',
          editType: 'standardSelect'
        },
        industryType: {
          name: '行业',
          isRequired: true,
          value: '',
          editable: true,
          type: 'ismp.industry',
          editType: 'standardSelect'
        },
        occupation: {
          name: '职业',
          isRequired: true,
          value: '',
          editable: true,
          type: 'ismp.occupational',
          editType: 'standardSelect'
        },
        jobTitle: {
          name: '职务',
          isRequired: true,
          value: '',
          editable: true,
          type: 'ismp.jobs',
          editType: 'standardSelect',
          isSubmitValue: true // 是否只提交value值
        },
        // income: {
        //   name: '年收入',
        //   isRequired: true,
        //   value: '',
        //   editable: true,
        //   type: 'ismp.income',
        //   editType: 'standardSelect'
        // },
        companyName: {
          name: '工作单位',
          isRequired: true,
          value: '',
          editable: true,
          editType: 'standardInput',
          checkType: '',
          maxLength: 30,
          minLength: 4
        },
        address: {
          name: '家庭住址',
          value: '',
          editable: true
        }
      }, {
        postcode: {
          name: '邮编',
          value: '',
          editable: true,
          editType: 'standardInput',
          checkType: 'postcode',
          maxLength: 6
        },
        email: {
          name: '电子邮箱',
          value: '',
          editable: true,
          editType: 'standardInput',
          checkType: 'email',
          maxLength: 30
        },
        secRelationName: {
          name: '紧急联系人',
          value: '',
          editable: true,
          relation: '',
          relationKey: '',
          mobile: '',
          relationType: 'ismp.relation'
        }
      }, {
        controlPerson: {
          name: '账户实际控制人',
          isRequired: true,
          value: '',
          editable: true
        },
        benefitPerson: {
          name: '账户实际受益人',
          isRequired: true,
          value: '',
          editable: true
        }
      }, {
        creditRecord: {
          name: '不良诚信记录',
          isRequired: true,
          value: '',
          editable: true,
          type: 'ismp.credit',
          isMulti: true
        },
        taxResidentPerson: {
          name: '涉税信息',
          isRequired: true,
          value: '',
          editable: true,
          type: 'taxTypeData'
        }
      }],
      selBox: {
        show: false,
        title: '',
        category: '',
        defaultStr: '',
        initData: []
      },
      inputBox: {
        show: false,
        title: '',
        placeholder: '',
        value: '',
        checkType: '',
        maxLength: ''
      },
      pickerShow: false,
      controlPersonBoxShow: false,
      taxTypeData: [{
        index: '1',
        type: '仅为中国税收居民',
        info: '中国境内有住所，或者无住所而在境内居住满一年的个人'
      }, {
        index: '2',
        type: '仅为非居民',
        info: '中国税收居民以外的个人'
      }, {
        index: '3',
        type: '既是中国税收居民又是其他国家地区',
        info: '中国境内有住所，同时在国外境内也有住所的个人'
      }],
      showControlPersonBox: false,
      showCreditRecordBox: false,
      showSecRelationInput: false,
      showTaxBox: false,
      selTaxChecked: false,
      updateField: {} // 修改中的字段集合
    }
  },
  computed: {
    showUserInfo() {
      if (
        !this.selBox.show &&
        !this.inputBox.show &&
        !this.pickerShow &&
        !this.showCreditRecordBox &&
        !this.showTaxBox &&
        !this.controlPersonBoxShow &&
        !this.showSecRelationInput
      ) {
        this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
        return true
      } else {
        return false
      }
    }
  },
  activated() {
    // 更改下一步按钮的文字
    this.$store.commit('updateBusinessNextBtnStatus', '提交保存')

    // 合并并缓存用户信息
    const synchronizedUserInfo = JSON.parse(
      this.pageParam[0].synchronizedUserInfo
    )
    const custTypeCheck = JSON.parse(this.pageParam[0].custTypeCheck)
    Object.assign(this.ygtUserInfo, synchronizedUserInfo[0])
    Object.assign(this.ygtUserInfo, custTypeCheck[0])
    this.ygtUserInfo.riskLevelDesc = custTypeCheck[0].riskLevelDesc
    $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })

    // 将用户信息填充到页面中
    this.reloadData().then(() => { // 填充页面数据
      this.checkModifyingItem()
      // 有内容修改时开启返回确认弹窗并更改弹窗文字
      if (this.checkModify()) {
        this.$store.commit('updateReturnHomeConfirmText', '您还有修改的信息未提交，是否确认返回，未提交则信息不会保存')
      } else {
        this.$store.commit('updateReturnHomeConfirmFlag', false)
      }
    })
  },
  methods: {
    // 填充页面数据
    async reloadData () {
      return new Promise(async (resolve, reject) => {
        for (let i = 0; i < this.infoList.length; i++) {
          let item = this.infoList[i]
          for (let key in item) {
            if (this.ygtUserInfo[key]) {
              this.$set(item[key], 'value', this.ygtUserInfo[key])
            }
            // 风险测评
            if (key === 'riskLevel' && this.ygtUserInfo.riskLevelDesc) {
              this.$set(item[key], 'value', fillRiskLevel(this.ygtUserInfo.riskLevelDesc, this.ygtUserInfo.isEffective))
            }
            // 身份证号
            if (key === 'identityNum' && this.ygtUserInfo.identityNum) {
              this.$set(item[key], 'value', formatIdno(this.ygtUserInfo.identityNum))
            }
            // 身份证有效期
            if (key === 'validityDate' && this.ygtUserInfo.validityEnd) {
              this.$set(item[key], 'value', formatDate(this.ygtUserInfo.validityEnd, 'YYYY-MM-DD'))
            }
            // 紧急联系人
            if (key === 'secRelationName') {
              if (this.ygtUserInfo.secRelationPhone) {
                item[key].mobile = this.ygtUserInfo.secRelationPhone
              }
              item[key].relation = this.ygtUserInfo.secRelationType
              item[key].relationKey = await this.queryDictForId(item[key].relationType, item[key].relation)
            }
            // 数据字典匹配
            if (this.ygtUserInfo[key] && item[key].type) {
              if (item[key].isSubmitValue) {
                if (item[key].value) {
                  item[key].id = await this.queryDictForId(item[key].type, item[key].value)
                }
              } else {
                await this.queryDictForValue(item[key].type, key, item[key], item[key].isMulti)
              }
            }
            // 涉税信息
            if (key === 'taxResidentPerson' && item[key].value) {
              const matchType = this.taxTypeData.filter(taxType => {
                return taxType.index === item[key].value
              })
              this.$set(item[key], 'value', matchType[0].type)
              this.$set(item[key], 'id', matchType[0].index)
            }
          }
        }
        this.reloadUpdateField()
        resolve()
      })
    },
    // 加载修改中的字段集合
    reloadUpdateField () {
      const updateField = JSON.parse(this.pageParam[0].updateField)[0]
      const dictItem = ['education', 'occupation', 'income', 'taxResidentPerson', 'industryType']
      let updateFieldHandled = {}
      for (let i in updateField) {
        if (dictItem.includes(i)) {
          updateFieldHandled[i] = {
            id: updateField[i],
            value: ''
          }
        } else {
          updateFieldHandled[i] = {
            value: updateField[i]
          }
        }
      }
      this.updateField = updateFieldHandled || {}
    },
    // 查数据字典获取对应的字段名
    queryDictForValue (type, name, item, isMulti) {
      const ygtUserInfo = this.ygtUserInfo
      return new Promise((resolve, reject) => {
        queryDictionary({
          type: type,
          key: isMulti ? '' : item.id || ygtUserInfo[name]
        }, function(d) {
          // 单选
          if (!isMulti) {
            if (!d.value) {
              resolve()
              return
            }
            Object.assign(item, {
              value: d.value,
              id: d.key,
              selected: d.index
            })
            resolve()
            return
          }

          // 多选
          let selectedArr = []
          let keys = ygtUserInfo[name].split(',')
          for (let i = 0; i < keys.length; i++) {
            for (let n = 0; n < d.length; n++) {
              let c = d[n]
              if (keys[i] === c.key) {
                selectedArr.push(c)
                break
              }
            }
          }
          const val = selectedArr.map(item => { return item.value })
          const key = selectedArr.map(item => { return item.key })
          const index = selectedArr.map(item => { return item.index })
          Object.assign(item, {
            value: val.join(','),
            id: key.join(','),
            selected: index.join(',')
          })
          resolve()
        })
      })
    },
    // 查数据字典获取对应的id
    queryDictForId (type, value) {
      return new Promise((resolve, reject) => {
        queryDictionary({
          type: type
        }, function(d) {
          let resultId = ''
          for (let n = 0; n < d.length; n++) {
            let c = d[n]
            if (c.value === value) {
              resultId = c.key
            }
          }
          resolve(resultId)
          return resultId
        })
      })
    },
    // 检查是否有正在修改的项
    async checkModifyingItem() {
      for (let i in this.updateField) {
        for (let j = 0; j < this.infoList.length; j++) {
          let item = this.infoList[j]
          for (let key in item) {
            if (key === i) {
              if (item[key]) {
                this.$set(item[key], 'isModifying', true)
              }
              if (this.updateField[i].id) {
                if (key === i && item[key].type) {
                  await this.queryDictForValue(item[key].type, key, this.updateField[i])
                }
                // 涉税信息
                if (i === 'taxResidentPerson') {
                  const matchType = this.taxTypeData.filter(taxType => {
                    return taxType.index === this.updateField[i].id
                  })
                  // this.updateField[i] = Object.assign({}, this.updateField[i], {
                  //   value: matchType[0] ? matchType[0].type : '请选择'
                  // })
                  this.$set(this.updateField, i, Object.assign({}, this.updateField[i], {
                    value: matchType[0] ? matchType[0].type : '请选择'
                  }))
                }
              }
            }
          }
        }
      }
    },
    // 编辑基本信息
    editInfoItem (infoType) {
      this.infoList.forEach(item => {
        if (item[infoType]) {
          // 跳转其他页面修改
          if (item[infoType].linkPage) {
            this.$store.commit('updateReturnHomeConfirmFlag', false)
            this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
            this.$router.push(item[infoType].linkPage)
            return
          }
          // 打开标准选择页面
          if (item[infoType].editType === 'standardSelect') {
            this.openSelector(item, infoType, item[infoType].name)
            return
          }
          // 打开标准输入页面
          if (item[infoType].editType === 'standardInput') {
            this.openInputer(item, infoType, item[infoType].name)
            return
          }
          // 打开特殊选择或输入组件
          this.openEditor(item, infoType, item[infoType].name)
        }
      })
    },
    // 检查是否有修改项
    checkModify () {
      this.userInfoModify = {} // 清空修改项集合
      const userinfo = this.ygtUserInfo
      let submitParam = {}

      this.infoList.forEach(item => {
        for (let key in item) {
          if (userinfo[key] !== undefined && item[key].editable) {
            if (item[key].type) {
              if (item[key].isSubmitValue) {
                if (item[key].value && item[key].value !== userinfo[key]) {
                  submitParam[key] = item[key].value
                }
              } else {
                if (item[key].id && item[key].id !== userinfo[key]) {
                  submitParam[key] = item[key].id
                }
              }
            } else if (item[key].value !== userinfo[key]) {
              submitParam[key] = item[key].value
            }
            if (key === 'secRelationName') { // 紧急联系人
              if (item[key].relation !== userinfo.secRelationType) {
                submitParam.secRelationType = item[key].relation
              }
              if (item[key].mobile !== userinfo.secRelationPhone) {
                submitParam.secRelationPhone = item[key].mobile
              }
            }
            if (key === 'taxResidentPerson') { // 涉税信息
              if (item[key].id && item[key].id !== userinfo[key]) {
                submitParam.taxResidentCountry = item[key].id
              }
            }
          }
        }
      })

      // console.log('submitParam', submitParam)
      Object.assign(this.userInfoModify, submitParam, {})
      if (JSON.stringify(submitParam) === '{}') {
        return false
      } else {
        return true
      }
    },
    // 打开标准选择组件
    openSelector (item, type, title) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.editInfoKey = type
      this.editType = item[type].editType

      this.selBox.title = title
      this.selBox.category = item[type].type
      this.selBox.defaultStr = item[type].id
      this.selBox.show = true
    },
    // 打开标准输入组件
    openInputer (item, type, title) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.editInfoKey = type
      this.editType = item[type].editType

      this.inputBox.title = title
      this.inputBox.placeholder = item[type].placeholder
      this.inputBox.checkType = item[type].checkType
      this.inputBox.value = item[type].value
      this.inputBox.maxLength = item[type].maxLength
      this.inputBox.minLength = item[type].minLength
      this.inputBox.inputTips = item[type].inputTips
      this.inputBox.show = true
    },
    // 打开特殊选择或输入组件
    openEditor (item, type, title) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      this.editInfoKey = type
      this.editType = ''

      if (type === 'address') { // 联系地址
        this.pickerShow = true
      } else if (type === 'controlPerson' || type === 'benefitPerson') { // 账户实际控制人、受益人
        Object.assign(item.controlPerson, { selected: item.controlPerson.value === this.ygtUserInfo.name
          ? 0 : 1 })
        Object.assign(item.benefitPerson, { selected: item.benefitPerson.value === this.ygtUserInfo.name
          ? 0 : 1 })
        this.controlPersonBoxShow = true
      } else if (type === 'creditRecord') { // 不良诚信记录
        // 打开选择器
        this.selBox.title = title
        this.selBox.category = item[type].type
        this.selBox.defaultStr = item[type].id
        this.showCreditRecordBox = true
      } else if (type === 'taxResidentPerson') { // 涉税信息
        this.showTaxBox = true
      } else if (type === 'secRelationName') { // 紧急联系人
        this.showSecRelationInput = true
      }
    },
    // 涉税信息选择
    selTaxClick(index) {
      this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
      if (index !== 0) {
        _hvueAlert({
          title: '提示',
          mes:
            '如您为其他国家（地区）的税收居民，请到营业部现场办理',
          opts: [{ txt: '我已知晓' }]
        })
      }
    },
    // 选择器组件回调方法
    selCallback(d) {
      this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      const type = this.editInfoKey
      let returnData = null

      if (this.editType === 'standardSelect') {
        returnData = {
          value: d.data.value,
          id: d.data.key,
          selected: d.index,
          detailedInfo: d.detailedInfo || ''
        }
      } else if (this.editType === 'standardInput') {
        returnData = {
          value: d.value
        }
      } else if (type === 'address') { // 家庭住址
        returnData = {
          value: d.address,
          province: d.province,
          city: d.city,
          area: d.area,
          detailed: d.detailed
        }
      } else if (type === 'creditRecord') { // 不良诚信记录
        this.showCreditRecordBox = false
        const val = d.map(item => { return item.value })
        const key = d.map(item => { return item.key })
        const index = d.map(item => { return item.index })
        returnData = {
          value: val.join(','),
          id: key.join(','),
          selected: index.join(',')
        }
      } else if (type === 'taxResidentPerson') { // 涉税信息
        // if (!this.selTaxChecked) {
        //   _hvueToast({ mes: '请勾选本人确认' })
        //   return
        // }
        returnData = {
          id: this.taxTypeData[0].index,
          value: this.taxTypeData[0].type
        }
      } else if (type === 'secRelationName') { // 紧急联系人
        returnData = {
          value: d.name,
          relation: d.relation,
          relationKey: d.relationKey,
          mobile: d.mobile
        }
      }

      this.infoList.forEach(item => {
        for (let key in item) {
          if (key === type) {
            if (type === 'controlPerson' || type === 'benefitPerson') { // 账户实际控制人、受益人
              if (item.controlPerson.selected === 0) {
                Object.assign(item.controlPerson, {
                  value: this.ygtUserInfo.name
                })
              } else {
                Object.assign(item.controlPerson, {
                  value: ''
                })
              }
              if (item.benefitPerson.selected === 0) {
                Object.assign(item.benefitPerson, {
                  value: this.ygtUserInfo.name
                })
              } else {
                Object.assign(item.benefitPerson, {
                  value: ''
                })
              }
              return
            }
            Object.assign(item[type], returnData)
            if (returnData.detailedInfo && type === 'occupation') { // 职业其他
              Object.assign(item.occupationOther, {
                value: returnData.detailedInfo
              })
            }
          }
        }
      })
      this.controlPersonBoxShow = false
      this.showTaxBox = false
      this.showSecRelationInput = false

      // 有内容修改时开启返回确认弹窗并更改弹窗文字
      if (this.checkModify()) {
        this.$store.commit('updateReturnHomeConfirmText', '您还有修改的信息未提交，是否确认返回，未提交则信息不会保存')
      } else {
        this.$store.commit('updateReturnHomeConfirmFlag', false)
      }
    },

    // 控制人受益人组件回调方法
    controlPersonCall(type) {
      // this.$store.commit('updateBusinessNextBtnStatus', true) // 显示下一步按钮
      let selectedValue = null
      let selectedInfoItem = null
      this.infoList.forEach(item => {
        for (let key in item) {
          if (key === type) {
            selectedValue = item[key].selected
            selectedInfoItem = item
          }
        }
      })
      if (this.ygtUserInfo[type] && selectedValue === 0) { // 账户实际控制人、受益人
        _hvueAlert({
          title: '提示',
          mes:
            '根据监管要求，账户实际控制人和实际受益人必须为客户本人，无法线上修改'
        })
      } else {
        this.$set(selectedInfoItem, type, { ...selectedInfoItem[type], selected: +!selectedInfoItem[type].selected })
      }
    },
    // 提交前校验输入项
    checkSubmit() {
      let noticeItem = null
      let noticeMessage = ''
      this.infoList.forEach(item => {
        for (let key in item) {
          if (item[key].isRequired && !item[key].isModifying) {
            if ((item[key].editType === 'standardSelect' || key === 'creditRecord') &&
              !item[key].isSubmitValue && !item[key].id) {
              if (!noticeItem) noticeItem = item[key]
              if (!noticeMessage) noticeMessage = `请选择${item[key].name}`
              return
            }
            if (!item[key].value) {
              if (!noticeItem) noticeItem = item[key]
              if (!noticeMessage) noticeMessage = `请完善${item[key].name}`
              return
            }
            if ((key === 'controlPerson' || key === 'benefitPerson') && item[key].value !== this.ygtUserInfo.name) {
              if (!noticeMessage) noticeMessage = '根据监管要求，账户实际控制人和实际受益人必须为客户本人'
            }
          }
        }
      })
      if (noticeMessage) {
        _hvueToast({
          mes: noticeMessage
        })
        return false
      }

      if (!this.checkModify()) {
        _hvueToast({
          mes: '您未做资料修改，无需提交'
        })
        return false
      }
      return true
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = this.userInfoModify
      return params
    },
    /**
     * 表单参数打包
     */
    putFormData() {
      let formData = {
        userInfoModify: this.userInfoModify
      }
      return formData
    },
    back() {
      this.$router.go(-1)
    }
  }
}
</script>

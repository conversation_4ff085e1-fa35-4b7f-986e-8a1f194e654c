<template>
  <div>
    <h5 class="com_title">请选择需要开通或调整的账户</h5>
    <div class="acct_table">
      <ul class="title">
        <li class="m_soc_3">账户</li>
        <li class="m_soc_2">状态</li>
        <li class="m_soc_3">权限</li>
        <li class="m_soc_2">选择</li>
      </ul>
      <ul class="data">
        <li v-for="(item,index) in pageParam" :key="index">
          <div class="item m_soc_3">{{item.stockAccount}}</div>
          <div class="item m_soc_2">{{item.holderStatus|filAccountState}}</div>
          <div class="item m_soc_3">{{statusDesc(item)}}</div>
          <div class="item m_soc_2">
            <span
              v-if="item.holderStatus == 0"
              class="icon_check"
              :class="{checked : index == currentChecked}"
              @click.stop="addCheckedClass(index,item.stockAccount ,item.openType,item.stkbd)"
            ></span>
          </div>
        </li>
      </ul>
    </div>
    <dl v-if="accountFlag" class="note_tips">
      <dd>选择已开通权限的账户，则可调整此账户的权限类别</dd>
      <dd>选择未开通权限的账户，则可为此账户新开权限</dd>
    </dl>
    <div v-else class="cond_tips">
      <p>您没有正常的账户，请先将账户规整至正常状态</p>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      openedType: '',
      accountFlag: false, // 当前账户是否满足办理条件
      currentChecked: '-1', // 当前选中的账户index
      checkedAccount: '', // 选中的账号
      market: ''
    }
  },
  created () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ title: '温馨提示', mes: '抱歉，未查询到账户' })
      return false
    }
    for (let i = 0; i < this.pageParam.length; i++) {
      let el = this.pageParam[i]
      if (el.holderStatus == '0') {
        this.accountFlag = true
      }
    }
    if (this.accountFlag) {
      // 账户条件通过，可以下一步
      this.$store.commit('updateNextBtnText', '下一步') // 修改下一步按钮
    } else {
      // 账户条件不通过，返回
      this.$store.commit('updateNextBtnText', '放弃办理') // 修改下一步按钮
    }
  },
  mounted () {
    this.$on('putFormData', () => {
      return this.putFormData()
    })
    this.$on('reqParamPackage', () => {
      return this.reqParamPackage()
    })
  },
  activated () {
    this.currentChecked = -1
    this.openedType = ''
    this.checkedAccount = ''
    this.market = ''
  },
  methods: {
    statusDesc (item) {
      if (item.openStatus == '1') {
        switch (
          item.openType // 当前权限 新三板开通类型 3：一类 4：二类 5：三类 6：四类 7：受限户
        ) {
          case '3':
            return '已开通/一类'
            break
          case '4':
            return '已开通/二类'
            break
          case '5':
            return '已开通/三类'
            break
          case '6':
            return '已开通/四类'
            break
          case '7':
            return '已开通/受限户'
            break
          default:
            return '已开通'
            break
        }
      } else {
        return '未开通'
      }
    },
    addCheckedClass (index, stockAccount, openType, market) {
      if (this.currentChecked == index) {
        this.currentChecked = -1
        this.openedType = ''
        this.market = ''
        this.checkedAccount = ''
        return
      }
      this.currentChecked = index
      this.checkedAccount = stockAccount
      this.openedType = openType
      this.market = market
    },
    checkSubmit () {
      if (!this.checkedAccount) {
        _hvueToast({ mes: '请选择账户' })
        return false
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      let _this = this
      return new Promise((resolve, reject) => {
        if (_this.accountFlag) {
          if (_this.checkSubmit() == true) {
            // 可以下一步
            resolve()
          }
        } else {
          // 账户条件不通过，返回
          _this.$router.push({ name: 'index' })
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        market: this.market,
        stockAccount: this.checkedAccount,
        openType: this.openedType
      }
      return params
    },
    putFormData () {
      let formData = {
        chooseAccount: {
          //   transFlag: "0",
          market: this.market,
          stockAccount: this.checkedAccount
        }
      }
      return formData
    }
  }
}
</script>

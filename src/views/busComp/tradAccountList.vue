<!-- 增开股东户-股东账户列表（暂未使用） -->
<template>
  <div>
    <div v-show="szaAccountList.length>0" class="cond_box">
      <h5 class="title">您已开通的深A账户</h5>
      <ul class="acct">
        <li v-for="(item,index) in szaAccountList" :key="index">
          <div class="cont">
            <p>
              <span>{{item.stkbdName}} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus)">{{item.holderName}}</em>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="shaAccountList.length>0" class="cond_box">
      <h5 class="title">您已开通的沪A账户</h5>
      <ul class="acct">
        <li v-for="(item,index) in shaAccountList" :key="index">
          <div class="cont">
            <p>
              <span>{{item.stkbdName}} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus)">{{item.holderName}}</em>
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div v-show="!nextFlag" class="cond_tips">
      <p>抱歉，您已满三户，无法继续开户</p>
    </div>
  </div>
</template>

<script>
export default {
  props: ["pageParam"],
  data() {
    return {
      szaAccountList: [],
      shaAccountList: [],
      nextFlag: false
    };
  },
  activated() {
    let list = this.pageParam;
    this.szaAccountList = [];
    this.shaAccountList = [];
    for (let i = 0; i < list.length; i++) {
      let el = list[i];
      if (el.stkbd === "00") {
        this.szaAccountList.push(el);
      } else if (el.stkbd === "10") {
        this.shaAccountList.push(el);
      }
    }
    if (this.szaAccountList.length > 2 && this.shaAccountList.length > 2) {
      this.$store.commit("updateNextBtnText", "返回首页");
      this.nextFlag = false;
    } else {
      this.$store.commit("updateNextBtnText", "下一步");
      this.nextFlag = true;
    }
  },
  methods: {
    getAccountStyle(holderStatus) {
      if (holderStatus == "0") {
        return "normal_span";
      } else {
        return "assign_span";
      }
    },
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.nextFlag) {
          // 可以下一步
          resolve();
        } else {
          // 返回业务办理首页;
          this.$router.push({ name: "index" });
        }
      });
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {};
      return params;
    },
    putFormData() {
      let formData = {};
      return formData;
    }
  }
};
</script>

<template>
  <div>
    <div class="agree_main">
      <h5 class="title">请选择要开通的账户</h5>
      <ul class="account_list">
        <li
          v-for="(item,index) in pageParam"
          :class="{ disable : (item.openStatus == '1' || item.holderStatus != '0')}"
          :key="index"
        >
          <p>
            <span
              :class="accountStyle(item)"
              @click.stop="accSelEvent(item)"
            >{{item.marketName}} {{item.stockAccount}}</span>
          </p>
          <span
            v-if="item.openStatus != '0' || item.holderStatus != '0'"
            class="status"
          >{{ item.holderStatus | filAccountState(item.regFlag, item.openStatus, item.market, item.holderName) }}</span>
        </li>
      </ul>
    </div>
    <div class="cond_tips" v-show="allOpened">
      <p>抱歉，您在我司没有可选的账户，暂时无法办理</p>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data () {
    return {
      hasOpenedAccount: false, // 有已经开通权限的账户
      selectedIndexes: [],
      submitAccountList: [],
      allOpened: true// 是否已全部开通（一般业务都在前置步骤拦截了，创业板需求不拦截，单独在选择账户步骤拦截，无语）
    }
  },
  mounted () {
    this.selectedIndexes = []
    this.$on('putFormData', () => {
      return this.putFormData()
    })
    this.$on('reqParamPackage', () => {
      return this.reqParamPackage()
    })
  },
  activated () {
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到账号列表', title: '提示' })
    }
    $h.clearSession('isOpenCredit')
    this.allOpened = true
    this.submitAccountList = []
    this.hasOpenedAccount = false
    this.pageParam.forEach(element => {
      if (element && element.openStatus === '1') { // 有开通权限
        this.hasOpenedAccount = true
      } else {
        this.allOpened = false
      }
    })
    if (this.allOpened) {
      this.$store.commit('updateNextBtnText', '返回首页')
    }
  },
  methods: {
    accSelEvent (item) {
      if (this.$parent.flowName === 'kcbkt' || this.$parent.flowName === 'cybkt') {
        // 判断选择账户的顺序 科创板/创业板开通只能先选择普通股东账户
        if (item.trdacctExcls === '2') {
          // 信用账户，判断选中的列表中有没有选中普通股东账户
          let flag = false
          for (let index = 0; index < this.selectedIndexes.length; index++) {
            const element = this.selectedIndexes[index]
            if (element.trdacctExcls === '0') {
              flag = true
              break
            }
          }
          if (!flag && !this.hasOpenedAccount) {
            let bName = this.$parent.flowName === 'kcbkt' ? '科创板权限' : '创业板权限'
            _hvueToast({
              mes:
                '开通信用账户' + bName + '，需要您先开通普通账户的' + bName
            })
            return
          }
        }
      }
      if (this.selectedIndexes.some(subItem => {
        return subItem.stockAccount === item.stockAccount
      })) {
        // 如果是点击取消普通股东账户的勾选（并且没有已开通的权限） 则也要将信用账户的选择取消
        if (item.trdacctExcls === '0' && !this.hasOpenedAccount) {
          for (let index = 0; index < this.selectedIndexes.length; index++) {
            const element = this.selectedIndexes[index]
            if (element.trdacctExcls === '2') {
              this.selectedIndexes.remove(element)
            }
          }
        }
        this.selectedIndexes.remove(this.selectedIndexes.filter(subItem => {
          return subItem.stockAccount === item.stockAccount
        })[0])
      } else {
        this.selectedIndexes.push(item)
      }
    },
    accountStyle (item) {
      let _class = ''
      if (item.openStatus === '1') {
        _class = ''
      } else if (item.openStatus === '0') {
        _class = 'icon_radio'
      } else {
        if (item.holderStatus !== '0') {
          _class = ''
        } else {
          if (item.regFlag !== '1' && item.regFlag !== '2') {
            _class = ''
          }
        }
      }
      if (this.selectedIndexes.some(subItem => {
        return subItem.stockAccount === item.stockAccount
      })) {
        _class += ' checked'
      }
      return _class
    },
    checkSubmit () {
      if (this.selectedIndexes.length === 0) {
        _hvueToast({
          mes: '请选择账户'
        })
        return false
      }
      this.submitAccountList = []
      for (let i = 0; i < this.selectedIndexes.length; i++) {
        const el = this.selectedIndexes[i]
        let account = {
          market: el.market,
          stockAccount: el.stockAccount,
          trdacctExcls: el.trdacctExcls
        }
        this.submitAccountList.push(account)
        if (el.trdacctExcls === '2') { // 已勾选信用账户
          $h.setSession('isOpenCredit', 1)
        }
      }
      return true
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.allOpened) {
          this.$router.push({name: 'index'})
          return
        }
        if (this.checkSubmit() === true) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        stockAccountInfo: JSON.stringify(this.submitAccountList)
      }
      return params
    },
    putFormData () {
      let formData = {
        chooseAccount: this.submitAccountList
      }
      return formData
    }
  }
}
</script>

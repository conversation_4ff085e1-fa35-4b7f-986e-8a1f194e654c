<!-- 协议列表组件1 常用在账户列表选择组件下 -->
<template>
  <div v-show="isShow" :class="{ bottom_check: isFixed, white_bg: isFixed }">
    <div class="rule_check">
      <span class="icon_check" :class="{ checked: isChecked }" @click.stop="confirmCheck"></span>
      <label>
        我已阅读并同意
        <a
          href="javascript:void(0)"
          @click.stop="toDetail(item.agreementId,item.agreeName)"
          v-for="(item,index) in pageParam"
          :key="index"
        >《{{item.agreeName}}》</a>
      </label>
    </div>
    <div v-show="isShowFundBtn" class="fund_botbtn">
      <div class="left">
        <span
          class="icon_radio"
          :class="{checked : allClickFlag}"
          @click.stop="chooseAllClick"
        >全选（{{selectedAccountsLength}}）</span>
      </div>
      <div class="right">
        <a class="ui button block" @click.stop="nextStep">立即开通</a>
      </div>
    </div>
  </div>
</template>
<script>
import { registerNativeBack } from '@/common/util'
export default {
  props: {
    pageParam: {
      type: Array
    }
  },
  data () {
    return {
      isShow: true,
      isChecked: false,
      agreementIds: []
    }
  },
  activated () {
    // window.androidAppBack = this.$parent.back
    registerNativeBack({
      callback: this.$parent.back
    })
    if (this.pageParam.length < 1) {
      _hvueAlert({ mes: '未查询到协议', title: '提示' })
    }
    // 注册vuebus方法 接受fundCompanyList组件的是否展示已开通账户状态
    this.$bus.on('showOpenAccountFlag', isShow => {
      this.isShow = !isShow
    })
  },
  computed: {
    // 是否全选，保存在vuex中
    allClickFlag () {
      return this.$store.state.allFundCheckClickFlag
    },
    // 下一步按钮是否固定在底部，保存在vuex中
    isFixed () {
      return this.$store.state.agreementIsFixed
    },
    // 已勾选账户总数，保存在vuex中
    selectedAccountsLength () {
      return this.$store.state.selectedAccountsLength
    },
    // 是否展示基金户的下一步按钮
    isShowFundBtn () {
      // 如果公用的下一步按钮隐藏了，则展示基金的下一步按钮
      let businessNextBtnShow = this.$store.state.businessNextBtnShow
      if (businessNextBtnShow) {
        return false
      } else {
        return true
      }
    }
  },
  methods: {
    toDetail (agreementId, agreementName) {
      // 查看协议详情
      this.$router.push({
        name: 'agreementDetail',
        query: { agreementId: agreementId, agreementTitle: agreementName, type: this.$route.query.type }
      })
    },
    checkSubmit () {
      if (!this.isChecked) {
        _hvueAlert({ title: '温馨提示', mes: '请先勾选同意按钮' })
        return false
      }
      this.agreementIds = []
      for (let index = 0; index < this.pageParam.length; index++) {
        const element = this.pageParam[index]
        this.agreementIds.push(element.agreementId)
      }
      return true
    },
    // 已阅同意勾选
    confirmCheck () {
      if (!this.isChecked) {
        const readAgreements = $h.getSession('agreementIds')
        if (readAgreements && readAgreements.split(',').length === this.pageParam.length) {
          this.isChecked = true
        } else {
          _hvueAlert({ title: '温馨提示', mes: '请点开协议完成阅读，再进行勾选' })
        }
        return false
      }
      this.isChecked = false
    },
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        } else {
          reject(new Error({ mes: '表单校验不通过' }))
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      // 业务请求参数封装
      let params = {
        agreementId: this.agreementIds.join(',')
      }
      return params
    },
    putFormData () {
      let formData = {
        signProtocol: { agreementId: this.agreementIds.join(',') }
      }
      return formData
    },
    // 选择全部点击事件
    chooseAllClick () {
      let allFlag = this.$store.state.allFundCheckClickFlag
      this.$store.commit('updateAllFundCheckClickFlag', !allFlag) // 是否全选，存在vuex中
      this.$bus.emit('changeAllFlag', !allFlag) // 通过vuebus调用账户组件的方法修改账户勾选状态
    },
    // 调用父组件的下一步事件
    nextStep () {
      this.$parent.emitNextEvent()
    }
  },
  destroyed () {
    this.$bus.off('showOpenAccountFlag') // 事件销毁，防止多次触发
    $h.clearSession('agreementIds')
  }
}
</script>
<style scoped>
.bottom_check {
  position: fixed;
  bottom: 0;
  border-top: 0.05rem solid #f9f9f9;
  z-index: 99999;
  background: white;
  width: 100%;
}
</style>

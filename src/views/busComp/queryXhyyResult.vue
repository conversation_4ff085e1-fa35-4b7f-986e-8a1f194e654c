/*
 * 选择账户 - 销户
 * @Date: 2020-08-20 19:35:17
 * @Last Modified by: Way
 * @Last Modified time: 2020-08-28 17:39:46
 */

<template>
  <div class="page_main">
        <headComponent headerTitle="结果页"></headComponent>
        <article class="content">
            <div class="result_main">
                <div
                    :class="{icon_ing:pageParam[0].state=='1',icon_ok:pageParam[0].state=='2',icon_error:pageParam[0].state=='3'}"
                ></div>
                <h5>{{showDealDesc}}</h5>
                <p class="center">{{showTips}}</p>
            </div>

            <dl v-if="pageParam[0].state=='1'" class="result_tips">
                <dt>
                    <span>提示</span>
                </dt>
                <dd>我们已收到您的销户请求，我司工作人员将于24小时内与您取得联系，为您办理后续务，请您留意电话或短信通知。如有疑问您可致电开户营业部：4000-188-688转5</dd>
            </dl>

            <dl v-else-if="pageParam[0].state=='3'" class="result_tips">
                <dt>
                    <span>提示</span>
                </dt>
                <dd>根据我司工作人员与您的沟通结果，您已同意取消销户申请，已为您取消申请，感谢您的支持！</dd>
            </dl>

            <div v-else class="xh_home_zbbox">
                <div style="text-align: center; margin-bottom: 0.2rem;">
                    <h5>温馨提示</h5>
                    <p>视频见证时，可能需要您准备的条件</p>
                </div>
                <ul class="xh_zblist">
                    <li><i class="icon"><img src="@/assets/images/xh_zbic01.png"></i><span>二代身份证</span></li>
                    <li><i class="icon"><img src="@/assets/images/xh_zbic02.png"></i><span>WIFI或4G</span></li>
                    <li><i class="icon"><img src="@/assets/images/xh_zbic03.png"></i><span>必须本人</span></li>
                </ul>
            </div>
        </article>
        <div class="bottom_btn">
            <div class="ce_btn">
                <a v-if="pageParam[0].state=='2'" class="ui button block rounded" @click.stop="toCloseAccount">开始销户</a>
                <a v-else-if="pageParam[0].state=='1'" class="ui button block rounded" @click.stop="back">确定</a>
                <a v-else class="ui button block rounded" @click.stop="yyxhEnd">确定</a>
            </div>
        </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { yyxhEnd } from '@/service/comServiceNew'

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  data () {
    return {
      ygtUserInfo: $h.getSession('ygtUserInfo', {decrypt: false}),
      clientId: '', // 客户号
      state: '1', // 办理状态
      showDealDesc: '',
      showTips: ''
    }
  },
  activated () {
    // 隐藏business.vue的下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    this.$store.commit('updateIsShowHead', false) // 隐藏head
  },
  mounted () {
    window.phoneBackBtnCallBack = this.back
    if (this.pageParam[0].state == '1') {
      this.showDealDesc = '销户预约成功，等待审核。'
      this.showTips = '您于' + this.pageParam[0].createDate + ' 预约成功'
    } else if (this.pageParam[0].state == '2') {
      this.showDealDesc = '预约通过，继续销户！'
      this.showTips = '您的申请于' + this.pageParam[0].createDate + ' 处理成功'
    } else if (this.pageParam[0].state == '3') {
      this.showDealDesc = '您已取消销户申请！'
      this.showTips = '您的申请于' + this.pageParam[0].createDate + ' 处理成功'
    }
  },
  methods: {
    yyxhEnd () {
      yyxhEnd({mobile: this.ygtUserInfo.mobile}, {}).then(
        res => {
          if (res.error_no === '0') {
            this.back()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          _hvueToast({ mes: err })
        }
      )
    },
    toCloseAccount () {
      this.$router.replace({
        name: 'business',
        query: {
          type: 'xh'
        }
      })
    },
    back () {
      this.$router.push({
        name: 'index'
      })
    }
  }
}
</script>

<style scoped>
.bottom_btn {
  position: fixed;
  bottom: 0;
  border-top: 0.05rem solid #f9f9f9;
  z-index: 99999;
  background: white;
  margin-bottom: 0.1rem;
  width: 100%;
}
</style>

<!-- 找回账户-身份验证 -->
<template>
  <div>
    <h5 class="com_title">请输入真实的身份信息</h5>
    <div class="input_form">
      <div
        class="ui field text"
        v-for="(item, index) in inputList"
        :key="index"
      >
        <label class="ui label">{{ item.text }}</label>
        <input
          type="text"
          class="ui input"
          :placeholder="item.placeholder"
          v-model="item.value"
        />
        <a
          class="txt_close"
          @mousedown.stop.prevent="item.value = ''"
          v-show="item.value !== ''"
        ></a>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['pageParam'],
  data() {
    return {
      handleResult: true,
      ygtUserInfo: {},
      inputList: [
        {
          name: 'name',
          text: '真实姓名',
          placeholder: '请输入您的真实姓名',
          value: '',
          errorInfo: '请输入您的真实姓名'
        },
        {
          name: 'idno',
          text: '身份证号',
          placeholder: '请输入您的身份证号码',
          value: '',
          errorInfo: '请输入您的身份证号码'
        }
      ]
    }
  },
  mounted() {
    this.$store.commit('updateBusinessNextBtnStatus', '提交验证') // 显示下一步按钮
  },
  methods: {
    /** ************子组件公共方法定义****** */
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        let errorInfo = ''
        this.inputList.forEach(item => {
          if (!errorInfo && !item.value) {
            errorInfo = item.errorInfo
          }
        })

        if (errorInfo) {
          _hvueToast({ mes: errorInfo })
          return false
        }
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      let params = {
        userName: '',
        identityNum: '',
        businessCode: this.$route.query.type,
        serivalId: this.$parent.publicParam.serivalId
      }
      this.inputList.forEach(item => {
        if (item.name === 'name') {
          params.userName = item.value
        }
        if (item.name === 'idno') {
          params.identityNum = item.value
        }
      })
      return params
    },
    putFormData() {
      let formData = {}
      return formData
    },
    // 提交后对结果进行处理的方法
    async handleResultFunc(res) {
      this.ygtUserInfo = res.userInfo[0]
      this.$parent.publicParam.userId = this.ygtUserInfo.userId
      $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })
      $h.setSession('isLogin', false, { encrypt: false })
      // 处理完成后，继续执行下一步
      this.$parent.$emit('init')
    }
    /** ************子组件公共方法定义****** */
  }
}
</script>

<!-- 姿势照上传组件 -->
<template>
  <div v-cloak>
    <!--头部 -->
    <headComponent :headerTitle="stepName"></headComponent>
    <div class="content">
      <div>
        <div class="photo_page">
          <h5 class="title">请按照以下姿势样例，拍摄个人免冠照片</h5>
          <div class="photo_cursor">
            <div class="cursor_right">
              <div class="pic"><img :src="gesturePhotoMap[gesturePhotoNum].examplePhoto" /></div>
              <p>正确姿势</p>
              <strong>{{ gesturePhotoMap[gesturePhotoNum].tips }}</strong>
            </div>
            <ul class="cursor_wrong">
              <li>
                <div class="item">
                  <div class="pic"><img :src="gesturePhotoMap[gesturePhotoNum].wrongExamplePhoto[0]" /></div>
                  <p>模糊不清</p>
                </div>
              </li>
              <li>
                <div class="item">
                  <div class="pic"><img :src="gesturePhotoMap[gesturePhotoNum].wrongExamplePhoto[1]" /></div>
                  <p>太暗</p>
                </div>
              </li>
              <li>
                <div class="item">
                  <div class="pic"><img :src="gesturePhotoMap[gesturePhotoNum].wrongExamplePhoto[2]" /></div>
                  <p>遮脸</p>
                </div>
              </li>
              <li>
                <div class="item">
                  <div class="pic"><img :src="gesturePhotoMap[gesturePhotoNum].wrongExamplePhoto[3]" /></div>
                  <p>手势不完整</p>
                </div>
              </li>
            </ul>
          </div>
          <div class="photo_opea">
            <a class="re_btn" href="javascript:;" v-show="gesturePhoto"
              @click.stop="rePhoto('imgUploader')">重拍</a>
            <div class="item">
              <input
                ref="imgUploader"
                v-show="platform === '0' && !gesturePhoto"
                type="file" accept="image/*" capture="camera"
                class="file_uploader"
                @change="getImgBrowser('imgUploader')"
              >
              <div class="pic"><img :src="gesturePhoto" /></div>
              <a class="btn" href="javascript:;" v-show="!gesturePhoto"
                @click.stop="typePop('show')">点击拍摄</a>
            </div>
          </div>
        </div>
        <div class="ce_btn" v-show="gesturePhoto">
          <a class="ui button block rounded" @click.stop="nextStep">下一步</a>
        </div>
      </div>

      <!-- 选择上传方式 -->
      <div v-show="showType" style="display:none;">
        <div class="ui dialog-overlay"></div>
        <div class="upload_btn">
          <ul>
            <li>
              <a @click.stop="getImgNative('pai')">拍摄</a>
            </li>
            <li>
              <a @click.stop="getImgNative('phone')">从相册选择上传</a>
            </li>
          </ul>
          <div class="cancel_btn">
            <a @click.stop="typePop('hide')">取消</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { uploadImg } from '@/service/comServiceNew'
import { function60002 } from 'h5CallNative'
import { getPlatformValue } from 'thinkive-hvue'
import { compressImg } from '@/common/util'

export default {
  components: {
    headComponent
  },
  props: ['pageParam'],
  name: 'uploadGesturePhoto',
  data () {
    return {
      serivalId: this.$parent.publicParam.serivalId,
      platform: '', // 操作渠道
      showType: false, // 是否展示上传方法选择框
      gesturePhotoMap: [{
        tips: '手指触碰嘴唇',
        examplePhoto: require('@/assets/images/gesture/woman1.jpg'),
        wrongExamplePhoto: [ require('@/assets/images/gesture/w14.png'), require('@/assets/images/gesture/w12.png'),
          require('@/assets/images/gesture/w13.png'), require('@/assets/images/gesture/w11.png') ],
        uploaded: false,
        uploadedPhoto: ''
      }, {
        tips: '手指触碰耳朵',
        examplePhoto: require('@/assets/images/gesture/woman2.png'),
        wrongExamplePhoto: [ require('@/assets/images/gesture/w24.png'), require('@/assets/images/gesture/w22.png'),
          require('@/assets/images/gesture/w23.png'), require('@/assets/images/gesture/w21.png') ],
        uploaded: false,
        uploadedPhoto: ''
      }, {
        tips: '握拳抵住下巴',
        examplePhoto: require('@/assets/images/gesture/woman3.png'),
        wrongExamplePhoto: [ require('@/assets/images/gesture/w34.png'), require('@/assets/images/gesture/w32.png'),
          require('@/assets/images/gesture/w33.png'), require('@/assets/images/gesture/w31.png') ],
        uploaded: false,
        uploadedPhoto: ''
      }],
      gesturePhotoNum: 0,
      gesturePhoto: '', // 姿势拍照照片
      backParam: []
    }
  },
  activated () {
    this.$store.commit('updateIsShowHead', false) // 隐藏头部
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
  },
  computed: {
    stepName() {
      return `姿势拍照(${this.gesturePhotoNum + 1}/3)`
    }
  },
  watch: {},
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.imgCallBack = this.getImgCallBack
    this.platform = getPlatformValue()
  },
  methods: {
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.checkSubmit()) {
          // 可以下一步
          resolve()
        }
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      return {}
    },
    putFormData () {
      let headImage = this.gesturePhotoMap.map(item => {
        if (item.uploaded) {
          return item.uploadInfo
        }
      })
      let formData = {
        posePhoto: headImage
      } // 需要保存的表单数据
      return formData
    },
    typePop (isShow) {
      this.showType = isShow === 'show'
    },
    // 重拍
    rePhoto(typeName) {
      if (this.platform !== '0') {
        this.typePop('show')
      } else {
        this.$refs[typeName].click()
      }
    },
    checkSubmit () {
      let gesturePhotosUploaded = true
      this.gesturePhotoMap.forEach(item => {
        if (!item.uploaded || !item.uploadInfo) {
          gesturePhotosUploaded = false
        }
      })
      if (!gesturePhotosUploaded) {
        _hvueToast({
          mes: '请按要求拍摄并上传个人免冠照'
        })
        this.gesturePhotoNum = 0
        return false
      }
      return true
    },
    // 调用原生上传图片
    getImgNative (actionType) {
      // 调用壳子的上传图片组件
      let param = {
        action: actionType, // phone：相册；pai:拍照界面
        imgType: '3', // 需上传图片类型 3大头照 4 身份证正面 5 反面
        url: SERVER_URL.YGT_NEW_SERVER
      }
      let result = function60002(param)
      this.showType = false
      if (result.error_no && result.error_no !== 0) {
        _hvueToast({ mes: result.error_info || '操作失败' })
      }
    },
    // 浏览器获取照片并转为base64
    getImgBrowser (typeName, type) {
      const inputDOM = this.$refs[typeName]
      let uploadedImg = inputDOM.files[0]
      const reader = new FileReader()
      const _this = this

      reader.onloadend = function (e) {
        let base64 = e.target.result
        console.log('original size: ', base64.length / 1024)
        if (base64.length > 512000) {
          let imgObj = new Image()
          imgObj.src = base64
          imgObj.onload = function () {
            base64 = compressImg(imgObj, 512000 / base64.length)
            _this.getImgCallBack({ base64 })
          }
          return
        }
        _this.getImgCallBack({ base64 })
      }
      if (uploadedImg) {
        console.log(uploadedImg.size)
        if (uploadedImg.size > 10485760) {
          _hvueToast({ mes: '上传的图片大小不得超过10MB' })
          return
        }
        reader.readAsDataURL(uploadedImg)
      }
    },
    // 原生拍照后回调h5的方法
    getImgCallBack (imgInfo) {
      if (imgInfo.ocrInfo) {
        // app端 已由原生上传图片
        this.updateShowPhoto(imgInfo.base64)
      } else {
        // h5本地上传base64到bus
        imgInfo.base64 = this.filterBase64Pre(imgInfo.base64)
        let param = {
          file_type: 'image',
          file_name: `${$h.getSession('ygtUserInfo', {decrypt: false}).userId}_${parseInt(Math.random() * 10e16)}.jpg`,
          isOcr: 0,
          mediaCode: '209',
          file_data: encodeURIComponent(imgInfo.base64),
          serivalId: this.serivalId,
          // businessCode: this.$route.query.type,
          businessCode: 'czmm',
          flow_current_step: 'uploadGesturePhoto'
        }
        uploadImg(param)
          .then(data => {
            if (data.error_no === '0') {
              this.gesturePhotoMap[this.gesturePhotoNum].uploadInfo = data.uploadInfo[0]
              this.updateShowPhoto(imgInfo.base64)
            } else {
              return Promise.reject(new Error(data.error_info))
            }
          })
          .catch(e => {
            this.updateShowPhoto(imgInfo.base64)
            _hvueToast({ mes: e.message })
          })
      }
    },
    filterBase64Pre (ndata) {
      let arr = ndata.split('base64,')
      return arr[arr.length - 1]
    },
    // 更新已拍照片
    updateShowPhoto (base64) {
      this.gesturePhoto = 'data:image/jpeg;base64,' + base64
      this.$set(this.gesturePhotoMap[this.gesturePhotoNum], 'uploadedPhoto', 'data:image/jpeg;base64,' + base64)
      this.$set(this.gesturePhotoMap[this.gesturePhotoNum], 'uploaded', true)
    },
    // 返回
    pageBack () {
      _hvueConfirm({
        mes: '姿势拍照未完成，是否返回上一步？',
        opts: [
          {
            txt: '取消',
            color: false
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 调用父类返回
              this.$parent.back()
            }
          }
        ]
      })
    },
    // 调用父组件的下一步事件
    nextStep () {
      let uploadPhoto = false
      this.gesturePhotoMap.forEach((item, index) => {
        if (!item.uploadedPhoto && !uploadPhoto) {
          uploadPhoto = index + ''
        }
      })
      if (!uploadPhoto) {
        this.$parent.emitNextEvent()
      } else {
        this.gesturePhotoNum++
        this.gesturePhoto = ''
      }
    }
  }
}
</script>
<style>
.hui-datetime-input {
  width: auto;
}
</style>

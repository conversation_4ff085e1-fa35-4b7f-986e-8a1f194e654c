<!-- 销户-销户原因 -->
<template>
  <div>
    <h5 class="com_title">您的销户原因（可多选）</h5>
    <div>
      <ul class="cancel_reasonlist">
        <li v-for="(item, index) in reasonList" :key="index">
          <p>{{ item.itemName }}</p>
          <span
            class="icon_check"
            :class="{ checked: item.selected }"
            @click.stop="$set(item, 'selected', !item.selected)"
          ></span>
        </li>
        <li>
          <p>其它原因</p>
          <span
            @click.stop="isOtherReasonSelected = !isOtherReasonSelected"
            class="icon_check"
            :class="{ checked: isOtherReasonSelected }"
          ></span>
          <div class="other_reason" v-show="isOtherReasonSelected">
            <multLineInput
              class="teare02 needsclick"
              v-model.trim="otherReason"
              placeholder="其他原因"
              :maxlength="50"
            ></multLineInput>
            <div class="num">{{ otherReason.length }}/50</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import multLineInput from '@/components/multLineInput'
import { setCaretPosition, registerNativeBack } from '@/common/util'

export default {
  components: {
    multLineInput
  },
  props: ['pageParam'],
  data() {
    return {
      handleResult: true,
      ygtUserInfo: {},
      reasonList: [],
      otherReason: '',
      otherReasonMaxlength: 50,
      isOtherReasonSelected: false
    }
  },
  activated() {
    console.log('closeReason activated 注册返回方法')
    registerNativeBack({
      callback: this.$parent.back
    })
  },
  mounted() {
    this.reasonList.push(...(this.pageParam || []))
    this.$store.commit('updateBusinessNextBtnStatus', '提交销户') // 显示下一步按钮
  },
  methods: {
    // 其他原因输入
    otherReasonInput(e) {
      const inputText = e.target.innerText

      this.otherReason = inputText.substring(0, 50)
      if (inputText.length > this.otherReasonMaxlength) {
        this.$refs.otherReasonInput.innerText = this.otherReason
      }
      // 设置光标位置
      setCaretPosition(
        this.$refs.otherReasonInput,
        inputText.length > this.otherReasonMaxlength
          ? this.otherReasonMaxlength
          : inputText.length
      )
    },
    /** ************子组件公共方法定义****** */
    doCheck() {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        if (this.isOtherReasonSelected && this.otherReason.length == 0) {
          _hvueToast({ mes: '请填写其他原因' })
          reject(new Error({ mes: '请填写其他原因' }))
          return
        }
        if (
          this.reasonList.some(item => {
            return item.selected
          }) ||
          (this.isOtherReasonSelected && this.otherReason)
        ) {
          resolve()
          return
        }

        _hvueToast({ mes: '请选择或填写销户原因' })
        reject(new Error({ mes: '请填写其他原因' }))
        return false
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage() {
      // 业务请求参数封装
      const reasonSelected = this.reasonList
        .filter(item => {
          return item.selected
        })
        .map(item => {
          return item.itemName
        })
      const reason =
        reasonSelected.join(';') +
        `${reasonSelected.length > 0 ? ';' : ''}${this.otherReason || ''}`
      let params = {
        reason: reason
      }
      return params
    },
    putFormData() {
      const reasonSelected = this.reasonList
        .filter(item => {
          return item.selected
        })
        .map(item => {
          return item.itemName
        })
      const reason =
        reasonSelected.join(';') +
        `${reasonSelected.length > 0 ? ';' : ''}${this.otherReason || ''}`
      let formData = {
        reason: reason
      }
      return formData
    },
    // // 提交后对结果进行处理的方法
    async handleResultFunc(res) {
      // 处理完成后，继续执行下一步
      this.$parent.$emit('init')
    }
    /** ************子组件公共方法定义****** */
  }
}
</script>

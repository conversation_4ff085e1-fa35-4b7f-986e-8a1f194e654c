<template>
  <div>
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" href="javascript:;" @click.stop="pageBack"></a>
        <h1 class="title text-center">{{ title }}</h1>
      </div>
    </header>
    <article class="content" v-show="!isDwLogin && isTransactionTime">
      <div class="logo_page">
        <!-- 东吴销户业务以后再也没了，先写死为销户，统一登录后也不会有此页面  -->
        <h2 class="title">东吴证券手机销户！</h2>
        <div class="login_form">
          <div
            class="input_text"
            v-for="(item, index) in inputModelListFilter"
            :key="index"
            :class="{ code: item.isVCode }"
          >
            <span class="tit">{{ item.cnName }}</span>
            <input
              v-model.trim="item.value"
              :type="item.type"
              class="t1"
              :placeholder="item.defaultText"
              :maxlength="item.maxlength"
              @blur="verifyYes(item)"
              @input="compeletenessCheck()"
            />
            <a
              class="icon_eye"
              :class="pwdShow ? 'show' : ''"
              @mousedown.stop.prevent="eyeClick(item)"
              v-show="item.name == 'password' && item.value !== ''"
            ></a>
            <a
              v-if="item.isVCode"
              class="code_img"
              href="javascript:;"
              @click.stop="getImgCode"
            >
              <img :src="codeImgUrl" />
            </a>
            <smsTimer
              v-if="item.isSCode && !isDowntime"
              v-model="startFlag"
            ></smsTimer>
            <smsTimer
              v-if="item.isSCode && isDowntime"
              v-model="startFlag"
              @sendSms="verifyImgCode"
            ></smsTimer>
          </div>
        </div>
        <div class="ce_btn">
          <a
            class="p_button"
            :class="{ disabled: !verifyPass, login_success: loginSuccess }"
            @click.stop="login"
            href="javascript:;"
            >{{ btnText }}</a
          >
        </div>
      </div>
    </article>
    <article class="content" v-show="!isTransactionTime">
      <div class="notice_box">
        <div class="pic">
          <img src="@/assets/images/not_ic05.png" />
        </div>
        <h5 v-html="message"></h5>
      </div>
      <div class="ce_btn mt20">
        <a v-throttle class="p_button" @click="pageBack" >返回首页</a>
      </div>
    </article>
  </div>
</template>

<script>
import {
  userLogin,
  ssoLogin,
  getImgCode,
  verifyImgCode,
  sendMobileMsg,
  queryTransactionTime
} from '@/service/comServiceNew'
import { closeYgt } from '@/common/sso'
import { ecbEncrypt } from 'thinkive-hvue/plugin/sm/sm4'
import smsTimer from '@/components/smsTimer'
import { queryDictionary, ajax, goBackXcApp, randomNum } from '@/common/util'
import('@/plugins/gt.js')

export default {
  components: {
    smsTimer
  },
  data() {
    return {
      startFlag: false, // 是否开始倒计时
      btnText: '登录',
      isDowntime: false,
      title: '登录',
      isTransactionTime: true,
      message:
        '当前为非中登时间，请在中登时间办理<br><span class="imp">交易日  09:00 ~ 16:00</span>',
      inputModelList: [
        {
          cnName: '客户号',
          name: 'account',
          value: '',
          icon: 'ic01',
          type: 'tel',
          defaultText: '请输入客户号',
          maxlength: 12
        },
        {
          cnName: '密码',
          name: 'password',
          value: '',
          icon: 'ic02',
          type: 'password',
          defaultText: '请输入交易密码',
          maxlength: 6
        },
        {
          cnName: '手机号',
          name: 'tel',
          value: '',
          icon: 'ic01',
          type: 'tel',
          defaultText: '请输入手机号',
          maxlength: 11
        },
        {
          cnName: '图形验证码',
          name: 'vcode',
          value: '',
          icon: 'ic03',
          type: 'text',
          defaultText: '请输入验证码',
          maxlength: 4,
          isVCode: true
        },
        {
          cnName: '验证码',
          name: 'scode',
          value: '',
          icon: 'ic03',
          type: 'tel',
          defaultText: '请输入验证码',
          maxlength: 6,
          isSCode: true
        }
      ],
      loginSuccess: false, // 登录成功
      verifyPass: false, // 输入校验通过
      rememberAccount: false, // 记住密码
      codeImgUrl: '', // 图形验证码url
      codeImgKey: '',
      pwdShow: false,
      smsNo: '', // 短信类型数据字典
      sendSmsFlag: '', // 是否已经发送了验证码
      startFlag: false, // 是否开始倒计时
      businessCode: 'xh',
      isDwLogin: true
    }
  },
  computed: {
    pwdType() {
      // 密码明文显示开关
      return this.pwdShow ? 'tel' : 'password'
    },
    inputModelListFilter() {
      if (!this.isDowntime) {
        return this.inputModelList.filter(item => {
          return !item.isVCode
        })
      } else {
        return this.inputModelList
      }
    }
  },
  mounted() {
    queryTransactionTime({}, {}).then(
      res => {
        if (res.error_no === '0') {
          let transactionTime = res.results[0].transactionTime
          if (transactionTime != 'true') {
            // _hvueAlert({
            //   title: '提示',
            //   mes:
            //     '当前非业务办理时间，无法办理。办理时间为：交易日 9：00~16：00',
            //   callback: () => {
            //     this.pageBack()
            //   }
            // })
            this.isTransactionTime = false
            return
          } else {
            this.isTransactionTime = true
            this.getImgCode() // 获取图形验证码
            if ($hvue.env == 'ths') {
              // invokeNative('8011', '000', {}, data => {
              // console.log('8011首页回调结果', data)
              // data = JSON.parse(data)
              // })
            }
            let ztToken = $h.getSession('ztToken')
            // ztToken = false //目前统一登录不上线，就先设置没有统一登录的token
            if (
              ztToken &&
              ztToken != undefined &&
              ztToken != 'undefined' &&
              ztToken != null
            ) {
              this.isDwLogin = true
              ssoLogin({
                user_token: ztToken
              }).then(
                res => {
                  console.log(res)
                  if (res.error_no === '0') {
                    this.loginSuccess = true
                    this.btnText = '登录成功'
                    // 保存用户信息
                    let userInfo = res.userInfo[0]
                    userInfo.riskLevelDesc =
                      res.custTypeCheck !== undefined
                        ? res.custTypeCheck[0].riskLevelDesc
                        : ''
                    const ssoToken =
                      res.userToken && res.userToken[0]
                        ? res.userToken[0].userToken
                        : ''
                    const ssoInfo = {
                      ssoToken,
                      source: '',
                      loginFlag: 2,
                      mobilecode: 'NA'
                    }
                    const dw_token =
                      res.userToken && res.userToken[0]
                        ? res.userToken[0].dw_token
                        : ''
                    $h.setSession('dw_token', dw_token, { decrypt: false })
                    $h.setSession('ygtUserInfo', userInfo, { encrypt: false })
                    $h.setSession('isLogin', true, { decrypt: false })
                    $h.setSession('isLoginSD', false, { decrypt: false })
                    if ($hvue.env == 'ths') {
                      this.$router.push({
                        name: 'business',
                        query: {
                          type: 'xh'
                        }
                      })
                    } else {
                      $this.$router.push({
                        name: 'index'
                      })
                    }
                  } else {
                    if(res.error_info &&res.error_info!='null'&&res.error_info!='undefined'){
                        _hvueAlert({
                        title: '提示',
                        mes: res.error_info,
                        callback: () => {
                          this.sdLoginViews()
                        }
                      })
                    }else{
                      this.sdLoginViews()
                    }

                  }
                },
                err => {
                  console.log(err)
                }
              )
            } else {
              this.sdLoginViews()
            }
          }
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info,
            callback: () => {
              this.pageBack()
            }
          })
        }
      },
      err => {
        console.log(err)
      }
    )
  },
  methods: {
    sdLoginViews() {
      let that = this
      that.isDwLogin = false
      window.androidAppBack = $hvue.env == 'ths' ? goBackXcApp : that.goIndex
      console.log(that.$route.query.type)
      queryDictionary(
        { type: 'ismp.sms_type', key: 'xh_sms_login_check' },
        data => {
          that.smsNo = data.key
        }
      )
      that.getSmsGeetest()
    },
    getSmsGeetest() {
      let that = this
      var g_user_id = ''
      for (let i = 0; i < 6; i++) {
        g_user_id += Math.floor(Math.random() * 10)
      }
      console.log(g_user_id)
      ajax({
        url:
          SERVER_URL.YGT_NEW_SERVER_SERVLET_API +
          '/register?t=' +
          new Date().getTime(), // 加随机数防止缓存
        type: 'get', // type ---> 请求方式
        async: true, // async----> 同步：false，异步：true
        data: {
          //传入信息
          userId: g_user_id,
          client_type: '',
          ip: '127.0.0.1',
          gt_flag: '1',
          gt_client_type: 'H5',
          gt_user_id: g_user_id,
          g_user_id: g_user_id
        },
        success: function(data) {
          //返回接受信息
          console.log(data)
          let result = JSON.parse(data)
          //宕机情况,还是放行让过
          if (result.success == '0') {
            // _hvueToast({
            //   icon: 'error',
            //   mes: '机制校验宕机当前走图片验证码模式'
            // })
            that.isDowntime = true
            return
          }

          initGeetest(
            {
              product: 'bind',
              width: '300px',
              https: true,
              api_server: 'apiv6.geetest.com',
              gt: result.gt,
              challenge: result.challenge,
              offline: !result.success,
              new_captcha: result.new_captcha,
              onError: () => {
                console.log('onError')
                that.isDowntime = true
              }
            },
            function(captchaObj) {
              // captchaObj.appendTo(captchaBox)

              captchaObj
                .onReady(function() {
                  console.log('准备')
                })
                .onSuccess(function() {
                  console.log('成功')
                  var validateRes = captchaObj.getValidate()
                  ajax({
                    url:
                      SERVER_URL.YGT_NEW_SERVER_SERVLET_API +
                      '/validate?t=' +
                      new Date().getTime(), // 加随机数防止缓存
                    type: 'POST', // type ---> 请求方式
                    async: true, // async----> 同步：false，异步：true
                    data: {
                      //传入信息
                      geetest_challenge: encodeURIComponent(
                        validateRes.geetest_challenge
                      ),
                      geetest_seccode: encodeURIComponent(
                        validateRes.geetest_seccode
                      ),
                      geetest_validate: encodeURIComponent(
                        validateRes.geetest_validate
                      ),
                      challenge: encodeURIComponent(
                        validateRes.geetest_challenge
                      ),
                      seccode: encodeURIComponent(validateRes.geetest_seccode),
                      validate: encodeURIComponent(validateRes.geetest_validate)
                    },
                    success: function(data) {
                      //返回接受信息
                      console.log(data)
                      let result = JSON.parse(data)
                      if (result.success == '0') {
                        _hvueToast({
                          icon: 'error',
                          mes: result.msg
                        })
                        captchaObj.reset()
                      } else {
                        var params = {
                          ip: $h.getSession('LIP'),
                          smsNo: that.smsNo,
                          // userId: g_user_id,
                          mobile: that.inputModelList[2].value
                        }
                        sendMobileMsg(params).then(res => {
                          if (res.error_no === '0') {
                            // 短信验证码发送成功，开始倒计时
                            // console.log(this)
                            that.startFlag = true
                            that.sendSmsFlag = true
                          } else {
                            _hvueAlert({
                              title: '提示',
                              mes: res.error_info,
                              callback: () => {}
                            })
                          }
                        })
                      }
                    }
                  })
                })
                .onError(function() {
                  console.log('错误')
                  that.isDowntime = true
                })
              if (that.startFlag) return
              captchaObj.verify() //显示验证码
              let smsButton = document.getElementById('smsButton')
              // 按钮提交事件
              smsButton.onclick = function() {
                // some code
                // 检测验证码是否ready, 验证码的onReady是否执行
                if (that.startFlag) return
                let telPhone = that.inputModelList[2].value
                if (telPhone.trim() === '') {
                  _hvueToast({
                    icon: 'error',
                    mes: '手机号不能为空'
                  })
                  return false
                } else if (!/^1\d{10}$/.test(telPhone)) {
                  _hvueToast({
                    icon: 'error',
                    mes: '请输入正确格式的手机号'
                  })
                  return false
                }
                captchaObj.verify() //显示验证码
                // some code
              }
              that.captchaObj = captchaObj
            }
          )
        }
      })
    },
    // // 获取短信验证码
    // getVerifyCode () {
    //   if (!this.inputModelList[2].value) {
    //     _hvueToast({
    //       icon: 'error',
    //       mes: '手机号不能为空'
    //     })
    //     return false
    //   }
    //   // this.getSmsGeetest()

    // },
    eyeClick(item) {
      item.type = item.type == 'password' ? 'tel' : 'password'
      this.pwdShow = !this.pwdShow
      this.$set(this.inputModelList[1], index, item)
    },
    // 获取图形验证码
    getImgVerificationCode() {
      let mobileKey = Math.random()
      this.codeImgUrl = `${$hvue.config.imgUrl}/nImgServlet?mobileKey=${mobileKey}&r=${mobileKey}`
    },
    getImgCode() {
      getImgCode({}, {}).then(
        res => {
          if (res.error_no === '0') {
            let results = res.results
              ? res.results[0]
              : res.generateVerifyCode[0]
            this.codeImgUrl = results.imageCode
            this.codeImgKey = results.mobileKey
            this.inputModelList.forEach(item => {
              if (item.name === 'vcode') {
                item.value = ''
              }
            })
            this.compeletenessCheck()
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    clearInput(item) {
      item.value = ''
      this.compeletenessCheck()
    },
    // 输入检查
    verifyYes(inputItem, noToast) {
      let errTxt = '' // 错误信息
      if (inputItem.name === 'password') {
        if (inputItem.value.trim() === '') {
          errTxt = '请输入六位数字密码'
        } else if (!/^\d{6}$/.test(inputItem.value)) {
          errTxt = '请输入六位数字密码'
        } else {
          errTxt = 'pass'
        }
      } else if (inputItem.name === 'account') {
        if (inputItem.value.trim() === '') {
          errTxt = '请输入资金账号'
        } else if (!/^\d{5,15}$/.test(inputItem.value)) {
          errTxt = '请输入正确格式的资金账号'
        } else {
          errTxt = 'pass'
        }
      } else if (inputItem.name === 'tel') {
        if (inputItem.value.trim() === '') {
          errTxt = '请输入手机号'
        } else if (!/^1\d{10}$/.test(inputItem.value)) {
          errTxt = '请输入正确格式的手机号'
        } else {
          errTxt = 'pass'
        }
      } else if (inputItem.name === 'scode') {
        if (inputItem.value.trim() === '') {
          errTxt = '请输入验证码'
        } else if (!/^\d{6}$/.test(inputItem.value)) {
          errTxt = '请输入正确验证码'
        } else {
          errTxt = 'pass'
        }
      } else {
        // if (inputItem.value.trim() === '') {
        //   errTxt = inputItem.defaultText
        // } else if (inputItem.value.length < inputItem.maxlength) {
        //   errTxt =
        //     '请输入正确格式的' + inputItem.defaultText.replace('请输入', '')
        // } else {
        //   errTxt = 'pass'
        // }
        errTxt = 'pass'
      }

      if (errTxt !== 'pass') {
        if (!noToast) {
          _hvueToast({
            timeout: 1500,
            mes: errTxt
          })
        }
        return false
      }
      return true
    },

    // 输入完整性检查
    compeletenessCheck() {
      this.verifyPass = true
      this.inputModelList.forEach(item => {
        this.verifyPass = this.verifyPass && this.verifyYes(item, true)
      })
    },

    // 校验图片验证码
    verifyImgCode() {
      let that = this
      let imgCode = ''
      this.inputModelList.forEach(item => {
        if (item.name === 'vcode') {
          imgCode = item.value
        }
      })
      verifyImgCode({ mobileKey: this.codeImgKey, imageCode: imgCode }).then(
        res => {
          if (res.error_no === '0') {
            var params = {
              ip: $h.getSession('LIP'),
              smsNo: that.smsNo,
              // userId: g_user_id,
              mobile: that.inputModelList[2].value
            }
            sendMobileMsg(params).then(res => {
              if (res.error_no === '0') {
                // 短信验证码发送成功，开始倒计时
                // console.log(this)
                that.startFlag = true
                that.sendSmsFlag = true
              } else {
                _hvueAlert({
                  title: '提示',
                  mes: res.error_info,
                  callback: () => {}
                })
              }
            })
            // this.startFlag = true
            // this.sendSmsFlag = true
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
            this.getImgCode()
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    // 请求登录
    login() {
      let imgCode = ''
      this.inputModelList.forEach(item => {
        if (item.name === 'vcode') {
          imgCode = item.value
        }
      })
      if(this.isDowntime && imgCode === '') {
        _hvueToast({ mes: '请先填写图形验证码' })
        return false
      }
      if (!this.verifyPass) {
        return
      }
      if (!this.sendSmsFlag) {
        _hvueToast({ mes: '请先发送验证码' })
        return false
      }
      let account, password, tel, smsNo
      this.inputModelList.forEach(item => {
        if (item.name === 'account') {
          account = item.value
        }
        if (item.name === 'password') {
          password = item.value
        }
        if (item.name === 'tel') {
          tel = item.value
        }
        if (item.name === 'scode') {
          smsNo = item.value
        }
      })
      const sm4Key = $hvue.config.sm4Key
      const newPassword = ecbEncrypt(
        sm4Key.substring(8, sm4Key.length - 8),
        password
      )

      // 登录 1005300
      ssoLogin({
        account: account,
        password: encodeURIComponent(newPassword),
        mobile: tel,
        smsNo: this.smsNo,
        verifyCode: smsNo
      }).then(
        res => {
          console.log(res)
          if (res.error_no === '0') {
            this.loginSuccess = true
            this.btnText = '登录成功'
            // 保存用户信息
            let userInfo = res.userInfo[0]
            userInfo.riskLevelDesc =
              res.custTypeCheck !== undefined
                ? res.custTypeCheck[0].riskLevelDesc
                : ''
            const ssoToken =
              res.userToken && res.userToken[0]
                ? res.userToken[0].userToken
                : ''
            const ssoInfo = {
              ssoToken,
              source: '',
              loginFlag: 2,
              mobilecode: 'NA'
            }
            const dw_token =
              res.userToken && res.userToken[0] ? res.userToken[0].dw_token : ''
            $h.setSession('dw_token', dw_token, { decrypt: false })
            $h.setSession('isLoginSD', true, { decrypt: false })
            $h.setSession('ygtUserInfo', userInfo, { encrypt: false })
            $h.setSession('isLogin', true, { decrypt: false })

            // 保存用户信息
            // let userInfo = res.userInfo[0]
            // userInfo.riskLevelDesc =
            //   res.custTypeCheck !== undefined
            //     ? res.custTypeCheck[0].riskLevelDesc
            //     : ''
            // const ssoToken =
            //   res.userToken && res.userToken[0]
            //     ? res.userToken[0].userToken
            //     : ''
            // const ssoInfo = {
            //   ssoToken,
            //   source: '',
            //   loginFlag: 2,
            //   mobilecode: 'NA'
            // }
            // $h.setSession('ygtUserInfo', userInfo, { encrypt: false })
            // $h.setSession('isLogin', true, { decrypt: false })

            // $h.setSession('ssoInfo', ssoInfo, { encrypt: true })

            // this.rememberAccount
            //   ? $h.setLocal('historyAccount', account, { encrypt: true })
            //   : $h.clearLocal('historyAccount')
            // this.$router.go(-1)
            if ($hvue.env == 'ths') {
              this.$router.push({
                name: 'business',
                query: {
                  type: 'xh'
                }
              })
            } else {
              // this.$router.go(-1)
              $this.$router.push({
                name: 'index'
              })
            }
          } else {
            this.getImgCode()
            _hvueAlert({
              title: '提示',
              mes: res.error_info
            })
          }
        },
        err => {
          this.getImgCode()
          console.log(err)
        }
      )
    },
    // 返回上一页
    pageBack() {
      console.log('throttle')
      $h.clearSession('toPage')
      $hvue.env == 'ths' ? goBackXcApp() : this.$router.push({ name: 'index' })
    },
    // 退出登录
    logout() {
      // h5访问时退出登录
      if ($hvue.platform === '0') {
        $h.clearSession('ygtUserInfo')
        $h.clearSession('isLogin')
        $h.clearSession('ssoInfo')
        $h.clearSession('passwordType')
      } else {
        closeYgt(0, '1A', 0, 1) // 退出网厅
      }
    },
    // 忘记密码
    fogetPassword() {
      let allBusinessArr = $h.getSession('allBusinessArr') || []
      let toPage
      if (allBusinessArr.length > 0) {
        allBusinessArr.forEach(item => {
          item.businessMenu.forEach(item => {
            if (item.businessCode === 'czmm') {
              toPage = item
            }
          })
        })
        $h.setSession('toPage', JSON.stringify(toPage), { encrypt: false })
      }
      this.$router.go(-1)
    },
    // 找回账号
    findAccount() {
      let allBusinessArr = $h.getSession('allBusinessArr') || []
      let toPage
      if (allBusinessArr.length > 0) {
        allBusinessArr.forEach(item => {
          item.businessMenu.forEach(item => {
            if (item.businessCode === 'zhzh') {
              toPage = item
            }
          })
        })
        $h.setSession('toPage', JSON.stringify(toPage), { encrypt: false })
      }
      this.$router.go(-1)
    },
    goIndex() {
      this.$router.push({ name: 'index' })
    }
  },
  destroyed() {
    this.startFlag = false
  }
}
</script>

<style scoped>
.login_form .ui.field > .ui.input {
  padding-right: 0.4rem;
}
</style>

<!-- 业务入口页面，每个业务都跳转此页面，根据每一步返回的结果集名称加载对应的原子组件
    必须携带的参数：type:业务名称 例如：fxcp business.vue?type=fxcp  路由跳转使用query传入参数
    作用：
            1、查询表单状态
            2、初始化Flow对象。执行Flow对象的构造方法，初始化流程数据。
            初始化后，flow对象属性：userId flowId flowName数据已初始化完成。

            3、加载流程loadFlow
               加载完成后，当前业务流程步骤信息、业务流水编号serivalId数据已初始化
            4、执行步骤的拦截器事件
            5、加载当前步骤所需的原子组件
 -->
<template>
  <div class="page_main" v-cloak>
    <!--公用业务标题
        若不显示，调用 this.$store.commit("updateIsShowHead", false); 隐藏
    -->
    <headComponent :headerTitle="stepName" v-show="isShowHead"></headComponent>
    <article
      class="content"
      :class="{ flex_vertical: isComponentsFlexVertical }"
    >
      <!--根据当前init数据结果集名称加载对应的页面流子组件-->
      <keep-alive v-for="(atom, atomIndex) in componentList" :key="atomIndex">
        <component
          :is="atom.path"
          :ref="atom.name"
          :pageParam="initData[atomIndex]"
          @hook:activated="childActivated(atomIndex)"
        ></component>
      </keep-alive>
      <!--
        下一步按钮：
             1、业务公用的下一步按钮
             2、若不需要，在子组件的created钩子函数中,执行：this.$store.commit("updateBusinessNextBtnStatus", false); // 隐藏下一步按钮
      -->
      <!-- <div class="ce_btn mt20" v-show="parentBtnShow">
        //点击此按钮 触发所有子组件的putFormData,doSubmit事件
        <a
          class="ui button block rounded"
          :class="{ disabled: nextBtnDisabled, border: nextBtnCss }"
          @click.stop.prevent="emitNextEvent"
          >{{ nextBtnText }}</a
        >
      </div> -->
    </article>
    <footer class="footer" v-show="parentBtnShow">
      <div class="ce_btn">
        <a
          v-throttle
          class="p_button"
          :class="{
            disabled: nextBtnDisabled || isSubmitting,
            border: nextBtnCss
          }"
          @click.stop.prevent="emitNextEvent"
          >{{ nextBtnText }}</a
        >
      </div>
    </footer>
  </div>
</template>
<script>
import Flow from '@/flow/flowNew'
import { queryUserFlowStatus, closeFlow } from '@/service/comServiceNew'
import { closeYgt } from '@/common/sso'
import { queryBusiness, goBackXcApp, registerNativeBack } from '@/common/util'
import headComponent from '@/components/headComponent' // 头部

export default {
  inject: ['reload'],
  components: { headComponent },
  name: 'business',
  data() {
    return {
      isLogin: $h.getSession('isLogin', { decrypt: false }) || false,
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }) || {},
      currentPage: 'loading', // 默认加载loading,等待流程初始化完成
      flowName: this.$route.query.type, // 业务类型
      flow: null, // 流程对象
      isBreakPoint: '0', // 根据业务配置是否有断点来判断是否结束流程isBreakPoint是否支持断点   1:支持   0：不支持
      businessName: '', // 业务名称

      stepName: '', // 步骤名称 -- 头部标题 取的pageFlow每一步配置的描述description字段
      stepsInfo: [], // 业务所有流程的步骤信息
      initData: [], // 组件初始化数据
      formData: {}, // 需要提交的表单数据
      componentNames: '', // 当前组件名称数组
      preSubmitResult: '', // 上一步的提交结果 用于下一步的页面需要展示上一步提交的结果时用
      activatedChild: [], // 已加载的子组件
      isAllChildrenActivated: false, // 当前步骤子组件是否都已加载
      // nextBtnDisable: false, // 下一步按钮是否可点击  默认不可点击 初始化成功后再允许点击

      // !!!公共入参在此处定义，禁止子组件修改
      publicParam: {
        userId: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).userId
          : '',
        clientId: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).clientId
          : '',
        fundAccount: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).fundAccount
          : '',
        name: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).name
          : '',
        branchNo: $h.getSession('ygtUserInfo', { decrypt: false })
          ? $h.getSession('ygtUserInfo', { decrypt: false }).branchNo
          : '',
        businessCode: this.$route.query.type, // 业务类型
        serivalId: '' // 流程初始化后赋值
      },
      historyStepInfo: [], // 历史pageFlow步骤记录，用于返回到指定的步骤
      historyComponentInfo: [], // 保存组件历史数据
      noFlowBusiness: ['czmm'],
      returnIgnoreStep: [],
      returnIgnoreComponents: [
        // 返回时要跳过的步骤包含的组件
        'popInformationMessage',
        'question'
      ],
      keywords: [],
      isSubmitting: false
    }
  },
  computed: {
    // 父组件的下一步按钮是否显示，保存在vuex中
    parentBtnShow() {
      return this.$store.state.businessNextBtnShow
    },
    // 父组件的下一步按钮的文字描述值，保存在vuex中
    nextBtnText() {
      return this.$store.state.nextBtnText
    },
    nextBtnDisabled() {
      return this.$store.state.nextBtnDisabled
    },
    nextBtnCss() {
      return this.$store.state.nextBtnCss
    },
    isShowHead() {
      return this.$store.state.isShowHead
    },
    flowParamDelay() {
      return this.$store.state.flowParamDelay
    },
    isComponentsFlexVertical() {
      return this.$store.state.isComponentsFlexVertical
    }
  },
  watch: {
    // 检测路由变化，当前业务类型发生改变时，强制刷新当前页面
    $route: {
      handler(route, oldVal) {
        if (route.query.type !== this.flowName && route.name === 'business') {
          this.flowName = route.query.type
          this.$store.commit('updateCachePath', [])
          this.reload() // 强制刷新当前页面 provide 与inject配合实现
        }
      }
    }
  },
  created() {
    queryBusiness(
      this.$route.query.type === 'yyxh' ? 'xh' : this.$route.query.type,
      data => {
        this.isBreakPoint = data.isBreakPoint
        this.businessName = data.businessName
      }
    )
    // 监控下一步的初始化事件 -- 原子组件调用完下一步后，必须提交此方法初始化下一步。
    this.$on('init', params => {
      // this.$store.commit('updateIsShowHead', true) // 初始化展示头部 ，需要隐藏时在子组件处理
      let stepInfo = this.flow.currentStepInfo
      let currentView = stepInfo.flow_current_step_view // 当前需要展示的组件
      let currentStepName = stepInfo.flow_current_step_name // 当前步骤名称
      let isFinished = stepInfo.flow_finish // 流程是否已经结束
      let isShow = stepInfo.flow_current_step_show // 当前步骤是否显示
      let isAutoSubmit = stepInfo.flow_current_step_auto_submit // 是否自动提交

      // 如果需要展示，则需要初始化
      if (isShow === '1') {
        // 记录步骤信息到历史步骤中。
        this.historyStepInfo.push(currentStepName)
        this.historyStepInfo = Array.from(new Set(this.historyStepInfo)) // 去重
        this.init(params)
        if (isAutoSubmit === '1') {
          this.next()
        }
      } else {
        // 不需要展示，直接提交事件。
        let initParam = this.$route.params || {}
        this.next(initParam)
          .then(res => {
            let stepInfo = this.flow.currentStepInfo
            let isFinished = stepInfo.flow_finish // 流程是否已经结束
            let isShow = stepInfo.flow_current_step_show // 当前步骤是否显示
            // 流程已经结束，不需要展示结果页面，若不需要则直接弹框提示
            // 不显示，直接弹框提示
            const asyncBusiness = ['zlxg'] // 非实时办理业务
            if (isFinished === '1' && isShow === '0') {
              const message = asyncBusiness.includes(this.flowName)
                ? '提交成功'
                : '办理成功'
              _hvueToast({
                mes: message,
                icon: 'success',
                timeout: 1500,
                callback: () => {
                  this.businessFinish()
                }
              })
              return
            }
            this.$emit('init', params)
          })
          .catch(error => {
            const returnToMainPageErrorNo = ['-8800018']
            console.log(error)
            // 初始化报错，隐藏下一步按钮 不能让用户点击 重新加载流程
            _hvueAlert({
              title: '错误提示',
              mes: error.error_info,
              callback: () => {
                // 初始化报错，流程无法继续往下走 初始化报错的场景不应该发生 一般都是查询数据，接口处理。此处直接返回首页
                // 直接后退
                if (returnToMainPageErrorNo.includes(error.error_no)) {
                  this.$router.go(-1)
                }
              }
            })
          })
      }
    })
    // 监控表单数据更新
    this.$on('putFormData', params => {
      this.putFormData(params)
    })
  },
  mounted() {
    // let opStation = $h.getSession('opStation')
    // if(!opStation){
    //   invokeNative('8011', '000', {}, data => {
    //       console.log('8011首页回调结果', data)
    //     })
    // }
    // window.phoneBackBtnCallBack = this.back

    this.keywords = []

    if (this.$route.params.flowInfo) {
      this.publicParam.serivalId = this.$route.params.flowInfo.serivalId
      this.afterInitFlow({
        userId: this.ygtUserInfo.userId || '',
        clientId: this.ygtUserInfo.clientId || '',
        flowName: this.flowName, // 业务名称/流程名称
        _this: this,
        formStatus: '',
        flowId: this.$route.params.flowInfo.flowId,
        serivalId: this.$route.params.flowInfo.serivalId // 业务流水ID
      })
      return
    }
    this.initFlow() // 初始化业务流程对象
    // window.androidAppBack = this.back
  },
  methods: {
    // 初始化流程对象
    async initFlow() {
      let options = {
        userId: this.ygtUserInfo.userId || '',
        clientId: this.ygtUserInfo.clientId || '',
        flowName: this.flowName, // 业务名称/流程名称
        _this: this,
        formStatus: '',
        flowId: '',
        serivalId: '' // 业务流水ID
      }

      // 不查询在途的业务
      if (
        this.noFlowBusiness.indexOf(this.flowName) > -1 &&
        !this.ygtUserInfo.userId
      ) {
        this.afterInitFlow(options)
        return
      }
      // 登录才能办理的业务在获取不到userId时拦截
      if (
        !$hvue.config.noLoginBusiness.includes(this.flowName) &&
        !this.ygtUserInfo.userId
      ) {
        // this.$router.replace({ name: 'index' })
        this.$router.replace({ name: 'login' })
        return
      }

      // 1、查询表单状态
      let formStatusResult = await queryUserFlowStatus({
        userId: options.userId,
        businessCode: options.flowName
      })
      if (formStatusResult.error_no === '0') {
        let formStatus = formStatusResult.results[0].formStatus
        $h.setSession('formStatus', formStatus)
        // 表单状态 0:表单未提交完(暂存)  1:表单已提交完成  2:表单已处理  3:结束  4:驳回  8:审核  9:待跑批(审核通过)
        // if (formStatus === '9' ||  formStatus === '4' || formStatus === '8') {
        //   // 跳转结果页，查询业务结果
        //   this.dealBusinessResult();
        // }
        options.formStatus = formStatusResult.results[0].formStatus || ''
        options.flowId = formStatusResult.results[0].flowId || ''
        options.serivalId = this.publicParam.serivalId =
          formStatusResult.results[0].serivalId

        console.log(
          '111flowId' +
            options.flowId +
            'serivalId' +
            this.publicParam.serivalId
        )
        // 根据业务配置是否有断点来判断是否结束流程isBreakPoint是否支持断点   1:支持   0：不支持
        if (this.isBreakPoint === '1') {
          if (options.flowId && formStatus === '0') {
            _hvueConfirm({
              mes: '检测您有未完成操作，是否继续上次操作？',
              opts: [
                {
                  txt: '取消',
                  color: false,
                  callback: () => {
                    // 结束原流水
                    this.closeOldFlow(options)
                  }
                },
                {
                  txt: '确定',
                  color: true,
                  callback: () => {
                    this.afterInitFlow(options)
                  }
                }
              ]
            })
          } else {
            this.afterInitFlow(options)
          }
        } else {
          if (options.flowId && formStatus === '0') {
            // 结束原流水
            this.closeOldFlow(options)
          } else {
            this.afterInitFlow(options)
          }
        }
      } else {
        _hvueAlert({ mes: formStatusResult.error_info })
      }
    },
    async afterInitFlow(options) {
      // 2、创建流程实例对象
      this.flow = new Flow(options)
      // 3、加载流程
      await this.flow.loadFlow()
      if (this.flow.currentStepInfo.flow_finish === '1' && options.flowId) {
        // 已经执行完成 且初始化时已经有flowId,则将原flowId的流程驳回到success,再重新加载。
        await this.flow.rejectFlow('success')
      }
      // 4、执行拦截器事件
      await this.flow.interceptorEvent()
      // 5、查询流程全部步骤信息
      this.stepsInfo = await this.flow.queryFlowInfo()
      // 6、执行初始化事件
      this.$emit('init')
    },
    /**
     * 初始化当前步骤
     */
    init(params) {
      this.$store.commit('updateNextBtnDisabled', true)
      this.isSubmitting = true
      // 先判断是否需要显示
      let paramMap = params || {}
      this.formData = {} // 初始化时清空formData数据
      this.isAllChildrenActivated = false
      this.activatedChild = []
      // 执行当前步骤初始化方法
      const currentStepName = this.flow.currentStepInfo.flow_current_step_name
      paramMap = Object.assign({}, this.publicParam, paramMap)
      if (currentStepName) {
        paramMap.flow_current_step_name = currentStepName
      }
      this.flow
        .init(paramMap)
        .then(res => {
          // 视频认证额外验证
          if (res.data.dsName.indexOf('oneOrTwoVideoPage') > -1) {
            const skipFlag = res['data']['oneOrTwoVideoPage'][0]['skipFlag']
            const skipStepNo = res['data']['oneOrTwoVideoPage'][0]['skipStepNo']
            if (skipFlag === '1') {
              _hvueConfirm({
                mes: '资料提交失败，请再次提交！',
                opts: [
                  {
                    txt: '取消',
                    color: false,
                    callback: () => {
                      this.$router.go(-1)
                    }
                  },
                  {
                    txt: '确定',
                    color: true,
                    callback: () => {
                      this.rollback(skipStepNo)
                    }
                  }
                ]
              })
              return
            }
          }
          // 协议签署额外验证
          if (res.data.dsName.indexOf('checkStepNo') > -1) {
            const skipFlag = res['data']['checkStepNo'][0]['skipFlag']
            const skipStepNo = res['data']['checkStepNo'][0]['skipStepNo']
            if (skipFlag === '1') {
              _hvueConfirm({
                mes: '资料提交失败，请再次提交！',
                opts: [
                  {
                    txt: '取消',
                    color: false
                  },
                  {
                    txt: '确定',
                    color: true,
                    callback: () => {
                      this.rollback(skipStepNo)
                    }
                  }
                ]
              })
              return
            }
          }
          // 步骤拦截弹窗特殊处理
          if (
            this.flow.currentStepInfo.flow_current_step_name ===
              'popInformation' &&
            res.componentResults[0][0].message
          ) {
            _hvueConfirm({
              mes: res.componentResults[0][0].message,
              opts: [
                {
                  txt: '取消',
                  color: false
                },
                {
                  txt: '确定',
                  color: true,
                  callback: () => {
                    this.next().then(res => {
                      this.$emit('init')
                    })
                  }
                }
              ]
            })
            return
          }
          this.$store.commit('updateIsShowHead', true) // 初始化展示头部 ，需要隐藏时在子组件处理
          this.initData = res.componentResults // 这里才是正式的代码
          // 返回步骤初始化的数据
          // 将keyWords数据传输组件剔除，并将其数据储存起来供协议列表agreementList使用
          let componentArray = res.componentName.filter(item => {
            return item !== 'keyWords'
          }) // 接口返回的组件
          let keyWordsIndex = res.componentName.indexOf('keyWords')
          if (keyWordsIndex >= 0) {
            this.keywords = this.initData.splice(keyWordsIndex, 1) || []
          }
          this.componentNames = componentArray
          this.componentList = componentArray.map(item => {
            // 将包含自动跳过组件的步骤添加到集合中，供返回时判断使用
            if (this.returnIgnoreComponents.includes(item)) {
              this.returnIgnoreStep.push(
                this.flow.currentStepInfo.flow_current_step_name
              )
              this.returnIgnoreStep = [...new Set(this.returnIgnoreStep)] // 去重
            }

            return {
              path: () => import(`../views/busComp/${item}.vue`),
              name: item
            }
          })
          for (const iterator of this.stepsInfo) {
            if (iterator.step_name === currentStepName) {
              if (currentStepName !== 'popInformation') {
                // 步骤拦截弹窗特殊处理
                this.stepName = iterator.description
              }
              break
            }
          }
          this.historyComponentInfo.push(this.componentList) // 保存当前组件到已经
          // 更新下一步按钮文字
          this.$store.commit('updateNextBtnText', '下一步')
          // 显示下一步按钮 初始化成功后再显示下一步按钮
          this.$store.commit('updateBusinessNextBtnStatus', true)
          this.$store.commit('updateNextBtnCss', false)

          // 初始化成功后，再允许用户点击下一步按钮
          this.$store.commit('updateNextBtnDisabled', false)
          this.isSubmitting = false
          _hvueLoading.closeLoading()
          // window.androidAppBack = this.back
          // registerNativeBack({
          //   callback: this.back
          // })
        })
        .catch(errorInfo => {
          console.log(errorInfo)
          // 初始化报错，隐藏下一步按钮 不能让用户点击 重新加载流程
          _hvueAlert({
            title: '错误提示',
            mes: errorInfo,
            callback: () => {
              // 初始化报错，流程无法继续往下走 初始化报错的场景不应该发生 一般都是查询数据，接口处理。此处直接返回首页
              // 直接后退
              this.$router.go(-1)
            }
          })
        })
    },
    // dealBusinessResult(){
    //   // 调用接口查询业务办理结果

    // },
    /**
     * 存放表单数据
     */
    putFormData(params) {
      this.formData = Object.assign(this.formData, params)
    },
    next(params, options) {
      // 添加防重复提交锁
      if (this.isSubmitting) {
        console.log('防止重复提交，请求已拦截')
        return Promise.reject({ error_info: '请勿重复提交' })
      }

      // 设置提交锁
      this.isSubmitting = true

      let paramMap = params || {}
      // 表单数据处理
      paramMap.formData = JSON.stringify([this.formData])
      let reqParam = Object.assign({}, this.publicParam, paramMap)

      // 执行下一步并在完成后（无论成功失败）释放锁
      return this.flow
        .next(reqParam, options)
        .then(result => {
          this.isSubmitting = false
          return result
        })
        .catch(error => {
          this.isSubmitting = false
          return Promise.reject(error)
        })
    },
    async back(params, backStep) {
      // 如果是从交易跳转过来的，返回到交易
      let source = this.$route.query.source
      if (source && source === 'trade') {
        closeYgt(0, '1A', 0, 1)
        return
      }
      // 当在第一步和驳回重走流程时返回首页
      let inputBackStep = backStep || ''
      if (inputBackStep) {
        let res = await this.flow.back(inputBackStep)
        if (res.error_no === '0') {
          // 需要重新加载流程
          await this.flow.loadFlow()
          // 重新初始化当前步骤
          this.$emit('init')
        } else {
          _hvueAlert({ mes: res.error_info })
        }
        return
      }

      // 根据缓存的路由历史长度计算要返回的步数
      const historyLength = $h.getSession('historyLength')
      const goBackLength = historyLength - history.length
      if (
        !this.flow ||
        this.historyStepInfo.length < 2 ||
        (this.historyStepInfo.length === 2 &&
          this.returnIgnoreStep.includes(this.historyStepInfo[0])) || // 返回时跳过自动跳过页
        this.flow.currentStepInfo.flow_reject === '1' ||
        this.flow.currentStepInfo.flow_finish === '1'
      ) {
        if (this.$store.state.returnHomeConfirm) {
          _hvueConfirm({
            mes: this.$store.state.returnHomeConfirmText,
            opts: [
              {
                txt: '取消',
                color: false,
                callback: () => {
                  registerNativeBack({
                    callback: this.back
                  })
                }
              },
              {
                txt: '确定',
                color: true,
                callback: () => {
                  this.$router.go(-1)
                }
              }
            ]
          })
          return
        }
        // 直接后退
        if (!historyLength) {
          this.$router.go(-1)
          return
        }
        if (goBackLength > 0) {
          this.$router.go(-1)
        } else if ($hvue.env === 'ths') {
          this.exitBusiness()
        } else {
          this.$router.push({ name: 'index' })
        }
        return
      }
      const backStepName = this.getHistoryStep(this.historyStepInfo.length - 2)
      if (backStepName) {
        this.rollback(backStepName)
        return
      }
      // 直接后退
      if (!historyLength) {
        this.$router.go(-1)
        return
      }
      if (goBackLength > 0) {
        this.$router.go(-1)
      } else if ($hvue.env === 'ths') {
        this.exitBusiness()
      } else {
        this.$router.push({ name: 'index' })
      }
    },
    exitBusiness() {
      _hvueConfirm({
        mes: '请确认是否退出业务？',
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              registerNativeBack({
                callback: this.back
              })
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              goBackXcApp()
            }
          }
        ]
      })
    },
    getHistoryStep(index) {
      if (this.returnIgnoreStep.includes(this.historyStepInfo[index])) {
        return this.getHistoryStep(index - 1)
      }
      return this.historyStepInfo[index]
    },
    /**
     * 驳回流程
     */
    async rejectFlow(rejectName) {
      if (rejectName) {
        this.flow.rejectFlow(rejectName).then(res => {
          if (res.error_no === '0') {
            this.initFlow()
          }
        })
      }
    },
    async rollback(backStepName) {
      let res = await this.flow.back(backStepName)
      if (res.error_no === '0') {
        const stepIndex = this.historyStepInfo.indexOf(backStepName)
        // 回退成功
        this.historyStepInfo.splice(
          stepIndex,
          this.historyStepInfo.length - stepIndex
        ) // 删掉节点
        // 切换组件
        // this.componentNames = this.historyComponentInfo[
        //   this.historyComponentInfo.length - 2
        // ]
        this.historyComponentInfo.splice(
          stepIndex,
          this.historyComponentInfo.length - stepIndex
        ) // 删除组件
        // 需要重新加载流程
        await this.flow.loadFlow()
        // 重新初始化当前步骤
        this.$emit('init')
      } else {
        _hvueAlert({ mes: res.error_info })
      }
    },
    // 执行子组件的 putFormData,reqParamPackage事件 存放表单数据与请求参数
    async emitNextEvent() {
      if (this.nextBtnDisabled || this.isSubmitting) {
        return
      }
      this.$store.commit('updateNextBtnDisabled', true)

      // 显示加载状态
      _hvueLoading.openLoading('提交中...')

      try {
        // 控制按钮此时无法点击
        const userRef = this.$refs
        let childrens = this.componentList.map(item => {
          return (userRef[item.name] && userRef[item.name][0]) || ''
        })
        console.log(childrens)
        let reqParams = {} // 请求参数
        let promiseAllAry = []
        for (let index = 0; index < childrens.length; index++) {
          let children = childrens[index]
          if (children.doCheck) {
            promiseAllAry.push(children.doCheck()) // 执行子组件的校验事件
          }
        }
        await Promise.all(promiseAllAry)
        for (let index = 0; index < childrens.length; index++) {
          let children = childrens[index]
          if (children.reqParamPackage) {
            let childReqParam = children.reqParamPackage()
            // 合并子组件的请求参数
            reqParams = Object.assign(reqParams, childReqParam)
          }
          if (children.putFormData) {
            // 合并子组件的表单数据
            this.formData = Object.assign(this.formData, children.putFormData())
          }
          // 合并上一步骤返回需要在这一步骤提交的参数
          this.formData = Object.assign(this.formData, this.passbackParam)
          this.passbackParam = {}
        }
        // 继续执行下一步
        const currentStepName = this.flow.currentStepInfo.flow_current_step_name
        if (currentStepName) {
          reqParams.flow_current_step_name = currentStepName
        }

        try {
          const res = await this.next(reqParams)
          // 判断是否返回了表单结果，是的话将接口返回的serivalId和flowId同步到前端参数中
          if (res.dsName.indexOf('formData') > -1) {
            let formData = res['formData'][0]
            this.publicParam.serivalId = formData.serivalId
            this.publicParam.flowId = formData.flowId
            console.log(
              '222--flowId' +
                this.publicParam.flowId +
                '-serivalId-' +
                this.publicParam.serivalId
            )
          }
          // 判断是否需要对提交的结果进行处理 如果需要 需要在子组件中触发init事件。
          for (let index = 0; index < childrens.length; index++) {
            if (childrens[index].handleResult) {
              // 需要对结果集进行处理
              childrens[index].handleResultFunc(res)
              return
            }
          }
          this.preSubmitResult = res['DataSet'][0]
          this.$emit('init', this.flowParamDelay ? reqParams : {})
        } catch (error) {
          this.$store.commit('updateNextBtnDisabled', false)
          this.isSubmitting = false
          _hvueLoading.closeLoading()
          const returnToMainPageErrorNo = ['-8800018']
          console.log(error)
          // 初始化报错，隐藏下一步按钮 不能让用户点击 重新加载流程
          _hvueAlert({
            title: '错误提示',
            mes: error.error_info,
            callback: () => {
              // 直接后退到首页
              if (returnToMainPageErrorNo.includes(error.error_no)) {
                this.$router.go(-1)
                return
              }

              for (let index = 0; index < childrens.length; index++) {
                if (childrens[index].handleError) {
                  // 执行子组件报错处理方法
                  childrens[index].handleErrorFunc(error)
                }
              }
            }
          })
        }
      } catch (e) {
        this.$store.commit('updateNextBtnDisabled', false)
        this.isSubmitting = false
        _hvueLoading.closeLoading()
      }
    },
    // 关闭原流程
    async closeOldFlow(options) {
      let closeResults = await closeFlow({
        userId: options.userId,
        businessCode: options.flowName,
        serivalId: options.serivalId
      })
      if (closeResults.error_no === '0') {
        // 重新加载流程
        this.initFlow()
      } else {
        _hvueAlert({
          title: '提示',
          mes: closeResults.error_info,
          callback: () => {
            // 直接后退
            this.$router.go(-1)
          }
        })
      }
    },
    // 业务办理完成
    businessFinish() {
      $h.clearSession('toPage')
      const passwordType = $h.getSession('passwordType')
      if (
        this.$route.query.type === 'czmm' ||
        (this.$route.query.type === 'xgmm' &&
          passwordType &&
          passwordType.includes('2'))
      ) {
        // 重置密码办理成功时登出
        this.logout()
      } else {
        this.$router.push({ name: 'index', params: {} })
      }
    },
    // 退出登录
    logout() {
      // h5访问时退出登录
      if ($hvue.platform === '0') {
        $h.clearSession('ygtUserInfo')
        $h.clearSession('isLogin')
        $h.clearSession('ssoInfo')
        $h.clearSession('passwordType')
        this.$router.replace({ name: 'login', params: {} })
      } else {
        closeYgt(1, '1A', 0, 1) // 退出网厅
      }
    },
    childActivated(index) {
      // 更新已加载的子组件列表
      this.activatedChild.push(this.componentNames[index])
      // 判断当前步骤子组件是否都已加载
      if (
        this.componentNames.every(component => {
          return this.activatedChild.some(activatedChildComponent => {
            return component === activatedChildComponent
          })
        })
      ) {
        // 子组件都已加载时改变标识的值，并触发allChildrenActivated事件通知子组件
        this.isAllChildrenActivated = true
        this.$bus.emit('allChildrenActivated')
      }
    }
  },
  destroyed() {
    this.$store.commit('updateNextBtnText', '下一步')
    // 退出后隐藏下一步按钮
    this.$store.commit('updateBusinessNextBtnStatus', false)
  }
}
</script>

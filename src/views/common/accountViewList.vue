<template>
  <div class="page_main" v-cloak>
    <headComponent headerTitle="资质查询"></headComponent>
    <article class="content">
      <div class="notice_box spel">
        <div class="pic">
          <img src="@/assets/images/not_ic04.png" />
        </div>
        <h5>您的账户已全部开通</h5>
        <p>您的账户已全部开通{{flowNameCn}}</p>
      </div>
      <div class="result_sfinfo">
        <ul>
          <li v-for="(item,index) in accountList" :key="index">
            <span class="tit">{{item.marketName}} {{item.stockAccount}}</span>
            <p>
              <span class="status">已开通</span>
            </p>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class="ui button block rounded" @click.stop="toIndex">返回主页</a>
      </div>
    </article>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  components: { headComponent },
  props: ['flowNameCn', 'initData'],
  data () {
    return {}
  },
  computed: {
    accountList() {
      if (typeof this.initData === 'string') {
        return typeof JSON.parse(this.initData) === 'string'
          ? JSON.parse(JSON.parse(this.initData))
          : JSON.parse(this.initData)
      } else {
        return this.initData.SecAccountList
      }
    }
  },
  mounted () {
    // window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack = this.pageBack
  },
  methods: {
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    },
    // 返回
    pageBack () {
      this.$router.push({ name: 'index', params: {} })
      // 调用父类返回
      // this.$parent.pageBack();
    }
  }
}
</script>

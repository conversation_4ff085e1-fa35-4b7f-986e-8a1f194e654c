<template>
  <div class="page_main" v-cloak>
    <headComponent :headerTitle="agreementTitle"></headComponent>
    <article class="content" style="overflow:auto;">
      <div class="protocol_cont">
        <!-- <div class="title">{{ agreementTitle }}</div> -->
        <div class="xy_cont" v-html="agreementContent"></div>
      </div>
      <div class="bottom_check">
        <div class="ce_btn mt20">
          <a v-if="showReadSeconds" class="p_button disabled"
            >请认真阅读以上内容({{ readSeconds }}s)</a
          >
          <a
            v-else
            class="p_button"
            :class="{
              disabled: isSubmitting
            }"
            @click.prevent="confirmSubmit"
            >{{ btnDecs }}</a
          >
        </div>
      </div>
    </article>
    <!-- <div class="bottom_check">
      <p class="tips" v-if="tips">{{ tips }}</p>
      <div class="ce_btn mt20">
        <a v-if="showReadSeconds" class="p_button disabled"
          >请认真阅读以上内容({{ readSeconds }}s)</a
        >
        <a v-else class="p_button" @click.prevent="confirmSubmit">{{
          btnDecs
        }}</a>
      </div>

    </div> -->
  </div>
</template>
<script>
import headComponent from '@/components/headComponent'
import { getProtocolById } from '@/service/comServiceNew'
import { registerNativeBack } from '@/common/util'

export default {
  components: {
    headComponent
  },
  data() {
    return {
      agreementId: '',
      agreementTitle: '',
      agreementContent: '',
      tips: '',
      agreementType: '',
      readSeconds: 0,
      interval: null,
      btnDecs: '确认阅读并签署',
      readTime10Business: ['lryzx', 'zkgdh'],
      readTime15Business: ['xh'],
      readTime20Business: ['xsbkt'],
      readTime30Business: ['cybkt', 'fxjskt', 'kcbkt'],
      isSubmitting: false
    }
  },
  created() {
    this.$store.commit('updateIsWhite', true)
    // 查詢协议详情
    this.agreementId = this.$route.query.agreementId
    this.agreementTitle = this.$route.query.agreementTitle
    this.agreementType = this.$route.query.agreementType
    this.headerTitle = this.agreementTitle
    this.tips = this.$route.query.tips
    const type = this.$route.query.type
    // console.log(type)
    if (type === 'dzht') {
      // 电子合同
      this.btnDecs = '返回'
    }
    // let hasRead = $h.getSession('agreementIds') || ''
    // if (hasRead.indexOf(this.agreementId) === -1) {
    //   if (this.readTime10Business.includes(type)) {
    //     this.readSeconds = 10
    //   } else if (this.readTime15Business.includes(type)) {
    //     this.readSeconds = 15
    //   } else if (this.readTime20Business.includes(type)) {
    //     this.readSeconds = 20
    //   } else if (this.readTime30Business.includes(type)) {
    //     this.readSeconds = 30
    //   }
    // } else {
    //   this.readSeconds = 0
    // }
    // if (process.env.NODE_ENV === 'development') this.readSeconds = 0
    // this.startCountDown()
  },
  computed: {
    showReadSeconds() {
      if (this.readSeconds === 0) {
        return false
      } else {
        return true
      }
    }
  },
  mounted() {
    // window.phoneBackBtnCallBack = this.pageBack
    //  window.androidAppBack =  this.pageBack
    console.log('协议详情 注册返回事件')
    registerNativeBack({
      callback: this.pageBack
    })
    getProtocolById({
      agreementId: this.agreementId,
      agreementType: this.agreementType,
      userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
      keyWords: this.$route.query.keyWords
    }).then(res => {
      if (res.error_no === '0') {
        let results = res.results || res.DataSet
        this.agreementContent = results[0].agreeContent

        let hasRead = $h.getSession('agreementIds') || ''
        let readTime = +results[0].readTime

        this.agreementContent = results[0].agreeContent
        if (!hasRead.includes(this.agreementId)) {
          this.readSeconds = readTime
        } else {
          this.readSeconds = 0
        }
        if (process.env.NODE_ENV === 'development') this.readSeconds = 0
        this.startCountDown()
      } else {
        _hvueAlert({ mes: res.error_info })
      }
    })
  },
  methods: {
    startCountDown() {
      if (this.readSeconds > 0) {
        const countDown = setInterval(() => {
          this.readSeconds--
          if (this.readSeconds === 0) {
            clearInterval(countDown)
          }
        }, 1000)
        this.$once('hook:deactivated', () => {
          this.clearCountDown(countDown)
        })
      }
    },
    clearCountDown(countDown) {
      if (countDown) {
        clearInterval(countDown) || (countDown = null)
      }
    },
    confirmSubmit() {
      if (this.isSubmitting) return
      this.isSubmitting = true

      // 标记已经阅读过的协议
      let readAgreements = $h.getSession('agreementIds') || ''
      if (!readAgreements.includes(this.agreementId)) {
        $h.setSession(
          'agreementIds',
          readAgreements
            ? readAgreements + ',' + this.agreementId
            : this.agreementId
        )
      }
      this.$bus.emit('isReadAgreement', this.$route.query.agreementIndex) // 通过vuebus调用
      this.pageBack()
    },
    pageBack() {
      // 添加延时，防止快速连续点击
      setTimeout(() => {
        this.isSubmitting = false
      }, 500)
      this.$router.go(-1)
    }
  },
  destroyed() {
    this.$store.commit('updateIsWhite', false)
  }
}
</script>

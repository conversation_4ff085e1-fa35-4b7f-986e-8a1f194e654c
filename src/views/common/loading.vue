<template>
  <div class="hui-dialog-white-mask" v-if="isShow">
    <div class="hui-loading">
      <div class="hui-timer">
        <div></div>
      </div>
      <div class="hui-loading-txt">{{defaultText}}</div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "loading",
  data() {
    return {

    };
  },
  computed: {
    isShow() {
      return this.$store.state.isShowLoading;
    },
    defaultText() {
      return this.$store.state.tips;
    }
  }
};
</script>
<style>
/* .hui-dialog-white-mask {
    background-color: transparent;
}
.hui-dialog-black-mask, .hui-dialog-white-mask {
    position: fixed;
    z-index: 2000;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-pack: center;
    -webkit-box-pack: center;
    justify-content: center;
    -ms-flex-align: center;
    -webkit-box-align: center;
    align-items: center;
}
.hui-loading {
    border-radius: 3px;
    color: #fff;
    opacity: .8;
    background: rgba(0,0,0,.7);
    -webkit-animation: hui-kf-zoom-in .1s ease forwards;
    animation: hui-kf-zoom-in .1s ease forwards;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-direction: row;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    -ms-flex-pack: center;
    -webkit-box-pack: center;
    justify-content: center;
    padding: 14px 20px;
}
.hui-timer, .hui-timer>div {
    width: 22px;
    height: 22px;
}
.hui-timer {
    margin: 0 auto;
    display: block;
    font-size: 0;
    color: #fff;
}
.hui-timer>div {
    background: transparent;
    border-width: 2px;
    border-radius: 100%;
}
.hui-timer, .hui-timer>div {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.hui-timer>div:before {
    height: 8px;
}
.hui-timer>div:after, .hui-timer>div:before {
    position: absolute;
    top: 10px;
    left: 10px;
    display: block;
    width: 2px;
    margin-top: -1px;
    margin-left: -1px;
    content: "";
    background: currentColor;
    border-radius: 2px;
    -webkit-transform-origin: 1px 1px 0;
    transform-origin: 1px 1px 0;
    -webkit-animation: timer-loader 1.25s infinite linear;
    animation: timer-loader 1.25s infinite linear;
    -webkit-animation-delay: -625ms;
    animation-delay: -625ms;
} */
</style>


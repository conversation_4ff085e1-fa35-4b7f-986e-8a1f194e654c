<template>
  <div class="page_main" v-cloak>
    <headComponent headerTitle="结果页"></headComponent>
    <article class="content">
      <slot>
        <div class="result_main">
          <div class="icon_ok"></div>
          <h5>{{title}}成功</h5>
          <p class="center">您于 {{busTime|formatDate('YYYY.MM.DD hh:mm:ss')}}{{title}}成功</p>
        </div>
        <div class="ce_btn mt20">
          <a class="ui button block rounded" @click.stop="submit">返回首页</a>
        </div>
      </slot>
    </article>
  </div>
</template>
<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  components: { headComponent },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      busTime: new Date()
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    showResult (flowNameCn) {
      this.busTime = new Date()
    },
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    },
    submit () {
      // 调用父组件提交事件方法
      this.$emit('my-event')
    },
    pageBack () {
      // 调用父组件提交事件方法
      this.$emit('my-event')
    }
  }
}
</script>

<template>
  <div class="xh_must_box">
    <h5 class="title"><span>{{ accountTypeName }}</span></h5>
    <div class="xh_must_list">
      <div class="xh_must_item" v-for="(item, index) in accountList" :key="index">
        <div class="tit">
          <p>{{ item.account }}</p>
          <em>{{ item.accountName || item.marketName || '' }}</em>
          <span class="status" :class="{ ok: item.flag, error: !item.flag }">{{ item.flag ? '通过' : '未通过' }}</span>
        </div>
        <ul class="list" v-show="!item.flag && item.reason">
          <li class="error" v-for="(subItem, subIndex) in item.reason" :key="subIndex">
            <p>{{ subItem.desc }}</p>
            <a v-show="subItem.reasonCode === '0'" class="link" href="javascript:;">前往结息</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'cancelAccountConditionDetail',
  props: ['accountList', 'accountTypeName'],
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

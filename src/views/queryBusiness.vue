<template>
  <div class="page_main" :class="className" v-if="isShow">
    <header class="page_header header">
      <div class="header_inner spel">
        <a class="icon_back" @click.stop="pageBack"></a>
        <div class="search_box">
          <div class="search_input">
            <i class="icon"></i>
            <input ref="searchInput" v-model.trim="queryBusiness" type="text" placeholder="请输入搜索内容" />
            <a class="txt_close" @click.stop="queryBusiness = ''" v-show="queryBusiness!=''"></a>
          </div>
        </div>
      </div>
    </header>
    <article class="content">
      <div class="search_content">
        <div v-if="searchList.length==0 && queryBusiness!=''" class="search_empty">
          <p>
            抱歉，没有找到与“<strong>{{queryBusiness}}</strong>”相关的业务
          </p>
        </div>
        <div v-else class="nav_list">
          <ul>
            <li
              v-for="(menuItem,index) in searchList"
              :key="index"
              @click.stop="toBusiness(menuItem)"
            >
              <i class="icon">
                <img :src="menuItem.mobileImgUrl" />
              </i>
              <h5>{{menuItem.businessName}}</h5>
              <p>{{menuItem.description}}</p>
            </li>
          </ul>
        </div>
      </div>
    </article>
  </div>
</template>

<script>
import pinyin from 'pinyin-match'
export default {
  name: 'queryBusiness',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      queryBusiness: '',
      allBusiness: $h.getSession('allBusiness'),
      isLogin: $h.getSession('isLogin', { decrypt: false }) || false,
      showBusinessArr: [],
      searchList: [],
      className: 'query'
    }
  },
  watch: {
    queryBusiness: function (newval, oldval) {
      this.searchList = []
      if (newval === '') {
        return
      }
      let allBusinessArr = this.showBusinessArr
      for (var item in allBusinessArr) {
        if (allBusinessArr.hasOwnProperty(item)) {
          if (allBusinessArr[item].businessName.indexOf(newval) !== -1) {
            this.addSearchList(allBusinessArr[item])
          } else {
            let m = pinyin.match(allBusinessArr[item].businessName, newval)
            if (m) {
              this.addSearchList(allBusinessArr[item])
            }
          }
        }
      }
    },
    isShow: {
      immediate: true,
      handler (newval, oldval) {
        this.$nextTick(() => {
          this.$refs.searchInput.focus()
        })
      }
    }
  },
  mounted() {
    this.getShowBusinessArr()
  },
  methods: {
    getShowBusinessArr() {
      this.showBusinessArr = this.allBusiness.filter(item => {
        return (!this.isLogin || !$hvue.config.loginHideBusiness.includes(item.businessCode))
      })
    },
    toBusiness (item) {
      this.$emit('toBusiness', item)
    },
    addSearchList (searchInfo) {
      this.searchList.push(searchInfo)
    },
    pageBack () {
      this.$emit('change', false)
    }
  }
}
</script>
<style>
</style>

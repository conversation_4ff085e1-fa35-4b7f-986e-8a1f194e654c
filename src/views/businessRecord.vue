<template>
  <div class="page_main">
    <header class="header">
      <div class="header_inner spel">
        <a class="icon_back" @click.stop="pageBack" href="javascript:;"></a>
        <h1 class="title text-center">业务进度查询</h1>
      </div>
      <div class="tab_nav">
        <ul>
          <li :class="{ active: checkType === 1 }">
            <a href="javascript:;" @click.stop="checkType = 1">
              <span>全部</span>
            </a>
          </li>
          <li :class="{ active: checkType === 0 }">
            <a href="javascript:;" @click.stop="checkType = 0">
              <span>进行中</span>
            </a>
          </li>
        </ul>
      </div>
    </header>
    <article class="content">
      <div class="scrollWrapper" v-show="progressList.length > 0" style="overflow:hidden;">
        <scroll
          :height="scrollheight"
          :pullupActive="!noMoreData"
          @pullingDown="pullDownHandler"
          @pullingUp="pullupHandler"
          ref="scroll"
          :tiptxt="tiptext"
        >
          <div class="bus_record">
            <div class="nav_list">
              <ul>
                <li v-for="(item, index) in progressList" :key="index"
                  @click.stop="goBusinessResult(item)">
                  <i class="icon">
                    <img :src="item.mobileImgUrl">
                  </i>
                  <h5>{{ item.businessName }}</h5>
                  <p>{{ item.createDate }}</p>
                  <span class="state"
                    :class="{ ok: finishedType.includes(item.formStatus) }"
                  >{{ handleStatusMap[item.handleStatus] || formStatusMap[item.formStatus] }}</span>
                </li>
              </ul>
            </div>
          </div>
        </scroll>
      </div>
    </article>
  </div>
</template>

<script>
import scroll from '@/components/scroll'
import { queryInProgressBusiness, queryBusinessResult } from '@/service/comServiceNew'

export default {
  name: 'businessRecord',
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  components: { scroll },
  data() {
    return {
      checkType: 1,
      progressList: [],
      formStatusMap: { // 表单状态字典
        '0': '表单未提交完',
        '1': '处理中',
        '2': '办理完成',
        '3': '已结束',
        '4': '被驳回',
        '5': '主动结束办理',
        '6': '已作废',
        '8': '审核中',
        '9': '处理中'
      },
      handleStatusMap: {
        '1': '办理成功',
        '2': '办理失败'
      },
      finishedType: ['2', '3'], // 流程结束的类型
      currentPage: 1,
      totalPages: 0,
      noMoreData: false,
      tiptext: '没有更多内容啦',
      scrollheight: '100%',
      noResultPageFormStatus: ['5', '6'],
      noResultPageBusiness: ['xgmm', 'zlxg', 'fxcp']
    }
  },
  watch: {
    isShow: {
      immediate: true,
      handler (newValue) {
        this.queryProgressList()
      }
    },
    checkType (newValue) {
      this.pullDownHandler()
    }
  },
  mounted () {
    this.scrollheight = window.innerHeight - 98
    this.queryProgressList()
  },
  methods: {
    // 查询历史业务办理记录
    queryProgressList () {
      queryInProgressBusiness({
        clientId: $h.getSession('ygtUserInfo', { decrypt: false }).clientId,
        queryFlag: this.checkType,
        currentPage: this.currentPage
      }).then(res => {
        if (res.error_no === '0') {
          const businessHandlingRecord = res.businessHandlingRecord[0]
          const flowList = JSON.parse(businessHandlingRecord.data)

          this.totalPages = businessHandlingRecord.totalPages
          this.progressList.push(...flowList.map(item => {
            return {
              ...item,
              mobileImgUrl: (process.env.NODE_ENV === 'development'
                ? '' : ROUTER_BASE) + item.mobileImgUrl
            }
          }))
          if (this.currentPage >= this.totalPages) {
            this.noMoreData = true
          }
          this.$refs.scroll.refresh()
        } else {
          _hvueToast({
            icon: 'error',
            mes: res.error_info
          })
        }
      })
    },
    // 跳转业务办理结果页
    goBusinessResult (flow) {
      if (this.noResultPageBusiness.includes(flow.businessCode) ||
        (this.noResultPageFormStatus.includes(flow.formStatus) && !this.handleStatusMap[flow.handleStatus])) {
        return
      }

      // console.log(flow)
      // queryBusinessResult({
      //   userId: $h.getSession('ygtUserInfo', { decrypt: false }).userId,
      //   businessCode: flow.businessCode,
      //   serivalId: flow.serivalId
      // }).then(res => {
      //   if (res.error_no === '0') {
      //     console.log(res)
      //   } else {
      //     _hvueToast({
      //       icon: 'error',
      //       mes: res.error_info
      //     })
      //   }
      // })
      this.$router.push({
        name: 'business',
        query: {
          type: flow.businessCode
        },
        params: {
          flowInfo: {
            flowId: flow.flowId,
            serivalId: flow.serivalId
          }
        }
      })
    },
    // 上拉刷新
    pullDownHandler () {
      this.currentPage = 1
      this.noMoreData = false
      this.progressList = []
      this.queryProgressList()
    },
    // 下拉加载
    pullupHandler () {
      if (this.currentPage >= this.totalPages) {
        this.noMoreData = true
        return
      }
      this.currentPage++
      this.queryProgressList()
    },
    pageBack () {
      if (!this.isShow) {
        this.$router.go(-1)
        return
      }
      this.$emit('change', false)
    }
  }
}
</script>

<style></style>

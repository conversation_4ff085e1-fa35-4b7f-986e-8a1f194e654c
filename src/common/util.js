import { getDict, businessList } from '@/service/comServiceNew'

/**
 * 身份证号获取性别值
 * @param c 身份证号码 15或18位
 * @returns {number} 0男 1女     注意大中台版本性别数据字典是 0男 1女   集中作业平台 0女 1男
 */
export function idCardToSex(c) {
  let i = 1
  if (c) {
    i = c.length === 15 ? +c.char<PERSON>t(14) % 2 : +c.char<PERSON>t(16) % 2
  }
  return i % 2
}

/**
 * 身份证号获取出生日期
 * @param c 身份证号码 15或18位
 * @returns 返回格式 1994-07-01
 */
export function idCardToBirthday(c) {
  if (c) {
    if (c.length === 18) {
      return c.replace(/\d{6}(\d{4})(\d{2})(\d{2})\d{3}[\dXx]/, '$1-$2-$3')
    } else if (c.length === 15) {
      return c.replace(/\d{6}(\d{2})(\d{2})(\d{2})\d{3}/, '19$1-$2-$3')
    }
  }
}

/**
 * 查询字典方法 参数为对象
 * 可以直接传入key或者value来返回对应的值 比如[{a:1,b:2}]  传入key=a返回1 传入value=1返回a
 * 也可以不传key和value 直接返回整个字典数组
 * @param d{type 数据字典类型 Y, key 查询键, value 查询值}
 * @param f 回调函数 参数返回数组或对象 [{key:1, value:2}]
 */
export function queryDictionary(d, f) {
  let dictionaryCacheName = 'dictionary_' + d.type
  let callback = function(data) {
    if (data && data.length > 0) {
      let arr = []
      for (let s = 0; s < data.length; s++) {
        let t = {
          key: data[s].itemValue,
          value: data[s].itemName,
          type: d.type,
          index: s
        }
        if ((d.key && d.key === t.key) || (d.value && d.value === t.value)) {
          f(t)
          return
        }
        arr.push(t)
      }
      f(arr)
      return
    }
    f('')
  }

  let dc = sessionStorage.getItem(dictionaryCacheName)
  if (dc) {
    callback(JSON.parse(dc))
  } else {
    getDict({
      enumNo: d.type
    }).then(data => {
      if (data.error_no === '0' && data.results && data.results.length > 0) {
        sessionStorage.setItem(
          dictionaryCacheName,
          JSON.stringify(data.results)
        )
        callback(data.results)
      } else {
        callback()
      }
    })
  }
}

/**
 * 校验输入框数据格式
 * @param $el 需要检测格式的input对象
 * @param isShowTip 是否弹出错误提示
 * @returns 返回检验结果，通过为空字符串
 */
export function checkInput($el, isShowTip) {
  if (isShowTip === undefined) {
    isShowTip = true
  }
  let flag = ''
  let $this = $el
  let val = $this.value.trim()
  // 处理：非必填项如果为空时，不做校验，不为空时做校验
  if (!!$this.must && $this.must === 'no') {
    if ($h.isEmptyString(val)) {
      return flag
    }
  }
  if ($h.isEmptyString(val)) {
    flag = ($this.name.includes('输入') ? '请' : '请输入') + $this.name
    isShowTip
      ? _hvueToast({
          mes: flag
        })
      : ''
    return flag
  }
  if (!!$this.minlength && val.length < $this.minlength) {
    flag = '请输入最低为' + $this.minlength + '位的' + $this.name
    isShowTip
      ? _hvueToast({
          mes: flag
        })
      : ''
    return flag
  }
  if ($this.format) {
    if ($this.format === 'tel,phone') {
      // 电话或手机号
      if (!$h.isTel(val) && !/^1\d{10}$/.test(val)) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip
          ? _hvueToast({
              mes: flag
            })
          : ''
        return flag
      }
    } else if ($this.format === 'name') {
      let reg = /^[\u4E00-\u9FA5]{2,4}$/
      if (!reg.test(val)) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip
          ? _hvueToast({
              mes: flag
            })
          : ''
        return flag
      }
      if (val.length < 2) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip
          ? _hvueToast({
              mes: flag
            })
          : ''
        return flag
      }
    } else if ($this.format === 'pwd') {
      if (!$h.isNum(val)) {
        flag = $this.name + '输入格式有误，请重填'
        isShowTip
          ? _hvueToast({
              mes: flag
            })
          : ''
        return flag
      }
      if (!isStrongPwd(val)) {
        flag = '请勿输入简单组合的' + $this.name
        isShowTip
          ? _hvueToast({
              mes: flag
            })
          : ''
        return flag
      }
    } else if (
      ($this.format === 'enNum' && !$h.isEnNum(val)) ||
      ($this.format === 'phone' && !/1\d{10}/.test(val)) ||
      ($this.format === 'email' && !$h.isEmail(val)) ||
      ($this.format === 'cnEnNum' && !$h.isCnEnNum_(val)) ||
      ($this.format === 'num' && !$h.isNum(val))
    ) {
      // 英文数字/手机号/电子邮箱/身份证号/中文英文数字/数字
      flag = $this.name + '输入格式有误，请重填'
      isShowTip
        ? _hvueToast({
            mes: flag
          })
        : ''
      return flag
    } else if ($this.format === 'idno' && !checkIDCard(val)) {
      // 英文数字/手机号/电子邮箱/身份证号/中文英文数字/数字
      flag = $this.name + '输入格式有误，请重填'
      isShowTip
        ? _hvueToast({
            mes: flag
          })
        : ''
      return flag
    }
  }
  return flag
}
/**
 * 判断字符串是否为强密码
 * @param {*} str
 */
export function isStrongPwd(str) {
  // 匹配4位顺增或顺降
  let pattern = /(?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)){3}\d/
  if (pattern.test(str)) {
    return false
  }
  pattern = /(?:9(?=8)|8(?=7)|7(?=6)|6(?=5)|5(?=4)|4(?=3)|3(?=2)|2(?=1)|1(?=0)){3}\d/
  if (pattern.test(str)) {
    return false
  }
  // 匹配3位以上的重复数字 字母
  pattern = /(\w)*(\w)\2{2}(\w)*/g
  if (pattern.test(str)) {
    return false
  }
  return true
}

// 函数参数必须是字符串，因为二代身份证号码是十八位，而在javascript中，十八位的数值会超出计算范围，造成不精确的结果，导致最后两位和计算的值不一致，从而该函数出现错误。
// 详情查看javascript的数值范围
export function checkIDCard(idcode) {
  // 加权因子
  let weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  // 校验码
  let check_code = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  let code = idcode + ''
  let last = idcode[17] // 最后一位

  let seventeen = code.substring(0, 17)

  // ISO 7064:1983.MOD 11-2
  // 判断最后一位校验码是否正确
  let arr = seventeen.split('')
  let len = arr.length
  let num = 0
  for (let i = 0; i < len; i++) {
    num = num + arr[i] * weight_factor[i]
  }

  // 获取余数
  let resisue = num % 11
  let last_no = check_code[resisue]

  // 格式的正则
  // 正则思路
  /**
   * 第一位不可能是0
   * 第二位到第六位可以是0-9
   * 第七位到第十位是年份，所以七八位为19或者20
   * 十一位和十二位是月份，这两位是01-12之间的数值
   * 十三位和十四位是日期，是从01-31之间的数值
   * 十五，十六，十七都是数字0-9
   * 十八位可能是数字0-9，也可能是X
   */
  let idcard_patter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/

  // 判断格式是否正确
  let format = idcard_patter.test(idcode)

  // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
  return !!(last === last_no && format)
}

/**
 * 查询业务标签配置
 * @param {业务编号} code
 */
export function queryBusiness(code, f) {
  let callback = function(data) {
    if (data && data.length > 0) {
      let arr = []
      for (let s = 0; s < data.length; s++) {
        let t = data[s]
        if (code && code === t.businessCode) {
          f(t)
          return
        }
        arr.push(t)
      }
      f(arr)
      return
    }
    f('')
  }

  let dc = $h.getSession('allBusiness')
  if (dc) {
    callback(dc)
  } else {
    businessList({ source: 2 }, {}).then(
      res => {
        if (res.error_no === '0') {
          let allBusiness = []
          let allBusinessArr = []
          let hotBusiness = []
          for (let i = 0; i < res.ismpCompQueryMenu.length; i++) {
            let el = res.ismpCompQueryMenu[i]
            let menus = (el.businessMenu = JSON.parse(el.businessMenu))
            for (let j = 0; j < menus.length; j++) {
              let menu = menus[j]
              menu.mobileImgUrl =
                (process.env.NODE_ENV == 'development' ? '' : ROUTER_BASE) +
                menu.mobileImgUrl
              if (menu.isHot === '1') {
                hotBusiness.push(menu)
              }
              allBusiness.push(menu)
            }
            allBusinessArr.push(el)
          }
          $h.setSession('allBusinessArr', allBusinessArr)
          $h.setSession('allBusiness', allBusiness)
          $h.setSession('hotBusiness', hotBusiness)
          callback(allBusiness)
        } else {
          _hvueToast({
            icon: 'error',
            mes: res.error_info
          })
        }
      },
      err => {
        console.log(err)
      }
    )
  }
}

/**
 * 获取地址栏参数
 * @param {参数名} key
 */
export function getUrlParam(key) {
  var pageParam = location.search.slice(1)
  var paramArr = pageParam.split('&')
  var paramJson = {}
  paramArr.forEach(item => {
    var flagIndex = item.indexOf('=')
    paramJson[item.substring(0, flagIndex)] = item.substring(
      flagIndex + 1,
      item.length
    )
  })
  var value = paramJson[key]
  if (value) {
    return value
  } else {
    return ''
  }
}

/**
 * 压缩图片
 * @param {图片} img
 * @param {图片质量（压缩比例）} quality
 */
export function compressImg(img, quality) {
  let canvas = document.createElement('canvas')
  let ctx = canvas.getContext('2d')
  const width = img.width
  const height = img.height
  canvas.width = width
  canvas.height = height

  // 铺底色
  ctx.fillStyle = '#fff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  ctx.drawImage(img, 0, 0, width, height)

  // 压缩
  let compressedImg = canvas.toDataURL('image/jpeg', quality)
  console.log('compressed size: ', compressedImg.length / 1024)
  return compressedImg
}

// 设置光标位置
export function setCaretPosition(element, pos) {
  let range, selection
  if (document.createRange) {
    range = document.createRange()
    range.selectNodeContents(element)
    if (element.innerHTML.length > 0) {
      range.setStart(element.childNodes[0], pos)
    }
    range.collapse(true)
    selection = window.getSelection()
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

export function ajax(options) {
  var xhr = null
  var params = formsParams(options.data)
  //创建对象
  if (window.XMLHttpRequest) {
    xhr = new XMLHttpRequest()
  } else {
    xhr = new ActiveXObject('Microsoft.XMLHTTP')
  }
  options.type = options.type.toUpperCase()
  // 连接
  if (options.type == 'GET') {
    xhr.open(options.type, options.url + '?' + params, options.async)
    xhr.send(null)
  } else if (options.type == 'POST') {
    xhr.open(options.type, options.url, options.async)
    //xhr.setRequestHeader("Content-type","application/json; charset=utf-8")
    //xhr.send(params);
    xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded')
    // 将数据通过send方法传递
    xhr.send(params)
  }
  xhr.onreadystatechange = function() {
    if (xhr.readyState == 4 && xhr.status == 200) {
      options.success(xhr.responseText)
    }
  }
  function formsParams(data) {
    var arr = []
    for (var prop in data) {
      arr.push(prop + '=' + data[prop])
    }
    return arr.join('&')
  }
}
export function goBackXcApp() {
  let backToApp = $h.getSession('backToApp')
  const fromSiteUrl = $h.getSession('fromSiteUrl', { decrypt: false })
  const gsfyParam = $h.getSession('gsfyParam')
  const isLoginSD = $h.getSession('isLoginSD')
  let dw_token = isLoginSD?'&ztToken='+$h.getSession('dw_token'):''
  try {
    if (backToApp == '1') {
      invokeNative('8002', '000', {}, data => {})
    } else {
      // window.history.back()
      if (fromSiteUrl) {
        if(!fromSiteUrl.includes('sys_from')){
          window.location.replace(fromSiteUrl+gsfyParam+dw_token)
        }else{
          window.location.replace(fromSiteUrl+dw_token)
        }
        return
      }
      if (!fromSiteUrl) {
        invokeNative('8002', '000', {}, data => {}) // 退出网厅
        return
      }
    }
  } catch (e) {
    // window.history.back()
    if (fromSiteUrl) {
      window.location.replace(fromSiteUrl)
      return
    }
    if (!fromSiteUrl) {
      invokeNative('8002', '000', {}, data => {}) // 退出网厅
      return
    }
  }

  // if (fromSiteUrl && fromSiteUrl.includes('http')) {
  //   window.location.replace(fromSiteUrl)
  //   return
  // }
  // if (fromSiteUrl === '') {
  //   closeYgt(0, '1A', 0, 1) // 退出网厅
  //   return
  // }
}

// 注册原生返回事件
export function registerNativeBack(registerData = {}) {
  console.log('registerNativeBack start')
  if (window.$hvue.env !== 'ths') return
  window.invokeNative('24002', '', {
    useNativeBack: '1'
  }, data => {
    console.log('registerNativeBack callback start')
    console.log(data)
    console.info('回调后重新声明: ', registerData)
    registerNativeBack(registerData.callback)
    if (registerData.callback && registerData.callback.constructor.name === 'Function') {
      registerData.callback()
    }
    console.log('registerNativeBack callback end')
  })
  console.log('registerNativeBack end')
}

/**
 * 获取min-max之间的随机数
 * @param {*} min
 * @param {*} max
 * @returns
 */
export function randomNum(min,max){

  return Math.floor(Math.random() * (max - min)) + min
}
/**
 * 获取秀财站点信息
 * @param {*} key
 * @param {*} deviceInfo
 * @returns
 */
 export function getDeviceInfoParam(key,deviceInfo) {
  var pageParam = deviceInfo
  var paramArr = pageParam.split(';')
  var paramJson = {}
  paramArr.forEach(item => {
    var flagIndex = item.indexOf('=')
    paramJson[item.substring(0, flagIndex)] = item.substring(
      flagIndex + 1,
      item.length
    )
  })
  var value = paramJson[key]
  if (value) {
    return value
  } else {
    return ''
  }
}

/**
 * 获取秀财站点信息v2
 * @param {*} key
 * @param {*} deviceInfo
 * @returns void
 */
export async function getDeviceInfoParamV2(n) {
  if (n <= 0) return
  if (window.invokeNative === undefined) {
    setTimeout(() => {
      getDeviceInfoParamV2(n - 1)
    }, 500)
  } else {
    try {
      window.invokeNative('23006', '000', {}, data => {
        console.info('getDeviceInfoParamV2 callback start', data)
        data = JSON.parse(data)
        $h.setSession('deviceInfoV2', data.tradeMediaData)
      })
    } catch (e) {
      console.error('getDeviceInfoParamV2 ERROR: ', e)
    }
  }
}

/**
 * 函数防抖
 * @param {回调函数} fn
 * @param {等待时间(ms)} time
 */
 export function debounce(fn, time) {
  let timeout = null
  return function() {
    clearTimeout(timeout)
    timeout = setTimeout(function() {
      fn.apply(arguments)
    }, time || 200)
  }
}
export const throttle = (callback, delay) => {
  let timer = null
  return (e) => {
    if (!timer) {
      timer = setTimeout(() => {
        callback(e)
        timer = null
      }, delay)
    }
  }
}


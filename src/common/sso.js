import '@/nativeShell/nativeCallH5'
import { ssoLogin } from '@/service/comServiceNew'
import { function60350, function60351, function50041, function50105, function50114, function50040 } from 'h5CallNative'
import { getUrlParam } from '@/common/util'

/**
 * 统一登录状态判断
 * @param {*} to
 * @param {*} next
 */
export function checkLogin(to, next) {
  if (to.path === '/resetPwd' || $hvue.config.noLoginBusiness.includes(to.query.type)) {
    next()
    return
  }

  let result50041 = getSSOInfo()
  // result50041TKUserKey没有手机委托方式的用户临时登入办理委托方式修改使用
  const result50041TKUserKey = function50041({ isEncrypt: '0', key: 'TKUserKey' })

  if (!result50041 || +result50041.error_no !== 0 || !result50041.results ||
    !result50041.results[0]) {
    if (to.path !== '/index') {
      _hvueAlert({
        title: '操作失败',
        mes: '统一登录信息获取失败',
        callback: () => {
          next({ path: '/index' })
        }
      })
    } else {
      next()
    }
    return
  }

  // 未登录
  if (!result50041.results[0].value && !result50041TKUserKey.results[0].value) {
    if (to.path !== '/index') {
      closeYgt(0, '1A', 1, 0)
    } else {
      next()
    }
    return
  }

  // 判断用户信息是否完整
  const ssoInfo = result50041.results[0].value
  if ((ssoInfo && ssoInfo.user_id && ssoInfo.params &&
    ssoInfo.params.mobilecode && ssoInfo.params.user_token) || result50041TKUserKey.results[0].value) {
    $h.setSession('ssoInfo', ssoInfo, { encrypt: true })
    const ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false })
    const isLogin = $h.getSession('isLogin', { decrypt: false })

    if (isLogin && !!ygtUserInfo && !!ygtUserInfo.userId &&
      ssoInfo.user_id === ygtUserInfo.fundAccount) {
      // 已做模拟登录,直接下一步
      next()
      return
    }

    $h.clearSession('ygtUserInfo')
    $h.clearSession('isLogin')
    // 去做模拟登录
    if (ssoInfo.params) {
      login(next, ssoInfo)
      return
    }
    if (result50041TKUserKey) {
      login(next, result50041TKUserKey.results[0])
      $h.setSession('isNonUnifyLogin', true, { encrypt: false })
    }
  } else {
    _hvueAlert({
      title: '操作失败',
      mes: '统一登录信息有误',
      callback: () => {
        next({ path: '/index' })
      }
    })
  }
}

// 调原生功能号获取统一登录用户信息
function getSSOInfo () {
  // 获取统一登录用户信息
  let result50041 = function50041({ isEncrypt: '0', key: 'tk_h5_account_info' })
  const maxCallNum = 10
  let i = 0

  while (i < maxCallNum && (!result50041 || result50041.error_no !== '0' ||
    !result50041.results || result50041.results.length <= 0 || !result50041.results[0].value)) {
    result50041 = function50041({ isEncrypt: '0', key: 'tk_h5_account_info' })
    i++
  }
  return result50041
}

/**
 * 网厅的模拟登陆
 * @param {*} next
 * @param {*} ssoInfo 统一登录信息
 */
export function login(next, ssoInfo) {
  const loginErrorMap = {
    '-1000199': '机构用户不能进入掌厅'
  }
  const param = ssoInfo.params ? {
    mobilecode: encodeURIComponent(ssoInfo.params.mobilecode),
    user_token: encodeURIComponent(ssoInfo.params.user_token),
    AppID: encodeURIComponent(ssoInfo.params.appid)
  } : {
    clientId: ssoInfo.value
  }
  // 调用网厅登录
  ssoLogin(param).then(
    res => {
      console.log('ssoLogin succeed:', res)
      if (res.error_no === '0') {
        // 保存用户信息
        let userInfo = res.userInfo[0] || res.results[0]
        userInfo.riskLevelDesc = (res.custTypeCheck !== undefined) ? res.custTypeCheck[0].riskLevelDesc : ''
        $h.setSession('ygtUserInfo', userInfo, { encrypt: false })
        $h.setSession('isLogin', true, { encrypt: false })

        if (next) {
          next()
        } else {
          let toPage = $h.getSession('toPage')
          if (toPage) {
            $this.$router.push({
              name: toPage.name,
              params: toPage.params,
              query: toPage.query
            })
          } else {
            $this.$router.push({
              name: 'index'
            })
          }
        }
      } else {
        const errorInfo = loginErrorMap[res.error_no]
        _hvueAlert({
          title: '操作失败',
          mes: errorInfo || res.error_info,
          callback: () => {
            // 是否是app 1-app 0或空-sdk
            function50041({ key: 'isApp' }).results[0].value === '1'
              ? function50105() // 退出网厅
              : function50114() // 退出sdk
          }
        })
      }
    },
    err => {
      console.log('ssoLogin failed:', err)
    }
  )
}

/**
 * 关闭webView
 * @param {*} isLogout 是否退出登录 1是 0否 默认否
 * @param {*} accountType 账户类型 默认1A 普通交易：“accountType” ：“1A” 两融：“accountType” ：“1B”手机号码：“accountType” ：“2C”
 * @param {*} isOpenLogin 是否打开登录窗口 1是 0否 默认否
 * @param {*} isCloseYgt 是否关闭模块，1是 0否 默认否
 */
export function closeYgt(isLogout, accountType, isOpenLogin, isCloseYgt) {
  isLogout = isLogout || 0
  accountType = accountType || '1A'
  isOpenLogin = isOpenLogin || 0
  isCloseYgt = isCloseYgt || 0

  function50040({ key: 'TKUserKey' }) // 清理sdk缓存的客户号
  // 退出操作时，清除云柜台用户缓存数据
  if (isLogout === 1) {
    $h.clearSession('ygtUserInfo')
    $h.clearSession('cardDetails')
    $h.clearSession('serivalId')
    $h.clearSession('ocrUserInfo')
    $h.clearSession('rejectList')

    function60351({}, function () { // 唤起登录
      // 是否是app 1-app 0或空-sdk
      function50041({ key: 'isApp' }).results[0].value === '1'
        ? function50105() // 退出网厅
        : function50114() // 退出sdk
    })
  }
  if (isOpenLogin === 1) {
    function60350({}, function () { // 唤起登录
      checkLogin({ path: '/index' }, () => {
        this.$router.push({
          name: 'index'
        })
      })
    })
  }
  if (isCloseYgt === 1) {
    // 是否是app 1-app 0或空-sdk
    function50041({ key: 'isApp' }).results[0].value === '1'
      ? function50105() // 退出网厅
      : function50114() // 退出sdk
  }
}

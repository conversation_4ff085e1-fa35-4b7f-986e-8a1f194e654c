// jsBridge初始化
export function setupWebViewJavascriptBridge (callback) {
  if (window.WebViewJavascriptBridge !== undefined) {
    callback(window.WebViewJavascriptBridge)
    return
  }
  if (window.WVJBCallbacks !== undefined) {
    window.WVJBCallbacks.push(callback)
    return
  }
  document.addEventListener('WebViewJavascriptBridgeReady', event => {
    window._bridge = event.bridge
    callback(window.WebViewJavascriptBridge)
  }, false)
  const timout = window.setTimeout(function () {
    if (window._bridge != null) {
      window.clearTimeout(timout)
      return
    }
    window.WVJBCallbacks = [callback]
    const WVJBIframe = document.createElement('iframe')
    WVJBIframe.style.display = 'none'
    WVJBIframe.src = 'https://__bridge_loaded__'
    document.documentElement.appendChild(WVJBIframe)
    setTimeout(function () {
      document.documentElement.removeChild(WVJBIframe)
    }, 0)
  }, 1000)
}

// 检测jsBridge是否初始化成功
export async function checkBridgeReady () {
  return new Promise((resolve, reject) => {
    console.log('进入checkBridgeReady方法')
    console.log(window._bridge)
    if (window._bridge != null) {
      // 成功返回_bridge函数
      resolve(window._bridge)
      return
    }
    // 不成功时进行jsBridge初始化
    setupWebViewJavascriptBridge((bridge) => {
      window._bridge = bridge
      if (bridge.init !== undefined) {
        bridge.init(function (message, responseCallback) {
          responseCallback('""')
          resolve(bridge)
        })
      }
      if (window._bridge != null) {
        resolve(window._bridge)
      } else {
        reject(new Error(''))
      }
    })
  })
}

// h5调用原生
export async function runInvokeNative ({
  dest, perms = '', params, fallback
}) {
  return new Promise((resolve, reject) => {
    console.log('进入runInvokeNative方法')
    checkBridgeReady()
      .then(bridge => {
        console.log('进入runInvokeNative => then方法')
        console.log(window.handlerName)
        console.log(window.functionNativeWillCall)
        if (window.handlerName !== undefined && window.functionNativeWillCall !== undefined) {
          bridge.registerHandler(window.handlerName, window.functionNativeWillCall)
        }
        bridge.callHandler('jsSkipModel', {
          destination: dest,
          pcode: perms,
          source: 'h5',
          paraDict: params
        }, (responseInJsonString) => {
          resolve(responseInJsonString)
        })
      }, function (reason) {
        if (typeof fallback === 'function') {
          fallback()
          return
        }
        throw reason
      })
  })
}

// 注册方法给原生调用
export function registerHandler (key, functionNativeWillCall) {
  checkBridgeReady().then((bridge) => {
    console.log('进入registerHandler方法')
    console.log(bridge)
    bridge.registerHandler(key, functionNativeWillCall)
  })
    .catch(() => {
      // do nothing
    })
}

// const { resolve } = require("core-js/fn/promise");

// special ios setup for wkwebview

function setupIOSWebViewJavascriptBridge(callback) {
  if (window.WebViewJavascriptBridge) {
    return callback(window.WebViewJavascriptBridge)
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback)
  }
  let isBridgeReady = false

  document.addEventListener(
    'WebViewJavascriptBridgeReady',
    function() {
      isBridgeReady = true
      callback(window.WebViewJavascriptBridge)
    },
    false
  )

  var timeoutId = window.setTimeout(function() {
    if (isBridgeReady) {
      window.clearTimeout(timeoutId)
      return
    }
    window.WVJBCallbacks = [callback]
    const WVJBIframe = document.createElement('iframe')
    WVJBIframe.style.display = 'none'
    WVJBIframe.src = 'https://__bridge_loaded__'
    document.documentElement.appendChild(WVJBIframe)
    setTimeout(function() {
      document.documentElement.removeChild(WVJBIframe)
    }, 0)
  }, 1000)
}

// 小程序环境不执行初始化代码
if (/miniprogram/i.test(window.navigator.userAgent) === false) {
  setupIOSWebViewJavascriptBridge(function(bridge) {
    try {
      bridge.init(function(message, responseCallback) {
        responseCallback('""')
      })
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log(e)
    }
  })
}

/**
 * @param params { funcNo, dest, pcode, paraDict, callback }
 * @returns
 */
// function dwInVokeNative(params) {
//   window.WebViewJavascriptBridge.callHandler(
//     params.funcNo,
//     {
//       destination: params.dest,
//       pcode: params.pcode,
//       source: 'h5',
//       paraDict: params.paraDict,
//     },
//     params.callback,
//   );
// }
// window.dwInVokeNative = dwInVokeNative;

/**
 * 调用原生功能的函数
 * @param {string} dest - 目标标识符
 * @param {string} pcode - 产品代码
 * @param {Object} params - 附加参数
 * @param {function} callBack - 回调函数
 */
function invokeNative(dest, pcode, params, callBack) {
  /*return new Promise((resolve, reject) => {
    if(!window.WebViewJavascriptBridge){
      _hvueAlert({
        title: '提示',
        mes: '非秀财APP内'
      })
      return
    }else{
      window.WebViewJavascriptBridge.callHandler('jsSkipModel',{
        destination:dest,
        pcode,
        source:'h5',
        paraDict:params
      },(data)=>{
        resolve(data)
      })
    }
  }) */
  console.log('调用原生入参:dest,' + dest + ',params' + JSON.stringify(params))
  if (window.WebViewJavascriptBridge === undefined) {
    console.error('WebViewJavascriptBridge is not ready')
    return
  }
  window.WebViewJavascriptBridge.callHandler(
    'jsSkipModel',
    {
      destination: dest,
      pcode,
      source: 'h5',
      paraDict: params
    },
    callBack
  )
}
window.invokeNative = invokeNative

function androidAppBack() {
  console.log('进入APPBack方法 *********')
  let backToApp = $h.getSession('backToApp')
  try {
    if (backToApp == '1') {
      invokeNative('8002', '000', {}, data => {})
    } else {
      window.history.back()
    }
  } catch (e) {
    window.history.back()
  }
  return
}
window.androidAppBack = androidAppBack

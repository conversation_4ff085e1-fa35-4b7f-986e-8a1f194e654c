import Vue from 'vue'
import Router from 'vue-router'
import {
  setTransitionName,
  setPageTitle,
  cancelRequest,
  callMessageNative,
  getPlatformValue
} from 'thinkive-hvue'

// 可以根据菜单栏目 把页面路由分组到各栏目模块中，再引入来扩展
import ISMP from './ismp'

import '@/nativeShell/nativeCallH5'

import {
  checkLogin
} from '@/common/sso'
import {goBackXcApp} from '@/common/util'
Vue.use(Router)

/*
  路由懒加载采用官方推荐的ES6 import()语法，
  webpackChunkName相同会打包成一个模块，不同则为不同模块
*/

const router = new Router({
  // 路由模式：history, hash. 默认设置为 history
  mode: 'history',
  // 采用history模式时，要设置base路径; hash模式不用设置(注释掉)
  // 环境变量ROUTER_BASE同步config.build.assetsPublicPath设置
  base: ROUTER_BASE,
  scrollBehavior(to, from, savedPosition) {
    // 页面滚动行为, 保持缓存页面的滚动位置, 否则返回页面顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return {
        x: 0,
        y: 0
      }
    }
  },
  routes: [
    {
      path: '/index',
      name: 'index',
      component: () =>
        import(/* webpackChunkName: "index" */ '@/views/index.vue'),
      meta: {
        title: '首页'
      }
    },

    {
      path: '/',
      redirect: 'index'
    },

    ...ISMP
  ]
})

router.beforeEach((to, from, next) => {
  // debugger
  // 页面切换动画
  setTransitionName()
  // h5访问或APP访问非登录业务不做登录拦截
  if ($hvue.platform === '0' || to.path === '/login' || to.path === '/resetPwd' ||
    $hvue.config.noLoginBusiness.includes(to.query.type)) {
    if(to.path === '/index'){
      if($hvue.env == 'ths'){
        goBackXcApp()
        return
      }
    }
    next()
  } else {
    // app端检查统一登录状态
    checkLogin(to, next)
  }
})

router.afterEach((route) => {
  // 页面切换更改title
  // 这里可以根据业务需求调整取title的顺序
  // 默认先取业务跳转参数query中title，再取路由元信息中title
  if (route.query.title) {
    setPageTitle(route.query.title)
  } else if (route.meta.title) {
    setPageTitle(route.meta.title)
  }
})

export default router

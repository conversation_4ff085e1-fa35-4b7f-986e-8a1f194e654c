export default [
  {
    path: '/business',
    name: 'business',
    component: () =>
            import(/* webpackChunkName: "business" */ '@/views/business.vue')
  },

  {
    path: '/businessRecord',
    name: 'businessRecord',
    component: () =>
      import(/* webpackChunkName: "businessRecord" */ '@/views/businessRecord.vue')
  },

  {
    path: '/agreementDetail',
    name: 'agreementDetail',
    component: () =>
            import(/* webpackChunkName: "agreementDetail" */ '@/views/common/agreementDetail.vue'),
    meta: {
      title: '协议详情'
    }
  },

  {
    path: '/accountDetail',
    name: 'accountDetail',
    component: () =>
            import(/* webpackChunkName: "accountDetail" */ '@/views/common/accountDetail.vue')
  },

  {
    path: '/specialInfo',
    name: 'specialInfo',
    component: () =>
            import(/* webpackChunkName: "specialInfo" */ '@/views/busComp/specialInfo.vue')
  },

  {
    path: '/login',
    name: 'login',
    component: () =>
            import(/* webpackChunkName: "login" */ '@/views/login.vue'),
    meta: {
      title: '登录'
    }
  },

  {
    path: '/userMoney',
    name: 'userMoney',
    component: () =>
            import(/* webpackChunkName: "userMoney" */ '@/views/business/userMoney'),
    meta: {
      title: '我的资金'
    }
  },

  {
    path: '/accessDetail',
    name: 'accessDetail',
    component: () =>
            import(/* webpackChunkName: "accessDetail" */ '@/views/business/accessDetail'),
    meta: {
      title: '转取转存明细'
    }
  },

  {
    path: '/dzht',
    name: 'dzht',
    component: () =>
            import(/* webpackChunkName: "dzht" */ '@/views/business/dzht'),
    meta: {
      title: '电子合同',
      keepAlive: true
    }
  },

  {
    path: '/error',
    name: 'error',
    component: () =>
            import(/* webpackChunkName: "error" */ '@/views/common/error.vue'),
    meta: {
      title: '提示'
    }
  },

  {
    path: '/showProtocol',
    name: 'showProtocol',
    component: () =>
            import(/* webpackChunkName: "showProtocol" */ '@/components/showProtocol.vue'),
    meta: {
      title: '协议详情'
    }
  },

  {
    path: '/closeAccount',
    name: 'closeAccount',
    component: () =>
            import(/* webpackChunkName: "dzht" */ '@/views/business/closeAccount'),
    meta: {
      title: '预约销户',
      keepAlive: true
    }
  }

]

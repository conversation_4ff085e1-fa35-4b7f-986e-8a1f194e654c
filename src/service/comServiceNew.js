// 请求模块暴露三个方法：
// request： ajax请求唯一方法；
// cancelRequest: 取消所有正在进行的请求，一般在切换路由或关闭页面时调用；
// cancelRequestUrl： 取消单个正在进行的请求.
import { request } from 'thinkive-hvue'
import p from 'platform'
import { decodeBase64 } from 'thinkive-hvue/lib/endecryptUtils'

function getPlatform(platform) {
  // platform 0-浏览器 1-Android 2-ios
  switch (platform) {
    case '0':
      if ($hvue.iBrowser.android) {
        // return $hvue.env=='ths'?'3':'5'
        return '5'
      }
      if ($hvue.iBrowser.ios) {
        // return $hvue.env=='ths'?'4':'6'
        return '6'
      }
      return '1' // H5
    case '1':
      return '3' // Android APP
    case '2':
      return '4' // iOS APP
    default:
      return '1'
  }
}

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {*} params 接口请求参数
 * @param {*} options 请求控制参数 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
export function commonService(funcNo, params, options = {}) {
  let sms_check = ['1050004000', '950101']
  // 默认控制参数
  let _options = {
    loading: false, // 是否显示等待层
    sign: false, // 是否加签名
    signModule: 'ssoSignNew', //  签名参数模块(对象)
    protocol: 'http', // 请求协议，http or websocket. 默认http
    // url: !sms_check.includes(funcNo)? SERVER_URL.YGT_NEW_SERVER:SERVER_URL.YGT_NEW_SERVER_SERVLET_API, // 请求地址
    url: SERVER_URL.YGT_NEW_SERVER, // 请求地址
    method: 'post', // 请求方式, 默认 get
    headers: {}, // 请求头
    timeout: 60000, // 超时时间
    withCredentials: true, // 是否跨域，默认为true
    encode: true, // 参数是否utf-8编码, 默认true
    global: false, // 是否全局请求，是则页面切换不会被取消
    servletVersion: '1.0', // 后端接口版本
    nativeProxy: false, // 是否走原生代理请求, 默认false
    nativeProxyMode: 0 // 原生代理 请求模式，对应50118插件号的入参:mode
  }
  const opSource = getPlatform($hvue.platform) // 0-浏览器 1-Android 2-ios
  let opStation = 'h5'
  let LIP = $h.getSession('LIP') ? $h.getSession('LIP') : 'NA'
  let MAC = $h.getSession('MAC') ? $h.getSession('MAC') : 'NA'
  if (opSource === '1' || opSource === '5' || opSource === '6') {
    _options.sign = false
    if ($hvue.iBrowser.android) {
      opStation = `MA;IIP=NA;IPORT=NA;LIP=${LIP};MAC=${MAC};IMEI=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=NA;IMSI=NA;@NA1`
    } else if ($hvue.iBrowser.ios) {
      opStation =
        'MI;IIP=NA;IPORT=NA;LIP=NA;MAC=${MAC};IDFV=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=NA;IMSI=NA;@NA'
    }
  } else {
    if ($hvue.iBrowser.android) {
      opStation =
        'MA;IIP=NA;IPORT=NA;LIP=NA;MAC=NA;IMEI=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=NA;IMSI=NA;@NA'
    } else if ($hvue.iBrowser.ios) {
      opStation =
        'MI;IIP=NA;IPORT=NA;LIP=NA;MAC=NA;IDFV=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=NA;IMSI=NA;@NA'
    }

    if (opSource === '3') {
      opStation = 'MA;'
    } else if (opSource === '4') {
      opStation = 'MI;'
    }
    // 调用壳子方法获取设备终端信息
    const res = $h.callMessageNative({
      funcNo: '50001',
      moduleName: 'open' // 必须为open
    })
    const result50001 = res.results[0]
    const result50041DeviceInfo = $h.callMessageNative({
      funcNo: '50041',
      key: 'TKDeviceKey'
    })
    console.log('result50041DeviceInfo', result50041DeviceInfo)
    const result50041 = $h.callMessageNative({
      funcNo: '50041',
      key: 'activePhone'
    })
    const ZZDeviceInfo = result50041DeviceInfo.results[0].value // 中焯设备信息
    const IIP = result50001.deviceIP || 'NA'
    const LIP = result50001.deviceLIP || 'NA'
    const MAC =
      ZZDeviceInfo.phonemac ||
      (opSource === '3'
        ? result50001.deviceMAC
          ? result50001.deviceMAC.replace(/:/g, '').replace(/-/g, '')
          : 'NA'
        : 'NA') ||
      'NA'
    const IMEI = ZZDeviceInfo.imei || result50001.deviceIMEI || 'NA'
    const IDFV = ZZDeviceInfo.idfv || result50001.deviceID || 'NA'
    const RMPN = ZZDeviceInfo.mobilecode || result50041.results[0].value || 'NA'
    const UMPN = ZZDeviceInfo.realmobilecode || 'NA'
    const OSV = ZZDeviceInfo.mobilekind || result50001.deviceSysVersion || 'NA'
    const IMSI = ZZDeviceInfo.imsi || result50001.deviceIMSI || 'NA'
    const softInfo =
      (result50001.softName || 'NA') + (result50001.softVersion || 'NA')

    opStation += `IIP=${IIP};IPORT=NA;LIP=${LIP};MAC=${MAC}`
    if (opSource === '3') {
      // Android
      opStation += `;IMEI=${IMEI}`
    }
    if (opSource === '4') {
      // iOS
      opStation += `;IDFV=${IDFV}`
    }
    opStation += `;RMPN=${RMPN};UMPN=${UMPN};ICCID=NA;OSV=${OSV};IMSI=${IMSI};@${softInfo.replace(
      'NANA',
      'NA'
    )};ygt`
  }

  const isUserLogin =
    $h.getSession('ygtUserInfo', { decrypt: false }) &&
    $h.getSession('ygtUserInfo', { decrypt: false }).userId &&
    $h.getSession('isLogin', { decrypt: false })
  const isNonUnifyLogin = $h.getSession('isNonUnifyLogin', { decrypt: false })

  // 接口公共入参
  // 客户号单独登录时isUserLogin传2
  let _params = {
    funcNo: funcNo,
    opSource: opSource,
    // opStation: opStation,
    isUserLogin: isUserLogin ? (isNonUnifyLogin ? '2' : '1') : '0'
  }
  /**
   * 20240905 东吴秀才 终端信息获取优化 start
   * 需求： 从秀才APP调用23006功能号获取终端信息，返回结果中包含设备信息。如果获取不到，由h5获取手机号，系统类型信息填充到终端信息。
   */
  const ygtUserInfo = $h.getSession('ygtUserInfo', { decrypt: false }) || {}
  const deviceInfoV2 = $h.getSession('deviceInfoV2') || undefined
  const deviceInfo = $h.getSession('deviceInfo') || {
    ICCID: '',
    IIP: '',
    IMEI: '',
    IMSI: '',
    IPORT: '',
    LIP: '',
    MAC: '',
    OSV: `${p.os.family}${p.os.version}`,
    RMPN: ygtUserInfo.mobile || '',
    TYPE: '',
    UMPN: ''
  }
  if ($hvue.iBrowser.android) {
    _params.opSource = '3'
  } else if ($hvue.iBrowser.ios) {
    _params.opSource = '4'
  }
  if (deviceInfoV2) {
    _params.opStation = deviceInfoV2
  } else {
    _params = { ..._params, ...deviceInfo }
  }
  console.info('_params: ', _params)
  /* 20240905 东吴秀才 终端信息获取优化 end */

  return new Promise((resolve, reject) => {
    // 如果完全默认控制参数，options, _options 不必传, 如：
    // request({_params, params})
    // options处理
    options = options || {}
    let loading = options.loading == null ? true : options.loading // 默认显示等待层
    if (document.getElementsByClassName('hui-dialog-white-mask').length === 0) {
      _hvueLoading.openLoading('加载中')
      // _hvueLoading.open("加载中");
    }
    options.loading = false // 底层请求不显示等待层，等待层的处理在此处自己处理。底层没有是否最后一次请求的控制，等待层会加载多次
    request({
      _params,
      params,
      options,
      _options
    }).then(
      res => {
        let hideLoading = options.isLastReq == null ? true : options.isLastReq // 是否隐藏加载层。
        if (hideLoading) _hvueLoading.closeLoading()
        if (res.error_no === '0') {
          // 出参解密
          res.dsName.forEach(item => {
            res[item].forEach(subItem => {
              for (let i in subItem) {
                if (subItem[i].match(/^tk:/)) {
                  subItem[i] = decodeBase64(subItem[i].replace('tk:', ''))
                }
              }
            })
          })
        }
        resolve(res)
      },
      err => {
        reject(err)
      }
    ).catch(error => {
      reject(error)
    })
  })
}
/**
 * @desc 获取业务列表
 * @param {Object} params 业务参数
 * {
 * source 渠道 1pc 2h5 3vtm
 * }
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 * {
 *    "cache"     :{Boolean}  是否缓存请求结果 N 默认 false
 *    "url"       :{String}   接口请求地址 N 默认为webpack配置注入SERVER_URL
 *    "sign"      :{Boolean}  请求接口默认是否加签 N 默认false
 *    "encode"    :{Boolean}  请求参数是否url编码 N 默认为true
 *    "method"    :{String}   请求方法 N 默认为POST
 *    "loading"   :{Boolean}  是否显示loading N 默认为true
 * }
 */
export function businessList(params, options) {
  return commonService('1005319', params, options)
}

/**
 * 功能：登录
 * funcNo:1005300
 * {
 *    account; // 登录账号
 *    password; // 密码
 *    accountType || ""; // 账户类型
 * }
 * @param {请求参数} params
 * }
 */
export function userLogin(params, options) {
  return commonService('1005300', params, options)
}

/**
 * 功能：统一登录
 * funcNo:1000001
 * @param {请求参数} params
 * }
 */
export function ssoLogin(params, options) {
  return commonService('1000001', params, options)
}

/**
 * 功能：查询用户当前业务的表单状态-formStatus
 * funcNo:1005312
 * @param {请求参数} params
 * @param {控制参数} option 参考1005319说明
 */
export function queryUserFlowStatus(params, options) {
  return commonService('1005312', params, options)
}

/**
 * 功能：调用ocr识别并上传图片
 * funcNo:1005378
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function uploadImg(params, options) {
  return commonService('1005378', params, options)
}

/**
 * 功能：查询数据字典
 * funcNo:1005321
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getDict(params, option) {
  return commonService('1005321', params, option)
}

/**
 * 功能：查询业务协议
 * funcNo:1005006
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getProtocolByType(params, option) {
  return commonService('1005006', params, option)
}

/**
 * 功能：查询协议详情
 * funcNo:1005007
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getProtocolById(params, option) {
  return commonService('1005007', params, option)
}

/**
 * 功能：业务流水写入redis
 * funcNo:1005381
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function setSidInRedis(params, option) {
  return commonService('1005381', params, option)
}

/**
 * 功能：查询业务办理结果
 * funcNo:1005392
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryBusinessResults(params, option) {
  return commonService('1005392', params, option)
}

/**
 * 功能：查询驳回原因
 * funcNo:1005412
 * @param {请求参数} params
 *      serivalId
 * @param {控制参数} option
 */
export function queryRejectReason(params, option) {
  return commonService('1005412', params, option)
}

/**
 * 功能：获取图片验证码
 * funcNo:1005415
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getImgCode(params, option) {
  return commonService('1005415', params, option)
}

/**
 * 功能：校验图片验证码
 * funcNo:1005416
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function verifyImgCode(params, option) {
  return commonService('1005416', params, option)
}

/**
 * 功能：用户主动放弃流程
 * funcNo:1005421
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function closeFlow(params, option) {
  return commonService('1005421', params, option)
}

/**
 * 功能：查询可绑定的银行列表
 * funcNo:1005423
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryBankList(params, option) {
  return commonService('1005423', params, option)
}

/**
 * 功能：发送短信验证码
 * funcNo:1005367
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function sendMobileMsg(params, option) {
  return commonService('1005367', params, option)
}

/**
 * 功能：校验短信验证码
 * funcNo:1005368
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function checkMobileMsg(params, option) {
  return commonService('1005368', params, option)
}

/**
 * 功能：结息
 * funcNo:1005424
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function interestSettlement(params, option) {
  return commonService('1005424', params, option)
}

/**
 * 功能：查询资金信息
 * funcNo:1005434
 * @param {请求参数} params：clientId  客户号   fundAccount：资金账户   moneyType：币种 默认人民币
 * 出参：enableBalance  可用资金   fetchBalance：可取资金   marketValue：股票市值   frozenBalance：冻结资金   asset：总资产
 * @param {控制参数} option
 */
export function queryUserMoney(params, option) {
  return commonService('1005434', params, option)
}

/**
 * 功能：查询股票持仓
 * funcNo:1005435
 * @param {请求参数} params 入参：clientId  客户号   fundAccount：资金账户   branchNo：营业部编号
         出参：stockName：股票名称    stockCode：股票代码    holdAmount：股票数量
 * @param {控制参数} option
 */
export function queryStockPositions(params, option) {
  return commonService('1005435', params, option)
}

/**
 * 功能：查询转账流水
 * funcNo:1005438
 * @param {请求参数} params
 *      入参：clientId 客户号   fundAccount 资金帐号 这两个必传
 *          非 必传  bankNo 银行代码  entrustNo 委托编号  pageIndex 当前页数  pageSize 页面条数
 *      出参：stockName：股票名称    stockCode：股票代码    holdAmount：股票数量
 * @param {控制参数} option
 */
export function queryTransferList(params, option) {
  return commonService('1005438', params, option)
}

/**
 * 功能：提交知识测评答案（除新三板业务）
 * funcNo:1005439
 * @param {请求参数} params
 *      入参：clientId 客户号   fundAccount 资金帐号 这两个必传
 *      出参：
 * @param {控制参数} option
 */
export function submitKnowledge(params, option) {
  return commonService('1005439', params, option)
}

/**
 * 功能：提交新三板知识测评答案
 * funcNo:1005450
 * @param {请求参数} params
 *      入参：必传 clientId 客户号   fundAccount 资金帐号
 *      出参：
 * @param {控制参数} option
 */
export function submitXsbKnowledge(params, option) {
  return commonService('1005450', params, option)
}

/**
 * 功能：电子合同列表查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryDzhtByProduct(params, option) {
  return commonService('1005160', params, option)
}

/**
 * 功能：电子合同详情查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryDzhtDetail(params, option) {
  return commonService('1005161', params, option)
}

/**
 * 功能：电子合同产品查询
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryProduct(params, option) {
  return commonService('1005164', params, option)
}

/**
 * 功能：重置密码-身份证上传
 * funcNo: ********
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function resetPwdUploadIDCard(params, option) {
  return commonService('********', params, option)
}

/**
 * 功能：查询销户预约结果
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getXhyyResult(params, option) {
  return commonService('1005514', params, option)
}

/**
 * 功能：检查用户类型    #result[custTypeCheck][0][accountType] == '1'   code="-8800003" message="机构用户不能办理此业务，请临柜办理"
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryUserType(params, option) {
  return commonService('1005330', params, option)
}

/**
 * 功能：查询销户结果
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getCloseAccountResult(params, option) {
  return commonService('1005510', params, option)
}

/**
 * 功能：销户预约 - 结束销户预约流水
 * @param {请求参数} params mobile  手机号码
 * @param {控制参数} option
 */
export function yyxhEnd(params, option) {
  return commonService('1005518', params, option)
}

/**
 * 功能：销户 - 重新提交
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function xhResubmit(params, option) {
  return commonService('1005519', params, option)
}

/**
 * 功能：协议提交
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function signAgreement(params, option) {
  return commonService('1005021', params, option)
}

/**
 * 功能：查询在途业务
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryInProgressBusiness(params, option) {
  return commonService('1005436', params, option)
}

/**
 * 功能：查询业务办理结果
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryBusinessResult(params, option) {
  return commonService('1005430', params, option)
}
/**
 * 功能：注册登记视频
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function regUnifiedWitness(params, option) {
  return commonService('1005554', params, option)
}
/**
 * 功能：同步视频信息
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function syncWitness(params, option) {
  return commonService('1005213', params, option)
}

/**
 * 功能：极致校验注册
 * @param {请求参数} params
 * @param {控制参数} option
 */
//  export function smsRegister (params, option) {
//   return commonService('1050004', params, option)
// }
/**
 * 功能：极致校验注册
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function smsRegister(params, option) {
  return commonService('10500040', params, option)
}
/**
 * 功能：极致校验校验
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function smsCheckjz(params, option) {
  // return commonService('950101', params, option)
  return commonService('10500041', params, option)
}
/**
 * 功能：活体人脸识别
 * funcNo: 1005219
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function faceCcompare(params, option) {
  return commonService('1005219', params, option)
}

/**
 * 功能：上传视频
 * funcNo: 1005218
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function videoBase64Upload(params, option) {
  return commonService('1005218', params, option)
}
/**
 * 单项视频获取token
 * funcNo: 1005217
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function getOneWitnessToken(params, option) {
  return commonService('1005217', params, option)
}

/**
 * 功能：查询交易时间
 * funcNo:1005515
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function queryTransactionTime(params, option) {
  return commonService('1005515', params, option)
}

/**
 * 功能：提交前步骤节点不一致检测
 * funcNo:1005224
 * @param {请求参数} params
 * @param {控制参数} option
 */
export function submitBeforCheck(params, option) {
  return commonService('1005224', params, option)
}

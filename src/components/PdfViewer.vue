<template>
  <div v-if="showPdf" class="pdf_viewer_overlay">
    <div class="pdf_viewer_container">
      <div class="pdf_viewer_header">
        <h3>{{ title }}</h3>
        <button class="close_btn" @click="closePdf">×</button>
      </div>
      <div class="pdf_viewer_content">
        <div v-if="loading" class="loading">
          <p>正在加载PDF...</p>
        </div>
        <div v-else-if="error" class="error">
          <p>{{ error }}</p>
          <button @click="openInNewTab">在新窗口打开</button>
        </div>
        <div v-else class="pdf_container">
          <!-- 使用iframe预览PDF，兼容性更好 -->
          <iframe
            :src="pdfViewerUrl"
            class="pdf_iframe"
            frameborder="0"
            @load="onIframeLoad"
            @error="onIframeError"
          ></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    showPdf: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null
    }
  },
  computed: {
    pdfViewerUrl() {
      if (!this.pdfUrl) return ''

      // 检测浏览器是否支持PDF预览
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = userAgent.includes('chrome')
      const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome')
      const isFirefox = userAgent.includes('firefox')

      // 对于支持PDF预览的浏览器，直接使用PDF URL
      if (isChrome || isFirefox) {
        return this.pdfUrl
      }

      // 对于Safari或其他浏览器，使用Google Docs Viewer作为备选
      // 或者使用Mozilla的PDF.js viewer
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(this.pdfUrl)}`
    }
  },
  watch: {
    showPdf(newVal) {
      if (newVal && this.pdfUrl) {
        this.loadPdf()
      }
    },
    pdfUrl(newVal) {
      if (newVal && this.showPdf) {
        this.loadPdf()
      }
    }
  },
  methods: {
    loadPdf() {
      this.loading = true
      this.error = null

      // 简单的延迟来模拟加载过程
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    onIframeLoad() {
      this.loading = false
      this.error = null
      console.log('PDF iframe加载成功')
    },

    onIframeError() {
      this.loading = false
      this.error = '无法加载PDF文件，请检查网络连接或文件是否存在'
      console.error('PDF iframe加载失败')
    },

    closePdf() {
      this.$emit('close')
    },

    openInNewTab() {
      window.open(this.pdfUrl, '_blank')
    }
  }
}
</script>

<style scoped>
.pdf_viewer_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf_viewer_container {
  width: 90%;
  height: 90%;
  max-width: 800px;
  background: #ffffff;
  border-radius: 0.08rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pdf_viewer_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.16rem 0.2rem;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.pdf_viewer_header h3 {
  margin: 0;
  font-size: 0.16rem;
  font-weight: 500;
  color: #333333;
}

.close_btn {
  width: 0.3rem;
  height: 0.3rem;
  border: none;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  font-size: 0.18rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.close_btn:hover {
  background: #cc3333;
}

.pdf_viewer_content {
  flex: 1;
  overflow-y: auto;
  padding: 0.16rem;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.loading p,
.error p {
  font-size: 0.16rem;
  color: #666666;
  margin-bottom: 0.16rem;
}

.error button {
  padding: 0.08rem 0.16rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 0.04rem;
  cursor: pointer;
}

.pdf_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.pdf_iframe {
  width: 100%;
  height: 100%;
  min-height: 4rem;
  border: none;
  border-radius: 0.04rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pdf_viewer_container {
    width: 95%;
    height: 95%;
  }

  .pdf_viewer_header {
    padding: 0.12rem 0.16rem;
  }

  .pdf_viewer_header h3 {
    font-size: 0.14rem;
  }

  .close_btn {
    width: 0.28rem;
    height: 0.28rem;
    font-size: 0.16rem;
  }
}
</style>

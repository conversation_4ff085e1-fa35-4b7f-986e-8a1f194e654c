<template>
  <div v-if="showPdf" class="pdf_viewer_wrapper">
    <div class="pdf_viewer_header">
      <h3>{{ title }}</h3>
      <button class="close_btn" @click="closePdf">×</button>
    </div>
    <div class="pdf_viewer_content">
      <div v-if="loading" class="loading">
        <p>正在加载PDF...</p>
      </div>
      <div v-else-if="error" class="error">
        <p>{{ error }}</p>
        <button @click="openInNewTab">在新窗口打开</button>
      </div>
      <div v-else class="pdf_container" :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top center' }">
        <!-- 使用iframe预览PDF，移除工具栏 -->
        <iframe
          :src="pdfViewerUrlWithoutToolbar"
          class="pdf_iframe"
          frameborder="0"
          @load="onIframeLoad"
          @error="onIframeError"
        ></iframe>
      </div>
    </div>
    <!-- 底部浮动缩放控件 -->
    <div v-if="!loading && !error" class="pdf_zoom_controls" @click.stop>
      <button
        class="zoom_control_btn"
        @click="testZoomOut"
        :disabled="zoomLevel <= 0.5"
        type="button"
      >
        -
      </button>
      <span class="zoom_level_display">{{ Math.round(zoomLevel * 100) }}%</span>
      <button
        class="zoom_control_btn"
        @click="testZoomIn"
        :disabled="zoomLevel >= 2"
        type="button"
      >
        +
      </button>
    </div>

    <!-- 临时测试按钮 -->
    <div style="position: fixed; top: 50px; left: 50px; z-index: 99999; background: red; padding: 10px;">
      <button @click="testZoomIn" style="background: green; color: white; padding: 5px;">测试放大</button>
      <button @click="testZoomOut" style="background: blue; color: white; padding: 5px;">测试缩小</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    showPdf: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      zoomLevel: 1 // 缩放级别，1为100%
    }
  },
  computed: {
    pdfViewerUrl() {
      if (!this.pdfUrl) return ''

      // 检测浏览器是否支持PDF预览
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = userAgent.includes('chrome')
      const isFirefox = userAgent.includes('firefox')

      // 对于支持PDF预览的浏览器，直接使用PDF URL
      if (isChrome || isFirefox) {
        return this.pdfUrl
      }

      // 对于Safari或其他浏览器，使用Mozilla的PDF.js viewer
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(this.pdfUrl)}`
    },

    pdfViewerUrlWithoutToolbar() {
      if (!this.pdfUrl) return ''

      // 检测浏览器是否支持PDF预览
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = userAgent.includes('chrome')
      const isFirefox = userAgent.includes('firefox')

      // 对于支持PDF预览的浏览器，使用内嵌模式（隐藏工具栏）
      if (isChrome || isFirefox) {
        return `${this.pdfUrl}#toolbar=0&navpanes=0&scrollbar=0`
      }

      // 对于其他浏览器，使用PDF.js viewer并隐藏工具栏
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(this.pdfUrl)}&toolbar=0&navpanes=0`
    }
  },
  watch: {
    showPdf(newVal) {
      if (newVal && this.pdfUrl) {
        this.loadPdf()
      }
    },
    pdfUrl(newVal) {
      if (newVal && this.showPdf) {
        this.loadPdf()
      }
    },
    zoomLevel(newVal, oldVal) {
      console.log('zoomLevel变化:', oldVal, '->', newVal)
      console.log('当前PDF容器transform样式:', `scale(${newVal})`)
    }
  },
  methods: {
    loadPdf() {
      this.loading = true
      this.error = null
      this.zoomLevel = 1 // 重置缩放级别

      // 简单的延迟来模拟加载过程
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    onIframeLoad() {
      this.loading = false
      this.error = null
      console.log('PDF iframe加载成功')
    },

    onIframeError() {
      this.loading = false
      this.error = '无法加载PDF文件，请检查网络连接或文件是否存在'
      console.error('PDF iframe加载失败')
    },

    closePdf() {
      this.$emit('close')
    },

    openInNewTab() {
      window.open(this.pdfUrl, '_blank')
    },

    testZoomIn() {
      console.log('🔥 测试放大按钮被点击了！')
      alert('放大按钮被点击了！当前缩放级别: ' + this.zoomLevel)
      this.zoomIn()
    },

    testZoomOut() {
      console.log('🔥 测试缩小按钮被点击了！')
      alert('缩小按钮被点击了！当前缩放级别: ' + this.zoomLevel)
      this.zoomOut()
    },

    zoomIn() {
      console.log('执行放大方法，当前级别:', this.zoomLevel)
      if (this.zoomLevel < 2) {
        const oldLevel = this.zoomLevel
        this.zoomLevel = Math.min(2, this.zoomLevel + 0.1)
        console.log('放大PDF，从', oldLevel, '到', this.zoomLevel)
        this.$forceUpdate() // 强制更新视图
      } else {
        console.log('已达到最大缩放级别')
      }
    },

    zoomOut() {
      console.log('执行缩小方法，当前级别:', this.zoomLevel)
      if (this.zoomLevel > 0.5) {
        const oldLevel = this.zoomLevel
        this.zoomLevel = Math.max(0.5, this.zoomLevel - 0.1)
        console.log('缩小PDF，从', oldLevel, '到', this.zoomLevel)
        this.$forceUpdate() // 强制更新视图
      } else {
        console.log('已达到最小缩放级别')
      }
    }
  }
}
</script>

<style scoped>
.pdf_viewer_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  z-index: 10001;
  overflow: hidden;
}

.pdf_viewer_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.12rem 0.16rem;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  min-height: 0.48rem;
}

.pdf_viewer_header h3 {
  margin: 0;
  font-size: 0.18rem;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

/* 底部浮动缩放控件 */
.pdf_zoom_controls {
  position: fixed;
  bottom: 0.16rem;
  right: 0.16rem;
  display: flex;
  align-items: center;
  gap: 0.06rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  padding: 0.06rem 0.10rem;
  border-radius: 0.20rem;
  z-index: 99999 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  pointer-events: auto !important;
  user-select: none;
}

.zoom_control_btn {
  width: 0.36rem;
  height: 0.36rem;
  border: 2px solid rgba(0, 0, 0, 0.6);
  background: transparent;
  border-radius: 50%;
  cursor: pointer !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 0.18rem;
  font-weight: 700;
  color: #000000;
  line-height: 1;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  pointer-events: auto !important;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  z-index: 99999 !important;
}

.zoom_control_btn:hover:not(:disabled) {
  background: transparent;
  border-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-1px) scale(1.05);
  color: #000000;
  text-shadow: 0 0 3px rgba(255, 255, 255, 1);
}

.zoom_control_btn:active:not(:disabled) {
  background: transparent;
  border-color: rgba(0, 0, 0, 0.9);
  transform: translateY(0) scale(0.95);
  color: #000000;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
}

.zoom_control_btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: #666666;
  border-color: rgba(0, 0, 0, 0.3);
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.zoom_level_display {
  font-size: 0.11rem;
  color: #666666;
  min-width: 0.40rem;
  text-align: center;
  font-weight: 500;
  padding: 0 0.06rem;
}

.close_btn {
  width: 0.32rem;
  height: 0.32rem;
  border: none;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  font-size: 0.18rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  margin-left: 0.12rem;
  transition: background-color 0.15s;
}

.close_btn:hover {
  background: #c82333;
}

.pdf_viewer_content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  background: #ffffff;
}

.loading p,
.error p {
  font-size: 0.16rem;
  color: #666666;
  margin-bottom: 0.16rem;
}

.error button {
  padding: 0.08rem 0.16rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 0.04rem;
  cursor: pointer;
}

.pdf_container {
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  transform-origin: top center;
  background: #ffffff;
}

.pdf_iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  display: block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pdf_viewer_wrapper {
    height: 100vh;
    border-radius: 0;
  }

  .pdf_viewer_header {
    padding: 0.10rem 0.12rem;
    min-height: 0.44rem;
  }

  .pdf_viewer_header h3 {
    font-size: 0.16rem;
  }

  .close_btn {
    width: 0.36rem;
    height: 0.36rem;
    font-size: 0.18rem;
  }

  /* 移动端浮动缩放控件优化 */
  .pdf_zoom_controls {
    bottom: 0.20rem;
    right: 0.12rem;
    padding: 0.08rem 0.12rem;
    gap: 0.08rem;
    border-radius: 0.24rem;
  }

  .zoom_control_btn {
    width: 0.44rem;
    height: 0.44rem;
    font-size: 0.20rem;
    border-width: 2.5px;
    font-weight: 800;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.9);
  }

  .zoom_control_btn:hover:not(:disabled) {
    text-shadow: 0 0 4px rgba(255, 255, 255, 1);
    transform: translateY(-1px) scale(1.08);
  }

  .zoom_level_display {
    font-size: 0.12rem;
    min-width: 0.48rem;
    padding: 0 0.08rem;
  }
}
</style>

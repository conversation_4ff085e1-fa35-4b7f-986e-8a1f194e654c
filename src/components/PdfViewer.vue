<template>
  <div>
    <!-- PDF预览主体 -->
    <div v-if="showPdf" class="pdf_viewer_wrapper">
      <div class="pdf_viewer_header">
        <h3>{{ title }}</h3>
        <button class="close_btn" @click="closePdf">×</button>
      </div>
      <div class="pdf_viewer_content">
        <div v-if="loading" class="loading">
          <p>正在加载PDF...</p>
        </div>
        <div v-else-if="error" class="error">
          <p>{{ error }}</p>
          <button @click="openInNewTab">在新窗口打开</button>
        </div>
        <div v-else class="pdf_container" :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top center' }">
          <!-- 使用iframe预览PDF，移除工具栏 -->
          <iframe
            :src="pdfViewerUrlWithoutToolbar"
            class="pdf_iframe"
            frameborder="0"
            @load="onIframeLoad"
            @error="onIframeError"
          ></iframe>
        </div>
      </div>
    </div>

    <!-- 独立的浮动缩放控件 -->
    <div v-if="showPdf && !loading && !error" class="pdf_zoom_controls_fixed">
      <button
        class="zoom_control_btn_fixed"
        @click="zoomOut"
        :disabled="zoomLevel <= 0.5"
        type="button"
      >
        -
      </button>
      <span class="zoom_level_display_fixed">{{ Math.round(zoomLevel * 100) }}%</span>
      <button
        class="zoom_control_btn_fixed"
        @click="zoomIn"
        :disabled="zoomLevel >= 2"
        type="button"
      >
        +
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    showPdf: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      zoomLevel: 1 // 缩放级别，1为100%
    }
  },
  computed: {

    pdfViewerUrlWithoutToolbar() {
      if (!this.pdfUrl) return ''

      // 统一使用PDF.js viewer，完全隐藏所有工具栏和控件
      const params = [
        `file=${encodeURIComponent(this.pdfUrl)}`,
        'toolbar=0',           // 隐藏主工具栏
        'navpanes=0',          // 隐藏导航面板
        'scrollbar=1',         // 保留滚动条（用户需要滚动查看）
        'statusbar=0',         // 隐藏状态栏
        'messages=0',          // 隐藏消息
        'printButton=0',       // 隐藏打印按钮
        'downloadButton=0',    // 隐藏下载按钮
        'viewBookmark=0',      // 隐藏书签视图
        'openFile=0',          // 隐藏打开文件
        'secondaryToolbar=0',  // 隐藏二级工具栏
        'sidebar=0'            // 隐藏侧边栏
      ].join('&')

      return `https://mozilla.github.io/pdf.js/web/viewer.html?${params}`
    }
  },
  watch: {
    showPdf(newVal) {
      if (newVal && this.pdfUrl) {
        this.loadPdf()
      }
    },
    pdfUrl(newVal) {
      if (newVal && this.showPdf) {
        this.loadPdf()
      }
    },
    zoomLevel(newVal, oldVal) {
      console.log('📊 zoomLevel数据变化:', oldVal, '->', newVal)
      console.log('🎯 PDF容器应用的transform:', `scale(${newVal})`)
    }
  },
  methods: {
    loadPdf() {
      this.loading = true
      this.error = null
      this.zoomLevel = 1 // 重置缩放级别

      // 简单的延迟来模拟加载过程
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    onIframeLoad() {
      this.loading = false
      this.error = null
    },

    onIframeError() {
      this.loading = false
      this.error = '无法加载PDF文件，请检查网络连接或文件是否存在'
    },

    closePdf() {
      this.$emit('close')
    },

    openInNewTab() {
      window.open(this.pdfUrl, '_blank')
    },

    zoomIn() {
      console.log('🔥 zoomIn方法被调用了！当前级别:', this.zoomLevel)
      if (this.zoomLevel < 2) {
        this.zoomLevel = Math.min(2, this.zoomLevel + 0.1)
        console.log('✅ 放大成功，新级别:', this.zoomLevel)
      } else {
        console.log('⚠️ 已达到最大缩放级别')
      }
    },

    zoomOut() {
      console.log('🔥 zoomOut方法被调用了！当前级别:', this.zoomLevel)
      if (this.zoomLevel > 0.5) {
        this.zoomLevel = Math.max(0.5, this.zoomLevel - 0.1)
        console.log('✅ 缩小成功，新级别:', this.zoomLevel)
      } else {
        console.log('⚠️ 已达到最小缩放级别')
      }
    }
  }
}
</script>

<style scoped>
.pdf_viewer_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  z-index: 10001;
  overflow: hidden;
}

.pdf_viewer_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.12rem 0.16rem;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  min-height: 0.48rem;
}

.pdf_viewer_header h3 {
  margin: 0;
  font-size: 0.18rem;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

/* 底部浮动缩放控件 */
.pdf_zoom_controls {
  position: fixed;
  bottom: 0.16rem;
  right: 0.16rem;
  display: flex;
  align-items: center;
  gap: 0.06rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  padding: 0.06rem 0.10rem;
  border-radius: 0.20rem;
  z-index: 99999 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  pointer-events: auto !important;
  user-select: none;
}

.zoom_control_btn {
  width: 0.36rem;
  height: 0.36rem;
  border: 2px solid rgba(0, 0, 0, 0.6);
  background: transparent;
  border-radius: 50%;
  cursor: pointer !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 0.18rem;
  font-weight: 700;
  color: #000000;
  line-height: 1;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  pointer-events: auto !important;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  z-index: 99999 !important;
}

.zoom_control_btn:hover:not(:disabled) {
  background: transparent;
  border-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-1px) scale(1.05);
  color: #000000;
  text-shadow: 0 0 3px rgba(255, 255, 255, 1);
}

.zoom_control_btn:active:not(:disabled) {
  background: transparent;
  border-color: rgba(0, 0, 0, 0.9);
  transform: translateY(0) scale(0.95);
  color: #000000;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
}

.zoom_control_btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: #666666;
  border-color: rgba(0, 0, 0, 0.3);
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.zoom_level_display {
  font-size: 0.11rem;
  color: #666666;
  min-width: 0.40rem;
  text-align: center;
  font-weight: 500;
  padding: 0 0.06rem;
}

/* 新的独立浮动缩放控件 */
.pdf_zoom_controls_fixed {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  padding: 8px 12px;
  border-radius: 20px;
  z-index: 999999 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(0, 0, 0, 0.1);
  pointer-events: auto !important;
  user-select: none;
}

.zoom_control_btn_fixed {
  width: 36px !important;
  height: 36px !important;
  border: 2px solid rgba(0, 0, 0, 0.6) !important;
  background: transparent !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #000000 !important;
  line-height: 1 !important;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8) !important;
  pointer-events: auto !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  position: relative !important;
  z-index: 999999 !important;
}

.zoom_control_btn_fixed:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.05) !important;
  border-color: rgba(0, 0, 0, 0.8) !important;
  transform: translateY(-1px) scale(1.05) !important;
  color: #000000 !important;
  text-shadow: 0 0 3px rgba(255, 255, 255, 1) !important;
}

.zoom_control_btn_fixed:active:not(:disabled) {
  background: rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.9) !important;
  transform: translateY(0) scale(0.95) !important;
  color: #000000 !important;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8) !important;
}

.zoom_control_btn_fixed:disabled {
  opacity: 0.4 !important;
  cursor: not-allowed !important;
  color: #666666 !important;
  border-color: rgba(0, 0, 0, 0.3) !important;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.5) !important;
}

.zoom_level_display_fixed {
  font-size: 12px !important;
  color: #666666 !important;
  min-width: 40px !important;
  text-align: center !important;
  font-weight: 500 !important;
  padding: 0 6px !important;
}

.close_btn {
  width: 0.32rem;
  height: 0.32rem;
  border: none;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  font-size: 0.18rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  margin-left: 0.12rem;
  transition: background-color 0.15s;
}

.close_btn:hover {
  background: #c82333;
}

.pdf_viewer_content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  background: #ffffff;
}

.loading p,
.error p {
  font-size: 0.16rem;
  color: #666666;
  margin-bottom: 0.16rem;
}

.error button {
  padding: 0.08rem 0.16rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 0.04rem;
  cursor: pointer;
}

.pdf_container {
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  transform-origin: top center;
  background: #ffffff;
}

.pdf_iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  display: block;
  pointer-events: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pdf_viewer_wrapper {
    height: 100vh;
    border-radius: 0;
  }

  .pdf_viewer_header {
    padding: 0.10rem 0.12rem;
    min-height: 0.44rem;
  }

  .pdf_viewer_header h3 {
    font-size: 0.16rem;
  }

  .close_btn {
    width: 0.36rem;
    height: 0.36rem;
    font-size: 0.18rem;
  }

  /* 移动端浮动缩放控件优化 */
  .pdf_zoom_controls {
    bottom: 0.20rem;
    right: 0.12rem;
    padding: 0.08rem 0.12rem;
    gap: 0.08rem;
    border-radius: 0.24rem;
  }

  .zoom_control_btn {
    width: 0.44rem;
    height: 0.44rem;
    font-size: 0.20rem;
    border-width: 2.5px;
    font-weight: 800;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.9);
  }

  .zoom_control_btn:hover:not(:disabled) {
    text-shadow: 0 0 4px rgba(255, 255, 255, 1);
    transform: translateY(-1px) scale(1.08);
  }

  .zoom_level_display {
    font-size: 0.12rem;
    min-width: 0.48rem;
    padding: 0 0.08rem;
  }
}
</style>

<template>
  <div v-if="showPdf" class="pdf_viewer_wrapper">
    <div class="pdf_viewer_header">
      <h3>{{ title }}</h3>
      <div class="pdf_controls">
        <button class="control_btn" @click="zoomOut" :disabled="zoomLevel <= 0.5">
          <span class="zoom_icon">-</span>
        </button>
        <span class="zoom_level">{{ Math.round(zoomLevel * 100) }}%</span>
        <button class="control_btn" @click="zoomIn" :disabled="zoomLevel >= 2">
          <span class="zoom_icon">+</span>
        </button>
      </div>
      <button class="close_btn" @click="closePdf">×</button>
    </div>
    <div class="pdf_viewer_content">
      <div v-if="loading" class="loading">
        <p>正在加载PDF...</p>
      </div>
      <div v-else-if="error" class="error">
        <p>{{ error }}</p>
        <button @click="openInNewTab">在新窗口打开</button>
      </div>
      <div v-else class="pdf_container" :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top center' }">
        <!-- 使用iframe预览PDF，移除工具栏 -->
        <iframe
          :src="pdfViewerUrlWithoutToolbar"
          class="pdf_iframe"
          frameborder="0"
          @load="onIframeLoad"
          @error="onIframeError"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    showPdf: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      zoomLevel: 1 // 缩放级别，1为100%
    }
  },
  computed: {
    pdfViewerUrl() {
      if (!this.pdfUrl) return ''

      // 检测浏览器是否支持PDF预览
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = userAgent.includes('chrome')
      const isFirefox = userAgent.includes('firefox')

      // 对于支持PDF预览的浏览器，直接使用PDF URL
      if (isChrome || isFirefox) {
        return this.pdfUrl
      }

      // 对于Safari或其他浏览器，使用Mozilla的PDF.js viewer
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(this.pdfUrl)}`
    },

    pdfViewerUrlWithoutToolbar() {
      if (!this.pdfUrl) return ''

      // 检测浏览器是否支持PDF预览
      const userAgent = navigator.userAgent.toLowerCase()
      const isChrome = userAgent.includes('chrome')
      const isFirefox = userAgent.includes('firefox')

      // 对于支持PDF预览的浏览器，使用内嵌模式（隐藏工具栏）
      if (isChrome || isFirefox) {
        return `${this.pdfUrl}#toolbar=0&navpanes=0&scrollbar=0`
      }

      // 对于其他浏览器，使用PDF.js viewer并隐藏工具栏
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(this.pdfUrl)}&toolbar=0&navpanes=0`
    }
  },
  watch: {
    showPdf(newVal) {
      if (newVal && this.pdfUrl) {
        this.loadPdf()
      }
    },
    pdfUrl(newVal) {
      if (newVal && this.showPdf) {
        this.loadPdf()
      }
    }
  },
  methods: {
    loadPdf() {
      this.loading = true
      this.error = null
      this.zoomLevel = 1 // 重置缩放级别

      // 简单的延迟来模拟加载过程
      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    onIframeLoad() {
      this.loading = false
      this.error = null
      console.log('PDF iframe加载成功')
    },

    onIframeError() {
      this.loading = false
      this.error = '无法加载PDF文件，请检查网络连接或文件是否存在'
      console.error('PDF iframe加载失败')
    },

    closePdf() {
      this.$emit('close')
    },

    openInNewTab() {
      window.open(this.pdfUrl, '_blank')
    },

    zoomIn() {
      if (this.zoomLevel < 2) {
        this.zoomLevel = Math.min(2, this.zoomLevel + 0.1)
      }
    },

    zoomOut() {
      if (this.zoomLevel > 0.5) {
        this.zoomLevel = Math.max(0.5, this.zoomLevel - 0.1)
      }
    }
  }
}
</script>

<style scoped>
.pdf_viewer_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  z-index: 10001;
  overflow: hidden;
}

.pdf_viewer_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.12rem 0.16rem;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  min-height: 0.48rem;
}

.pdf_viewer_header h3 {
  margin: 0;
  font-size: 0.18rem;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.pdf_controls {
  display: flex;
  align-items: center;
  gap: 0.06rem;
  background: #f8f9fa;
  padding: 0.04rem;
  border-radius: 0.06rem;
  border: 1px solid #e9ecef;
}

.control_btn {
  width: 0.36rem;
  height: 0.36rem;
  border: none;
  background: transparent;
  border-radius: 0.04rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s;
}

.control_btn:hover:not(:disabled) {
  background: #e9ecef;
}

.control_btn:active:not(:disabled) {
  background: #dee2e6;
}

.control_btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.zoom_icon {
  font-size: 0.20rem;
  font-weight: 600;
  color: #495057;
  line-height: 1;
}

.zoom_level {
  font-size: 0.12rem;
  color: #6c757d;
  min-width: 0.48rem;
  text-align: center;
  font-weight: 500;
  padding: 0 0.04rem;
}

.close_btn {
  width: 0.32rem;
  height: 0.32rem;
  border: none;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  font-size: 0.18rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  margin-left: 0.12rem;
  transition: background-color 0.15s;
}

.close_btn:hover {
  background: #c82333;
}

.pdf_viewer_content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  background: #ffffff;
}

.loading p,
.error p {
  font-size: 0.16rem;
  color: #666666;
  margin-bottom: 0.16rem;
}

.error button {
  padding: 0.08rem 0.16rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 0.04rem;
  cursor: pointer;
}

.pdf_container {
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  transform-origin: top center;
  background: #ffffff;
}

.pdf_iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
  display: block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pdf_viewer_wrapper {
    height: 100vh;
    border-radius: 0;
  }

  .pdf_viewer_header {
    padding: 0.10rem 0.12rem;
    min-height: 0.44rem;
  }

  .pdf_viewer_header h3 {
    font-size: 0.16rem;
  }

  .pdf_controls {
    gap: 0.04rem;
    padding: 0.03rem;
  }

  .control_btn {
    width: 0.32rem;
    height: 0.32rem;
  }

  .zoom_icon {
    font-size: 0.18rem;
  }

  .zoom_level {
    font-size: 0.11rem;
    min-width: 0.44rem;
  }

  .close_btn {
    width: 0.30rem;
    height: 0.30rem;
    font-size: 0.16rem;
    margin-left: 0.08rem;
  }
}
</style>

<template>
  <div>
    <!-- PDF预览主体 -->
    <div v-if="showPdf" class="pdf_viewer_wrapper">
      <div class="pdf_viewer_header">
        <h3>{{ title }}</h3>
        <div class="header_controls">
          <!-- 缩放控件 -->
          <div v-if="!loading && !error" class="header_zoom_controls">
            <button
              class="header_zoom_btn"
              @click="zoomOut"
              @touchstart.prevent="zoomOut"
              :disabled="zoomLevel <= 0.5"
              type="button"
            >
              -
            </button>
            <span class="header_zoom_display">{{ Math.round(zoomLevel * 100) }}%</span>
            <button
              class="header_zoom_btn"
              @click="zoomIn"
              @touchstart.prevent="zoomIn"
              :disabled="zoomLevel >= 2"
              type="button"
            >
              +
            </button>
          </div>
          <!-- 关闭按钮 -->
          <button class="close_btn" @click="closePdf">×</button>
        </div>
      </div>
      <div class="pdf_viewer_content">
        <div v-if="loading" class="loading">
          <p>正在加载PDF...</p>
        </div>
        <div v-else-if="error" class="error">
          <p>{{ error }}</p>
          <button @click="openInNewTab">在新窗口打开</button>
        </div>
        <div v-else class="pdf_container" :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }">
          <!-- 使用iframe预览PDF，移除工具栏 -->
          <iframe
            :src="pdfViewerUrlWithoutToolbar"
            class="pdf_iframe"
            frameborder="0"
            @load="onIframeLoad"
            @error="onIframeError"
          ></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    showPdf: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF预览'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      zoomLevel: 1 // 缩放级别，1为100%
    }
  },
  computed: {
    pdfViewerUrlWithoutToolbar() {
      if (!this.pdfUrl) return ''

      const baseUrl = '/static/pdfjs/web/viewer.html'
      const params = new URLSearchParams({
        file: this.pdfUrl
      })

      return `${baseUrl}?${params.toString()}#toolbar=0&navpanes=0&scrollbar=1`
    }
  },
  watch: {
    showPdf(newVal) {
      if (newVal && this.pdfUrl) {
        this.loadPdf()
      }
    },
    pdfUrl(newVal) {
      if (newVal && this.showPdf) {
        this.loadPdf()
      }
    }
  },
  methods: {
    loadPdf() {
      this.loading = true
      this.error = null
      this.zoomLevel = 1
    },

    onIframeLoad() {
      this.loading = false
      this.error = null
    },

    onIframeError() {
      this.loading = false
      this.error = '无法加载PDF文件，请检查网络连接或文件是否存在'
    },

    closePdf() {
      this.$emit('close')
    },

    openInNewTab() {
      window.open(this.pdfUrl, '_blank')
    },

    zoomIn() {
      if (this.zoomLevel < 2) {
        this.zoomLevel = Math.min(2, this.zoomLevel + 0.1)
      }
    },

    zoomOut() {
      if (this.zoomLevel > 0.5) {
        this.zoomLevel = Math.max(0.5, this.zoomLevel - 0.1)
      }
    }
  }
}
</script>

<style scoped>
.pdf_viewer_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  z-index: 10001;
  overflow: hidden;
}

.pdf_viewer_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.12rem 0.16rem;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  min-height: 0.48rem;
}

.pdf_viewer_header h3 {
  margin: 0;
  font-size: 0.18rem;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.header_controls {
  display: flex;
  align-items: center;
  gap: 0.12rem;
}

.header_zoom_controls {
  display: flex;
  align-items: center;
  gap: 0.06rem;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.04rem 0.08rem;
  border-radius: 0.16rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.header_zoom_btn {
  width: 0.28rem;
  height: 0.28rem;
  border: 1.5px solid rgba(0, 0, 0, 0.6);
  background: transparent;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 0.14rem;
  font-weight: 700;
  color: #000000;
  line-height: 1;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.header_zoom_btn:active:not(:disabled) {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.8);
  transform: scale(0.9);
}

.header_zoom_btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: #666666;
  border-color: rgba(0, 0, 0, 0.3);
}

.header_zoom_display {
  font-size: 0.10rem;
  color: #666666;
  min-width: 0.32rem;
  text-align: center;
  font-weight: 500;
  padding: 0 0.04rem;
}

.close_btn {
  width: 0.32rem;
  height: 0.32rem;
  border: none;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  font-size: 0.18rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  margin-left: 0.12rem;
  transition: background-color 0.15s;
}

.close_btn:hover {
  background: #c82333;
}

.pdf_viewer_content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
  position: relative;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  background: #ffffff;
}

.loading p,
.error p {
  font-size: 0.16rem;
  color: #666666;
  margin-bottom: 0.16rem;
}

.error button {
  padding: 0.08rem 0.16rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 0.04rem;
  cursor: pointer;
}

.pdf_container {
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  transform-origin: top left;
  background: #ffffff;
  position: relative;
  overflow: visible;
  min-width: 100%;
  min-height: 100%;
}

.pdf_iframe {
  width: 100%;
  height: calc(100% + 40px);
  border: none;
  background: white;
  display: block;
  pointer-events: auto;
  margin-top: -40px;
  touch-action: pan-x pan-y;
  will-change: transform;
}

/* 用遮罩隐藏PDF.js工具栏 */
.pdf_container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: #ffffff;
  z-index: 10;
  pointer-events: none;
}

@media (max-width: 768px) {
  .pdf_viewer_wrapper {
    height: 100vh;
    border-radius: 0;
  }

  .pdf_viewer_header {
    padding: 0.10rem 0.12rem;
    min-height: 0.44rem;
  }

  .pdf_viewer_content {
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
    overflow: auto;
  }

  .pdf_container {
    overflow: visible;
    touch-action: manipulation;
  }

  .pdf_iframe {
    touch-action: pan-x pan-y;
    transform: translateZ(0);
  }

  .pdf_viewer_header h3 {
    font-size: 0.16rem;
  }

  .close_btn {
    width: 0.36rem;
    height: 0.36rem;
    font-size: 0.18rem;
  }

  .header_controls {
    gap: 0.08rem;
  }

  .header_zoom_controls {
    padding: 0.06rem 0.10rem;
    gap: 0.08rem;
  }

  .header_zoom_btn {
    width: 0.32rem;
    height: 0.32rem;
    font-size: 0.16rem;
    border-width: 2px;
    font-weight: 800;
  }

  .header_zoom_display {
    font-size: 0.11rem;
    min-width: 0.36rem;
    padding: 0 0.06rem;
  }


}
</style>

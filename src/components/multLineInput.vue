<!--
  @desc 多行文本的输入框，支持自动增高 减行  (只适合少量数据)
  <AUTHOR>
  @date 2019年9月25日11:29:47
-->

<template>
  <textarea
    :maxlength="maxlength"
    :value="inputStr"
    :placeholder="placeholder"
    v-bind="$attrs"
    @input="inputChangeEvent($event.target.value)"
    @focus="inputChangeEvent($event.target.value)"
    ref="multLIneInputArea"
  ></textarea>
</template>

<script>
export default {
  name: 'multLineInput',
  model: {
    prop: 'inputStr',
    event: 'change'
  },
  props: {
    inputStr: {
      // 需要双向绑定的数据
      type: String
    },
    maxlength: {
      // 最大支持输入的长度
      type: Number,
      default: 100000
    },
    placeholder: {
      // 没有输入时的提示
      type: String
    },
    maxLine: {
      // 最大行数
      type: Number,
      default: 10
    },
    minLine: {
      // 最小行数
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      curLine: 1, // 当前控件行数
      el: '' // textarea对象
    }
  },
  watch: {
    inputStr(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.inputChangeEvent(newVal)
        })
      }
    }
  },
  methods: {
    inputChangeEvent (val) {
      // input事件处理方法
      this.reSetHeight()
      this.$emit('change', val)
    },
    hasScroll () {
      // 判断是否存在滚动条
      return this.el.scrollHeight > this.el.clientHeight
    },
    reSetHeight () {
      // 通过判断是否存在滚动条动态设置高度
      this.setDefaultHeight()
      while (
        (this.hasScroll() && this.curLine < this.maxLine) ||
        this.curLine < this.minLine
      ) {
        this.curLine++
        let sty = window.getComputedStyle(this.el)
        this.el.style.height =
          this.toFloatPx(sty.height) + this.toFloatPx(sty.lineHeight) + 'px'
      }
    },
    setDefaultHeight () {
      let sty = window.getComputedStyle(this.el)
      this.el.style.height =
        this.toFloatPx(sty.lineHeight) +
        this.toFloatPx(sty.paddingTop) +
        this.toFloatPx(sty.paddingBottom) +
        'px'
      this.curLine = 1
    },
    toFloatPx (str) {
      // 将单位为px的高度字符串转化为数字
      return parseFloat(str.replace(/px/, ''))
    },
    focus () {
      this.reSetHeight()
      this.el.focus()
    },
    blur () {
      this.reSetHeight()
      this.el.blur()
    }
  },
  mounted () {
    this.el = this.$refs.multLIneInputArea
  },
  activated () {
    this.reSetHeight()
  }
}
</script>

<style scoped>
textarea {
  resize: none;
}
</style>

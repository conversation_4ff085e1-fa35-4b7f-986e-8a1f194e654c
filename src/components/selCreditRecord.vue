<template>
  <div v-if="isShow" class="page_main">
    <headComponent :headerTitle="title"></headComponent>
    <article class="content">
      <div class="record_box">
        <ul>
          <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :key="index"
            v-show="index==0"
          >
            <span class="icon_radio" :class="{ checked: selMap[index] }">无</span>
          </li>
        </ul>
      </div>
      <div class="record_box">
        <h5>
          如有，请选择不良征信记录
          <span>（可多选）</span>
        </h5>
        <ul>
          <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :key="index"
            v-show="index>0"
          >
            <span class="icon_radio" :class="{ checked: selMap[index] }">{{ d.value }}</span>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class="ui button rounded block" @click.stop="confirmClick">确定</a>
      </div>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { queryDictionary } from '@/common/util'
export default {
  name: 'selCreditRecord',
  components: {
    headComponent
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    category: {
      type: String
    },
    initData: {
      type: Array,
      default: () => []
    },
    defaultStr: {
      type: String,
      default: ''
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: true
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key'
    }
  },
  data () {
    return {
      dataList: [],
      selMap: {}
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack =  this.pageBack
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    selClick (item, index) {
      if (this.mult) {
        // 选择无的时候，取消下面的勾选，选择下面的项，取消无的勾选
        if (index == 0 && !this.selMap[index]) {
          this.selMap = Object.assign({}, {})
        } else if (index != 0) {
          this.$set(this.selMap, 0, false)
        }
        this.$set(this.selMap, index, !this.selMap[index])
      } else {
        this.$emit('selCallback', { data: item, index: index })
        this.pageBack()
      }
    },
    confirmClick () {
      let sel = []
      for (let index in this.selMap) {
        if (this.selMap[index]) {
          sel.push(this.dataList[index])
        }
      }
      if (sel.length === 0) {
        return
      }

      const key = sel.map(item => { return item.key })
      if (key.length > 1 || (key[0] && key[0] !== '0')) {
        _hvueConfirm({
          mes: '一旦具有不良诚信记录信息，则无法通过线上办理业务，后续将临柜处理',
          opts: [ {
            txt: '取消',
            color: false,
            callback: () => {}
          }, {
            txt: '确认修改',
            color: true,
            callback: () => {
              this.$emit('selCallback', sel)
              this.pageBack()
            }
          } ]
        })
        return
      }

      this.$emit('selCallback', sel)
      this.pageBack()
    },
    initDefault () {
      let that = this
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(',').forEach(c => {
          if (c === a[that.idString]) {
            that.$set(that.selMap, b, true)
          }
        })
      })
    }
  },
  created () {
    if (this.category) {
      queryDictionary({ type: this.category }, data => {
        this.dataList = this.initData.concat(data)
        this.initDefault()
      })
    } else {
      this.dataList = this.initData
      this.initDefault()
    }
  },
  destroyed () {}
}
</script>

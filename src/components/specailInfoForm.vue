<!-- 特殊信息申报 -->
<template>
  <div class="special_sb_info">
    <h5 class="title">{{title}}</h5>
    <div class="input_form" v-for="(item,index) in formRender" :key="index">
      <div class="ui field text" v-for="(relItem,relIndex) in initData" :key="relIndex">
        <label class="ui label">{{relItem.name}}</label>
        <input
          v-if="relItem.type=='input'"
          type="text"
          class="ui input"
          :ref="relItem.key"
          :placeholder="relItem.placeHolder ? relItem.placeHolder :'请输入'+relItem.name"
          :format="relItem.format"
          :itemName="relItem.name"
          @focus="focus($event)"
          @input="maxLength($event,relItem.maxLength)"
          @blur="format($event.target)"
        />
        <span v-if="relItem.unit" class="unit_span">{{relItem.unit}}</span>
        <div
          v-else-if="relItem.type=='select'"
          class="ui dropdown"
          @click.stop="showSelectBox($event,relItem.category)"
        >
          <strong
            class="default"
            type="select"
            :placeholder="relItem.placeHolder ? relItem.placeHolder :'请选择'+relItem.name"
            :ref="relItem.key"
          >{{relItem.placeHolder ? relItem.placeHolder :'请选择'+relItem.name}}</strong>
        </div>
        <div
          v-else-if="relItem.type=='radio'"
          type="radio"
          :placeholder="relItem.placeHolder ? relItem.placeHolder :'请选择'+relItem.name"
          :ref="relItem.key"
          class="radio_wrap"
        >
          <span class="icon_radio" @click.stop="checkedRadio($event)" data-value="1">是</span>
          <span class="icon_radio" @click.stop="checkedRadio($event)" data-value="0">否</span>
        </div>
      </div>
      <div class="tax_delebtn" v-show="index == formRender.length -1 && index != 0">
        <a @click.prevent="deleteForm">删除</a>
      </div>
    </div>
    <ismpPicker
      :category="category"
      @okEvent="selCallback"
      :showPicker="showSelBox"
      v-model="showSelBox"
    ></ismpPicker>
  </div>
</template>
<script>
import ismpPicker from '@/components/picker' // 选择器
export default {
  components: {
    ismpPicker
  },
  props: ['initData', 'title', 'formLeng'],
  data () {
    return {
      showSelBox: false,
      relation: '请选择',
      category: '',
      currentSelectEle: '' // 记录当前是选择哪一项
    }
  },
  model: {
    prop: 'formLeng',
    event: 'updateFormLeng'
  },
  mounted () {},
  computed: {
    formRender () {
      let array = []
      for (let index = 0; index < this.formLeng; index++) {
        array.push('0')
      }
      return array
    }
  },
  methods: {
    checkedRadio (event) {
      var currentEle = event.currentTarget
      currentEle.classList.add('checked')
      var siblings = this.siblings(currentEle)
      for (let index = 0; index < siblings.length; index++) {
        siblings[index].classList.remove('checked')
      }
    },
    siblings (elm) {
      var a = [] // 保存所有兄弟节点
      var p = elm.parentNode.children // 获取父级的所有子节点
      for (var i = 0; i < p.length; i++) {
        // 循环
        if (p[i].nodeType == 1 && p[i] != elm) {
          // 如果该节点是元素节点与不是这个节点本身
          a.push(p[i]) // 添加到兄弟节点里
        }
      }
      return a
    },

    showSelectBox (event, value) {
      this.category = value
      this.showSelBox = true
      this.currentSelectEle = event.currentTarget
    },
    selCallback (currentObj) {
      this.showSelBox = false
      if (!currentObj) {
        return
      }
      // 将值填充到页面
      var currentEle = this.currentSelectEle.querySelector('strong')
      currentEle.innerText = currentObj.value
      currentEle.setAttribute('data-value', currentObj.key)
      currentEle.classList.remove('default')
    },
    deleteForm () {
      let len = this.formLeng - 1
      len = len <= 1 ? 1 : len
      this.$emit('updateFormLeng', len)
    },
    // 获取表单数据，提供给父组件使用
    getFormData () {
      let formData = []
      for (let j = 0; j < this.formLeng; j++) {
        let formItem = {}
        for (let i = 0; i < this.initData.length; i++) {
          const element = this.initData[i]
          const elementEles = this.$refs[element.key]
          let value = ''
          // 根据输入框类型的不同来取值
          if (elementEles[j].getAttribute('type') == 'text') {
            value = elementEles[j].value
          } else if (elementEles[j].getAttribute('type') == 'select') {
            // 下拉选择
            value = elementEles[j].innerHTML
          } else if (elementEles[j].getAttribute('type') == 'radio') {
            // 单选按钮
            // value = elementEles[j].querySelector(".icon_radio.checked").getAttribute("data-value");
            value = elementEles[j].querySelector('.icon_radio.checked')
              .innerHTML
          }
          if (!value) {
            _hvueToast({ mes: elementEles[j].getAttribute('placeHolder') })
            return null
          } else if (
            elementEles[j].getAttribute('type') == 'select' ||
            elementEles[j].getAttribute('type') == 'radio'
          ) {
            // 判断是否选中
            if (
              elementEles[j].getAttribute('type') == 'radio' &&
              !elementEles[j]
                .querySelector('.icon_radio.checked')
                .getAttribute('data-value')
            ) {
              _hvueToast({ mes: elementEles[j].getAttribute('placeHolder') })
              return null
            }
            if (
              elementEles[j].getAttribute('type') == 'select' &&
              !elementEles[j].getAttribute('data-value')
            ) {
              _hvueToast({ mes: elementEles[j].getAttribute('placeHolder') })
              return null
            }
          } else {
            // 判断格式
            if (!this.format(elementEles[j], true)) {
              return null
            }
          }
          formItem[element.key] = value
        }
        formData.push(formItem)
      }
      return formData
    },
    focus (event) {
      event.target.style.color = 'black'
    },
    maxLength (event, maxLengthParam) {
      if (event.target.value.length > maxLengthParam) {
        event.target.value = event.target.value.substring(0, maxLengthParam)
      }
      // this.format(event);
    },
    // 输入框格式校验
    format (event, isShowError) {
      let format = event.getAttribute('format')
      if (!format) {
        return true
      }
      format = eval(format)
      if (!format.test(event.value)) {
        //  _hvueToast({mes:event.target.getAttribute("itemName")+"格式错误"});
        event.style.color = '#da3c3c'
        if (isShowError) {
          _hvueToast({ mes: event.getAttribute('itemName') + '格式错误' })
        }
        return false
      } else {
        event.style.color = 'black'
        return true
      }
    }
  }
}
</script>

<template>
  <div class="page_main" v-cloak>
    <headComponent :headerTitle="pageParam"></headComponent>
    <article class="content">
      <div class="xy_box">
        <div class="title">《{{name}}》</div>
        <div class="xy_cont" v-html="content"></div>
      </div>
    </article>
    <div class="bottom_check">
      <div class="rule_check">
        <span
          @click.stop="signProtocolCheck = !signProtocolCheck"
          class="icon_check"
          :class="{ checked: signProtocolCheck }"
        ></span>
        <label>已阅读并同意以上协议</label>
      </div>
      <div class="ce_btn">
        <a class="ui button block rounded" @click.stop="submit" v-throttle>开始签署</a>
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { getProtocolById } from '@/service/comServiceNew'
export default {
  props: ['pageParam'],
  components: { headComponent },
  data () {
    return {
      name: '',
      content: '',
      signProtocolCheck: false,
      agreementId: ''
    }
  },
  mounted () {
    // window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack = this.pageBack
  },
  methods: {
    getProtocol (results) {
      let param = {
        agreementId: results.agreementId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId
      }
      this.agreementId = results.agreementId
      getProtocolById(param)
        .then(data => {
          if (data.error_no === '0') {
            let results = data.results || data.DataSet
            this.name = results[0].agreeBrief
            this.content = results[0].agreeContent
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    submit () {
      if (!this.signProtocolCheck) {
        _hvueToast({ mes: '您还未勾选同意协议' })
      } else {
        // 调用父组件提交事件方法
        this.$emit('my-event', { agreementId: this.agreementId })
      }
    },
    // 返回
    pageBack () {
      this.$router.go(-1)
    }
  }
}
</script>

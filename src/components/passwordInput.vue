<template>
  <div class="ui field text pword">
    <label class="ui label">{{ inputTitle }}</label>
    <input
      v-model="_inputValue"
      class="ui input"
      :type="pwdType"
      :placeholder="_placeholder"
      :maxlength="maxLength"
      autocomplete="off"
    />
    <a
      class="txt_close"
      @click.stop="$emit('valueInput', '')"
      v-show="inputValue != ''"
    ></a>
    <a
      class="icon_eye"
      :class="{ show: pwdShow }"
      v-show="canShowPwd"
      @click.stop="pwdShow = !pwdShow"
    ></a>
  </div>
</template>

<script>
export default {
  name: 'passwordInput',
  model: {
    prop: 'inputValue',
    event: 'valueInput'
  },
  props: {
    inputValue: {
      type: String,
      default: ''
    },
    inputTitle: {
      type: String,
      default: '密码'
    },
    placeholder: {
      type: String,
      default: ''
    },
    checkType: {
      type: String,
      default: 'number'
    },
    maxLength: {
      type: Number,
      default: 6
    },
    canShowPwd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pwdShow: false
    }
  },
  computed: {
    _placeholder() {
      return this.placeholder || ('请输入' + this.inputTitle)
    },
    _inputValue: {
      get() {
        return this.inputValue
      },
      set(newVal) {
        this.$emit('valueInput', newVal)
      }
    },
    pwdType () {
      return this.pwdShow ? 'tel' : 'password'
    }
  },
  methods: {},
  mounted() {},
  destroyed() {}
}
</script>

<!-- 视频见证组件 -->
<template>
  <div v-cloak>
    <!--头部 -->
    <div v-show="pageStatus !== 3">
      <div class="witness_box">
        <h5>将由见证人员与您进行视频连线</h5>
        <p>请做好以下准备</p>
        <ul class="witness_list">
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon01.png" />
            </i>
            <p>光线充足的位置</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon02.png" />
            </i>
            <p>推荐使用WIFI网络</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon03.png" />
            </i>
            <p>周遭环境安静</p>
          </li>
          <li>
            <i class="icon">
              <img src="@/assets/images/vd_icon04.png" />
            </i>
            <p>
              视频认证时间<em>交易日 {{ videoTime }}</em>
            </p>
          </li>
        </ul>
      </div>
      <div class="ce_btn mt20">
        <a class=" p_button" @click.stop="showAgreementPopup">开始视频</a>
      </div>
    </div>
    <div v-show="pageStatus == 3">

      <div class="reject_item" v-for="(item, index) in msgInfo" :key="index">
        <div class="title">
          <h5>{{ item.rejectTpye }}</h5>
          <span class="state error">已驳回</span>
        </div>
        <div class="cont">
          <p>驳回原因：{{ item.reasons }}</p>
        </div>
      </div>

      <div class="ce_btn">
        <a class="p_button" v-show="rejectStatus === 4 || rejectStatus === 3 || rejectStatus === 2
          " @click.stop="rejectVideoQuery">重新上传证件</a>
        <a class="p_button" v-show="rejectStatus === 1" @click.stop="setIdInRedis">重新视频</a>
      </div>
    </div>

    <!-- 人脸识别协议弹窗 -->
    <div v-if="showAgreementDialog" class="face_agreement_popup">
      <div class="popup_overlay"></div>
      <div class="popup_container">
        <div class="popup_content">
          <div class="content_title">尊敬的客户：</div>
          <div class="content_text">
            我们将向您申请收集您的人脸信息进行身份验证，在此之前请仔细阅读《<span class="agreement_link"
              @click="viewAgreement">东吴证券股份有限公司人脸识别验证服务协议（线上业务办理）</span>》，如您同意协议内容，请点击“同意”。我们将按照法律法规要求，采取各种合理必要的措施保障您的个人信息安全。
          </div>
          <div class="content_text">
            如果您不同意本协议的任何内容，或者无法准确理解条款含义，请不要签署本协议。但如果您不签署本协议进行人脸识别验证将可能导致业务办理失败。
          </div>
        </div>
        <div class="popup_buttons">
          <button class="btn_disagree" @click="disagreeAgreement">不同意</button>
          <button class="btn_agree" @click="agreeAgreement">同意</button>
        </div>
      </div>
    </div>

    <!-- 不同意后的提示弹窗 -->
    <div v-if="showDisagreeDialog" class="disagree_popup">
      <div class="popup_overlay" @click="closeDisagreeDialog"></div>
      <div class="disagree_container">
        <div class="disagree_content">
          <div class="disagree_text">
            线上开户需要使用您的人脸信息进行身份验证。因您对《<span class="agreement_link"
              @click="viewAgreement">东吴证券股份有限公司人脸识别验证服务协议（线上业务办理）</span>》仍有疑问，可线下至就近营业部柜面继续办理。
          </div>
        </div>
        <div class="disagree_button">
          <button class="btn_confirm" @click="closeDisagreeDialog">我知道了</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  setSidInRedis,
  regUnifiedWitness,
  syncWitness
} from '@/service/comServiceNew'
import '@/nativeShell/nativeCallH5'
import { callMessageNative } from 'thinkive-hvue'
import {
  queryDictionary,
  queryBusiness,
  goBackXcApp,
  registerNativeBack
} from '@/common/util'
export default {
  props: ['pageParam'],
  name: 'videoPage',
  data() {
    return {
      category: 'ismp.biz_type',
      rejectInfo: {
        flag: false,
        slideDown: true,
        dataList: []
      },
      pageStatus: 0, // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
      rejectReason: '',
      rejectStatus: 0, // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
      rejectTpye: '', // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
      anychatH5AppId: '',
      ocrUserInfo: $h.getSession('ocrUserInfo') || {},
      ygtUserInfo: $h.getSession('ygtUserInfo', { decrypt: false }),
      bizType: '',
      businessCode: this.$route.query.type,
      businessName: '',
      rejectDataInfo: '',
      msgInfo: [],
      showAgreementDialog: false, // 控制协议弹窗显示
      showDisagreeDialog: false // 控制不同意后的提示弹窗
    }
  },
  computed: {
    videoTime() {
      return $hvue.config.videoTime
    }
  },
  /* async created() {
    window.androidAppBack = this.pageBack
    window.videoCallBack = this.videoCallBack
    window.imgCallBack = this.getImgCallBack
    this.rejectInfo = {
      flag: false,
      slideDown: true,
      dataList: []
    }
    this.pageStatus = 0 // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
    this.rejectReason = ''
    this.rejectStatus = 0 // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.rejectTpye = '' // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.anychatH5AppId = ''
    this.ocrUserInfo = $h.getSession('ocrUserInfo') || {}
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮

    queryBusiness(this.$route.query.type, data => {
      this.businessName = data.businessName || this.$route.query.name
    })
    // 查询视频biz_type数据字典
    queryDictionary({ type: this.category, value: this.businessCode }, data => {
      this.bizType = data.key
    })
    let infoData = this.pageParam[0]
    const data = await regUnifiedWitness({
      userName: infoData.userName,
      identityNum: infoData.identityNum,
      papersAddr: infoData.papersAddr,
      sex: infoData.sex,
      birthday: infoData.birthday,
      signOffice: infoData.signOffice,
      validityBegin: infoData.validityBegin,
      validityEnd: infoData.validityEnd,
      ethnicName: infoData.ethnicName,
      clientId: this.ygtUserInfo.clientId,
      serivalId: this.$parent.publicParam.serivalId,
      flow_name: this.businessCode
    })
    if (data.error_no == 0) {
      this.ygtUserInfo.token = data.registerVideoResult[0].token
      this.ygtUserInfo.regFlowNo = data.registerVideoResult[0].regFlowNo
      this.ygtUserInfo.bizType = data.registerVideoResult[0].bizType
      // $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })
    } else {
      _hvueAlert({ mes: data.error_info })
    }
  }, */
  async mounted() {
    // window.androidAppBack = this.pageBack
    console.log('视频见证页面注册返回方法')
    registerNativeBack({
      callback: this.pageBack
    })
    window.videoCallBack = this.videoCallBack
    window.imgCallBack = this.getImgCallBack
    this.rejectInfo = {
      flag: false,
      slideDown: true,
      dataList: []
    }
    this.pageStatus = 0 // 视频页面 0:准备页面 1：排队中 2:视频见证通过 3:视频失败
    this.rejectReason = ''
    this.rejectStatus = 0 // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.rejectTpye = '' // 视频失败状态 1：视频见证未通过 2：身份证不合规 3:身份证正面 4：身份证反面 5：公安校验不通过
    this.anychatH5AppId = ''
    this.ocrUserInfo = $h.getSession('ocrUserInfo') || {}
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮

    queryBusiness(this.$route.query.type, data => {
      this.businessName = data.businessName || this.$route.query.name
    })
    // 查询视频biz_type数据字典
    queryDictionary({ type: this.category, value: this.businessCode }, data => {
      this.bizType = data.key
    })
  },
  methods: {
    // 返回
    pageBack() {
      _hvueConfirm({
        mes: '视频认证未完成，是否返回首页？',
        opts: [
          {
            txt: '取消',
            color: false,
            callback: () => {
              console.log('视频确认弹窗 - 注册返回方法')
              registerNativeBack({
                callback: this.pageBack
              })
            }
          },
          {
            txt: '确定',
            color: true,
            callback: () => {
              // 返回首页
              $hvue.env == 'ths'
                ? goBackXcApp()
                : this.$router.push({ name: 'index' })
            }
          }
        ]
      })
    },
    // 视频结束后原生回调H5方法
    videoCallBack(msg) {
      msg['videoFlag'] = decodeURI(msg['videoFlag'])
      // 兼容iOS，主动挂断时会进回调返回20000，不做驳回展示的处理
      if (msg['videoFlag'] === '20000') {
        return
      }
      let videoFlag = JSON.parse(msg['videoFlag'])
      let msgNo = +videoFlag.msgNo
      let msgInfo = videoFlag.msgInfo || []
      if (msgNo === 0) {
        this.pageStatus = 2
        this.nextStep()
      } else {
        this.pageStatus = 3
        this.rejectStatus = 1
        this.rejectTpye = '视频认证未通过'
        // 身份正面不合规 ：idcardFace
        // 身份证反面不合规：idcardBack
        // 身份证不合规：idcard
        // 视频驳回：witness
        let rejectContent = []
        for (let i = 0; i < msgInfo.length; i++) {
          let el = msgInfo[i]
          rejectContent.push(el.rejectContent)
          let step = el.step
          switch (step) {
            case 'idcardFace':
              this.rejectTpye = '肖像面认证未通过'
              this.rejectStatus = 3
              break
            case 'idcardBack':
              this.rejectTpye = '国徽面认证未通过'
              this.rejectStatus = 4
              break
            case 'idcard':
              this.rejectTpye = '身份证认证未通过'
              this.rejectStatus = 2
              break
            case 'witness':
              this.rejectTpye = '视频认证未通过'
              this.rejectStatus = 1
              break
            default:
              this.rejectStatus = 1
              this.rejectTpye = '视频认证未通过'
              break
          }
        }
        this.rejectReason = rejectContent.join(';')
      }
    },
    async witnessCallBack(data) {
      console.info('witnessCallBack 响应参数 ==== ', data)
      let _this = this
      data = JSON.parse(data)
      if (data.code == '1') {
        // 成功
        //
        this.pageStatus = 2
        this.nextStep()
        /* const res = await syncWitness({
          regFlowNo: this.ygtUserInfo.regFlowNo
        })
        if (res.error_no == 0) {

        } else {
          _hvueAlert({ mes: res.error_info })
        } */
      } else if (data.code == '0') {
        //
        // const errorInfo = data.errorInfo
        // this.pageStatus = 3
        // this.rejectStatus = 1
        // this.rejectTpye = '视频认证未通过'
        // this.rejectReason = errorInfo

        this.pageStatus = 3
        this.rejectStatus = 1
        this.rejectTpye = '视频认证未通过'
        let errorInfo = JSON.parse(data.errorInfo)
        let msgInfo = errorInfo.msgInfo || []
        this.rejectDataInfo = msgInfo
        let rejectContent = []
        let stepArray = []
        for (let i = 0; i < msgInfo.length; i++) {
          let el = msgInfo[i]
          rejectContent.push(el.reasons)
          let step = el.step
          stepArray.push(step)

          el.reasons = el.reasons.join(';')
          switch (step) {
            case 'g_id_front_path':
              el.rejectTpye = '身份证人像面'
              break
            case 'g_id_back_path':
              el.rejectTpye = '身份证国徽面'
              break
            case 'g_both_witness_video_url':
              el.rejectTpye = '视频见证'
              break
            case 'kh_witness':
              el.rejectTpye = '其他原因'
              break
            default:
              el.rejectTpye = '视频认证未通过'
              break
          }
        }
        this.msgInfo = msgInfo
        this.rejectReason = rejectContent.join(';')
        if (
          stepArray.includes('g_id_front_path') ||
          stepArray.includes('g_id_back_path')
        ) {
          this.rejectTpye = '身份证认证未通过'
          this.rejectStatus = 2
        }
      } else if (data.code == '-1') {
        _hvueConfirm({
          mes: '当前人工坐席繁忙，是否转入自助单向视频见证？',
          opts: [
            {
              txt: '继续排队',
              color: false,
              callback: () => {
                _this.startVideo()
              }
            },
            {
              txt: '确定',
              color: true,
              callback: () => {
                _this.$emit('videoCallBack', { toOneVideo: true })
              }
            }
          ]
        })
      }
    },
    setIdInRedis() {
      const infoData = this.pageParam[0]
      let param = {
        userId: this.ygtUserInfo.userId,
        serivalId: this.$parent.publicParam.serivalId,
        businessCode: this.businessCode
      }
      setSidInRedis(param)
        .then(data => {
          if (data.error_no === '0') {
            return regUnifiedWitness({
              userName: infoData.userName,
              identityNum: infoData.identityNum,
              papersAddr: infoData.papersAddr,
              sex: infoData.sex,
              birthday: infoData.birthday,
              signOffice: infoData.signOffice,
              validityBegin: infoData.validityBegin,
              validityEnd: infoData.validityEnd,
              ethnicName: infoData.ethnicName,
              clientId: this.ygtUserInfo.clientId,
              serivalId: this.$parent.publicParam.serivalId,
              flow_name: this.businessCode
            })
          } else {
            return Promise.reject(new Error(data.error_info))
          }
        })
        .then(data => {
          if (data.error_no == 0) {
            this.ygtUserInfo.token = data.registerVideoResult[0].token
            this.ygtUserInfo.regFlowNo = data.registerVideoResult[0].regFlowNo
            this.ygtUserInfo.bizType = data.registerVideoResult[0].bizType
            // $h.setSession('ygtUserInfo', this.ygtUserInfo, { encrypt: false })
            this.startVideo()
          } else {
            return Promise.reject(new Error(data.error_info))
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    },
    startVideo() {
      let videoParam = {
        activity: 'activity',
        isShowChange: false,
        userId: this.ygtUserInfo.userId,
        custName: this.ygtUserInfo.name, // 名字
        orgNo: this.ygtUserInfo.branchNo,
        channelNo: 'ths',
        regFlowNo: this.ygtUserInfo.regFlowNo,
        token: this.ygtUserInfo.token,
        bizType: this.ygtUserInfo.bizType
      }

      invokeNative('8010', '000', videoParam, data => {
        console.log('视频回调结果', data)
        this.witnessCallBack(data)
        console.log('视频见证回调后 - 注册返回方法')
        registerNativeBack({
          callback: this.pageBack
        })
      })
    },
    // doCheck() {
    //   // 执行下一步前的校验
    //   return new Promise((resolve, reject) => {
    //     // 可以下一步
    //     resolve()
    //   })
    // },
    // /**
    //  * 请求参数打包
    //  */
    // reqParamPackage() {
    //   let params = {} // 提交需要的参数
    //   return params
    // },
    // putFormData() {
    //   let formData = {} // 需要保存的表单数据
    //   return formData
    // },
    rejectVideoQuery() {
      // 判断是否是驳回流水
      if (this.$parent.flow.currentStepInfo.flow_reject === '1') {
        this.$parent.rejectFlow('uploadIdCard,oneOrTwoVideoPage')
        return
      }
      // 驳回后回滚到上传身份证步骤
      this.$parent.rollback('uploadIdCard')
    },
    // 调用父组件的下一步事件
    nextStep() {
      // this.$parent.nextStep()
      this.$emit('videoCallBack', { regFlowNo: this.ygtUserInfo.regFlowNo })
    },
    // 显示协议弹窗
    showAgreementPopup() {
      this.showAgreementDialog = true
    },
    // 关闭协议弹窗
    closeAgreementDialog() {
      this.showAgreementDialog = false
    },
    // 查看协议详情
    viewAgreement() {
      // 这里可以跳转到协议详情页面或者打开新的弹窗显示协议内容
      _hvueAlert({
        mes: '协议详情功能待实现'
      })
    },
    // 不同意协议
    disagreeAgreement() {
      this.showAgreementDialog = false
      this.showDisagreeDialog = true
    },
    // 关闭不同意弹窗
    closeDisagreeDialog() {
      this.showDisagreeDialog = false
    },
    // 同意协议
    agreeAgreement() {
      this.showAgreementDialog = false
      // 同意协议后继续原有的认证流程
      this.setIdInRedis()
    }
  }
}
</script>
<style scoped>
/* 人脸识别协议弹窗样式 */
.face_agreement_popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.popup_overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.popup_container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  border-radius: 0.12rem 0.12rem 0 0;
  padding: 0.24rem 0.2rem 0.2rem;
  box-shadow: 0 -0.02rem 0.1rem rgba(0, 0, 0, 0.1);
}

.popup_header {
  text-align: center;
  margin-bottom: 0.2rem;
}

.header_icon {
  width: 0.6rem;
  height: 0.6rem;
  margin: 0 auto 0.12rem;
  border-radius: 50%;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header_icon img {
  width: 0.4rem;
  height: 0.4rem;
  object-fit: cover;
}

.popup_header h3 {
  font-size: 0.18rem;
  font-weight: 500;
  color: #333333;
  margin: 0;
  line-height: 0.24rem;
}

.popup_content {
  margin-bottom: 0.24rem;
}

.content_title {
  font-size: 0.16rem;
  font-weight: 500;
  color: #333333;
  margin-bottom: 0.12rem;
  line-height: 0.22rem;
}

.content_text {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #666666;
  margin-bottom: 0.12rem;
  text-align: justify;
}

.content_text:last-child {
  margin-bottom: 0;
}

.agreement_link {
  color: #007AFF;
  text-decoration: none;
  cursor: pointer;
}

.agreement_link:active {
  opacity: 0.7;
}

.popup_buttons {
  display: flex;
  gap: 0.12rem;
}

.btn_disagree,
.btn_agree {
  flex: 1;
  height: 0.44rem;
  border: none;
  border-radius: 0.06rem;
  font-size: 0.16rem;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s;
}

.btn_disagree {
  background: #f5f5f5;
  color: #666666;
}

.btn_agree {
  background: #007AFF;
  color: #ffffff;
}

.btn_disagree:active,
.btn_agree:active {
  opacity: 0.7;
}

/* 不同意后的提示弹窗样式 */
.disagree_popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
}

.disagree_container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3.0rem;
  background: #ffffff;
  border-radius: 0.12rem;
  box-shadow: 0 0.04rem 0.2rem rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.disagree_content {
  padding: 0.24rem 0.2rem 0.2rem;
}

.disagree_text {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #666666;
  text-align: justify;
}

.disagree_button {
  border-top: 1px solid #f0f0f0;
  padding: 0;
}

.btn_confirm {
  width: 100%;
  height: 0.48rem;
  border: none;
  background: #ffffff;
  font-size: 0.14rem;
  font-weight: 400;
  color: #007AFF;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn_confirm:active {
  background-color: #f8f8f8;
}
</style>

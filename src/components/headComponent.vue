<!--
    属性:
    headerTitle: 标题
    isWhite:  是否为白色背景
    事件:
    @pageBack：点击返回按钮时触发
    插槽:
    slot: 默认插槽 头部主体内容, 使用slot时，slot内容将替换头部内容
-->
<template>
  <header class="page_header header" v-show="$route.path !=='/login'">
    <slot>
      <div class="header_inner" v-bind:class="{spel : backWhite}">
        <a class="icon_back" @click.stop="pageBack" v-throttle></a>
        <h1 class="title text-center">{{headerTitle}}</h1>
      </div>
    </slot>
  </header>
</template>
<script>
import { closeYgt } from '@/common/sso'
export default {
  props: ['headerTitle', 'isWhite'],
  data () {
    return {}
  },
  methods: {
    pageBack () {
      if (this.$route.path === '/index') {
        closeYgt(0, '1A', 0, 1)
      } else {
        if (this.$parent.pageBack) {
          this.$parent.pageBack()
        } else {
          this.$parent.back()
        }
      }
    }
  },
  computed: {
    backWhite () {
      if (this.isWhite === 'true') {
        return true
      }
      return false
    }
  }
}
</script>

<!--
    属性:
    isShow: <PERSON><PERSON><PERSON>, input组件是否可见，使用 v-model 绑定
    title: 标题
    value: input文字内容
    placeholder: 默认提示内容
    checkType: input校验类型
    maxLength: 最大长度
    minLength: 最大长度
    事件:
    @onShow: 弹窗显示时触发
    @onHide: 弹窗隐藏时触发
    @onSure: 点击确定按钮时触发
-->
<template>
  <div v-if="isShow" class="page_main">
    <headComponent :headerTitle="title"></headComponent>
    <article class="content">
      <div class="user_main">
        <div class="input_form">
          <div class="ui field text">
            <label class="ui label">{{title}}</label>
            <input
              ref="inputItem"
              v-model.trim="inputVal"
              :maxlength="maxLength"
              type="text"
              class="ui input"
              :placeholder="placeholder"
            />
            <a class="txt_close" @click.stop="inputVal=''" v-show="inputVal!=''"></a>
          </div>
        </div>
        <p v-show="inputTips" class="input_tips">{{ inputTips }}</p>
        <div class="ce_btn mt20">
          <a class="ui button block rounded" @click.stop="verification">确认修改</a>
        </div>
      </div>
    </article>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  name: 'inputBox',
  components: {
    headComponent
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: '请填写'
    },
    checkType: {
      type: String,
      default: 'number'
    },
    maxLength: {
      type: Number,
      default: 6
    },
    minLength: {
      type: Number,
      default: 0
    },
    inputTips: {
      type: String
    }
  },
  data () {
    return {
      inputVal: this.value
    }
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    // 校验格式
    verification () {
      if ($h.isEmptyString(this.inputVal)) {
        _hvueToast({ mes: '请输入' + this.title })
        this.$refs.inputItem.focus()
        return
      }
      if (this.checkType === 'postcode') {
        if (!$h.isPostcode(this.inputVal)) {
          _hvueToast({ mes: '请输入6位数值的邮政编码' })
          this.$refs.inputItem.focus()
          return
        }
      } else if (this.checkType === 'number') {
        if (!$h.isNumber(this.inputVal)) {
          _hvueToast({ mes: '请输入整数格式的' + this.title })
          this.$refs.inputItem.focus()
          return
        }
      } else if (this.checkType === 'email') {
        if (!$h.isEmail(this.inputVal)) {
          _hvueToast({ mes: '请输入正确格式的' + this.title })
          this.$refs.inputItem.focus()
          return
        }
      } else if (this.checkType === 'float') {
        if (!/^\d+(\.\d{1,2})?$/.test(this.inputVal)) {
          _hvueToast({ mes: '请输入两位小数以内的' + this.title })
          this.$refs.inputItem.focus()
          return
        }
      } else if (this.checkType === 'income') {
        if (!/^\d+(\.\d{1,2})?$/.test(this.inputVal)) {
          _hvueToast({ mes: '请输入两位小数以内的' + this.title })
          this.$refs.inputItem.focus()
          return
        }
        if (parseFloat(this.inputVal) === 0) {
          _hvueToast({ mes: '年收入不能为0' })
          this.$refs.inputItem.focus()
          return
        }
      } else if (this.checkType === 'telephone') {
        if (!/^\d{4}-\d{7,8}$/.test(this.inputVal)) {
          _hvueToast({ mes: '请输入正确格式的' + this.title })
          this.$refs.inputItem.focus()
          return
        }
      }

      if (this.inputVal.length < this.minLength) {
        _hvueToast({ mes: `${this.title}不得少于${this.minLength}个字` })
        this.$refs.inputItem.focus()
        return
      }

      this.$emit('selCallback', { value: this.inputVal })
      this.pageBack()
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack =  this.pageBack
  },
  destroyed () {}
}
</script>

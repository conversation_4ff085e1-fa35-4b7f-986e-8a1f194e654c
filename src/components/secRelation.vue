<template>
  <div class="page_main">
    <headComponent v-if="isShow && !showSelBox" headerTitle="紧急联系人"></headComponent>
    <article v-if="isShow && !showSelBox" class="content">
      <div class="user_main">
        <div class="input_form">
          <div class="ui field text">
            <label class="ui label">姓名</label>
            <input
              type="text"
              class="ui input"
              placeholder="输入姓名"
              v-model.trim="thisName"
              maxlength="20"
            />
            <a class="txt_close" @click.stop="thisName = ''" v-show="thisName != ''"></a>
          </div>
          <div class="ui field text">
            <label class="ui label">关系</label>
            <div class="ui dropdown" @click.stop="showSelBoxClick">
              <strong>{{ thisRelation }}</strong>
            </div>
          </div>
          <div class="ui field text">
            <label class="ui label">手机号码</label>
            <input
              type="text"
              class="ui input"
              placeholder="输入手机号"
              v-model.trim="thisMobile"
              maxlength="11"
            />
            <a class="txt_close" @click.stop="thisMobile = ''" v-show="thisMobile != ''"></a>
          </div>
        </div>
        <div class="ce_btn mt20">
          <a class="ui button block rounded" @click.stop="verification">确认修改</a>
        </div>
      </div>
    </article>
    <selBox
      v-if="showSelBox"
      v-model="showSelBox"
      title="关系"
      category="ismp.relation"
      :defaultStr="thisRelation"
      @selCallback="selCallback"
    ></selBox>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import selBox from '@/components/selBox' // 选择器
export default {
  name: 'secRelation',
  components: {
    headComponent,
    selBox
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    },
    relation: {
      type: String,
      default: ''
    },
    relationKey: {
      type: String,
      default: ''
    },
    mobile: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      showSelBox: false,
      thisName: '',
      thisRelation: '',
      thisRelationKey: '',
      thisMobile: ''
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack =  this.pageBack
    this.thisName = this.name
    this.thisRelation = this.relation
    this.thisRelationKey = this.relationKey
    this.thisMobile = this.mobile
  },
  methods: {
    pageBack () {
      if (this.thisName !== this.name ||
        this.thisRelation !== this.relation ||
        this.thisMobile !== this.mobile) {
        _hvueConfirm({
          mes: '联系人未保存，确认退出吗？',
          opts: [ {
            txt: '取消',
            color: false,
            callback: () => {}
          }, {
            txt: '确定',
            color: true,
            callback: () => {
              this.thisName = this.name
              this.thisRelation = this.relation
              this.thisRelationKey = this.relationKey
              this.thisMobile = this.mobile
              this.$emit('change', false)
            }
          } ]
        })
        return
      }
      this.$emit('change', false)
    },
    // 校验格式
    verification () {
      if ($h.isEmptyString(this.thisName)) {
        _hvueToast({ mes: '请输入姓名' })
        return
      }
      if (!/^[\u4E00-\u9FA5]{2,14}([·•]?[\u4E00-\u9FA5]+)*$/.test(this.thisName)) {
        _hvueToast({ mes: '姓名格式不正确' })
        return
      }
      if ($h.isEmptyString(this.thisRelation)) {
        _hvueToast({ mes: '请选择关系' })
        return
      }
      if ($h.isEmptyString(this.thisMobile)) {
        _hvueToast({ mes: '请输入手机号码' })
        return
      }
      if (!/^1\d{10}$/.test(this.thisMobile)) {
        _hvueToast({ mes: '手机号码格式不正确' })
        return
      }
      if (this.thisName === $h.getSession('ygtUserInfo', {decrypt: false}).name) {
        _hvueToast({ mes: '姓名不能是本人姓名' })
        return false
      }
      if (this.thisMobile === $h.getSession('ygtUserInfo', {decrypt: false}).mobile) {
        _hvueToast({ mes: '手机号码不能是本人手机号码' })
        return false
      }

      this.$emit('selCallback', {
        name: this.thisName,
        relationKey: this.thisRelationKey,
        relation: this.thisRelation,
        mobile: this.thisMobile
      })
      this.$emit('change', false)
    },
    showSelBoxClick () {
      this.showSelBox = true
    },
    selCallback (a) {
      this.thisRelationKey = a.data.key
      this.thisRelation = a.data.value
    }
  }
}
</script>

<style>
</style>

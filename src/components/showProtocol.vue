<template>
  <div class="page_main">
    <headComponent headerTitle="协议详情"></headComponent>
    <article class="content">
      <div class="xy_box">
        <div class="title">《{{protocolName}}》</div>
        <div class="xy_cont" v-html="protocolContent"></div>
      </div>
    </article>
    <div class="bottom_check">
      <p class="tips" v-show="isShowTips">{{tips}}</p>
      <div class="ce_btn">
        <a
          v-if="showReadSeconds"
          class="ui button block rounded disable"
        >请认真阅读以上内容({{readSeconds}}s)</a>
        <a v-else class="ui button block rounded" @click.stop="confirmClick" v-throttle>{{btnDesc}}</a>
      </div>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { getProtocolById } from '@/service/comServiceNew'
export default {
  components: { headComponent },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    // 协议id
    agreementId: {
      type: String,
      default: ''
    },
    // 是否已经阅读
    isRead: {
      type: Boolean,
      default: false
    },
    // 协议最低阅读秒数
    readSeconds: {
      type: Number,
      default: 0
    },
    // 底部提示
    tips: {
      type: String,
      default: ''
    },
    // 是否展示底部提示
    isShowTips: {
      type: Boolean,
      default: false
    },
    // 协议名
    name: {
      type: String,
      default: ''
    },
    // 协议内容
    content: {
      type: String,
      default: ''
    },
    filePath: {
      type: String,
      default: ''
    },
    // 按钮描述
    btnDesc: {
      type: String,
      default: '我已阅读并同意'
    }
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  data () {
    return {
      currentPage: 1,
      startX: 0, // 鼠标开始点击的x坐标
      startY: 0,

      protocolName: this.name, // 协议标题
      protocolContent: this.content, // 协议内容
      isShowContent: true // 展示协议内容唯true 展示附件为false
    }
  },
  computed: {
    showReadSeconds () {
      if (this.readSeconds == 0) {
        return false
      } else {
        return true
      }
    }
  },
  created () {
    if (this.agreementId) {
      let param = {
        agreementId: this.agreementId,
        userId: $h.getSession('ygtUserInfo', {decrypt: false}).userId
      }
      getProtocolById(param)
        .then(data => {
          if (data.error_no === '0') {
            let results = data.results || data.DataSet
            this.protocolName = results[0].agreeBrief
            this.protocolContent = results[0].agreeContent
          } else {
            _hvueToast({ mes: data.error_info })
          }
        })
        .catch(e => {
          _hvueToast({ mes: e.message })
        })
    }
    if (this.filePath == '') {
      this.isShowContent = true
    } else {
      // 如果需要用纯H5展示pdf，直接用这里的方法，但是不可以跨域，建议在app中调用原生查看pdf的50240方法
      this.isShowContent = false
      this.currentPage = 1
    }
  },
  mounted () {
    // window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack = this.pageBack
  },
  methods: {
    // 给目标添加事件，处理兼容
    addHandler (element, type, handler) {
      if (element.addEventListener) {
        element.addEventListener(type, handler, false)
      } else if (element.attachEvent) {
        element.attachEvent('on' + type, handler)
      } else {
        element['on' + type] = handler
      }
    },
    // 具体的滑动处理
    // （此处只需要处理上滑事件，所以处理较简单，还可以进行封装，处理各种滑动事件）
    handleTouchEvent (event) {
      switch (event.type) {
        case 'touchstart':
          this.startX = event.touches[0].pageX
          this.startY = event.touches[0].pageY
          break
        case 'touchend':
          var spanX = event.changedTouches[0].pageX - this.startX
          var spanY = event.changedTouches[0].pageY - this.startY
          if (spanY < -30) { // 向上
            this.currentPage++
          }
          if (spanY > 30 && this.currentPage > 1) { // 向下
            this.currentPage--
          }
          if (Math.abs(spanX) > Math.abs(spanY)) {
            // 认定为水平方向滑动
          } else {
            // 认定为垂直方向滑动
          }
          break
        case 'touchmove':
          // 阻止默认行为
          event.preventDefault()
          break
      }
    },
    // 返回
    pageBack () {
      this.$emit('change', false)
    },
    confirmClick () {
      this.$emit('showProtocolCall')
      this.pageBack()
    }
  }
}
</script>

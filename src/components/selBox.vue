<template>
  <div class="page_main" v-if="isShow">
    <headComponent :headerTitle="title"></headComponent>
    <article class="content">
      <slot>
        <ul class="select_list">
          <li
            v-for="(d, index) in dataList"
            @click.stop="selClick(d, index)"
            :class="{ active: selMap[index] }"
            :key="index"
          >
            <span>{{ d.value }}</span>
          </li>
        </ul>
      </slot>
    </article>
    <h-popup v-model="inputerShow" position="center">
      <div class="popup_box">
        <div class="popup_header">
          <p>请填写具体{{ title }}</p>
        </div>
        <div class="popup_content popup_input_box">
          <input ref="popupInput" :class="{ wrong: inputWarning }" type="text" maxlength="10"
            :placeholder="`请填写具体${title}`" v-model.trim="detailedInfo">
        </div>
        <div class="popup_btn_box">
          <a href="javascript:;" @click="confirmDetailedInfo">确定</a>
        </div>
      </div>
    </h-popup>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { queryDictionary } from '@/common/util'
export default {
  name: 'selBox',
  components: {
    headComponent
  },
  model: {
    prop: 'isShow',
    event: 'change'
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '请选择'
    },
    category: {
      type: String
    },
    initData: {
      type: Array,
      default: () => []
    },
    defaultStr: {
      type: String,
      default: ''
    },
    mult: {
      // 是否多选
      type: Boolean,
      default: false
    },
    idString: {
      // 指定id的key
      type: String,
      default: 'key'
    }
  },
  data () {
    return {
      dataList: [],
      selMap: {},
      selected: {},
      detailedInfo: '',
      inputerShow: false,
      inputWarning: false
    }
  },
  mounted () {
    this.$store.commit('updateBusinessNextBtnStatus', false) // 隐藏下一步按钮
    window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack =  this.pageBack
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    selClick (item, index) {
      if (item.value.replace(/（.*）/, '') === '其他') {
        if (this.category === 'ismp.occupational') {
          this.selected = index
          this.inputWarning = false
          this.inputerShow = true
          return
        }
      }

      if (this.mult) {
        this.$set(this.selMap, index, !this.selMap[index])
      } else {
        this.$emit('selCallback', { data: item, index: index })
        this.pageBack()
      }
    },
    confirmDetailedInfo () {
      if (!this.detailedInfo) {
        this.inputWarning = true
        this.$refs.popupInput.focus()
        return
      }
      this.$emit('selCallback', {
        data: this.dataList[this.selected],
        index: this.selected,
        detailedInfo: this.detailedInfo
      })
      this.pageBack()
    },
    confirmClick () {
      let sel = []
      for (let index in this.selMap) {
        sel.push(this.dataList[index])
      }
      if (sel.length === 0) {
        return
      }
      this.$emit('selCallback', sel)
      this.pageBack()
    },
    initDefault () {
      let that = this
      that.dataList.forEach((a, b) => {
        that.defaultStr.split(';').forEach(c => {
          if (c === a[that.idString]) {
            that.selMap[b] = true
          }
        })
      })
    }
  },
  created () {
    if (this.category) {
      queryDictionary({ type: this.category }, data => {
        this.dataList = this.initData.concat(data)
        this.initDefault()
      })
    } else {
      this.dataList = this.initData
      this.initDefault()
    }
  },
  destroyed () {}
}
</script>

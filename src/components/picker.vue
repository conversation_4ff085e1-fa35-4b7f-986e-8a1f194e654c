<!--picker组件  基于vant Picker封装-->
<template>
  <div>
    <van-popup v-model="showModel" position="bottom" @close="onCancel">
      <van-picker show-toolbar :columns="columns" @cancel="onCancel" @confirm="onConfirm" />
    </van-popup>
  </div>
</template>
<script>
import Vue from "vue";
import { Picker, Popup } from "vant";
import "vant/lib/picker/style";
import "vant/lib/popup/style";
import { queryDictionary } from "@/common/util";
Vue.use(Popup);
Vue.use(Picker);
export default {
  props: ["type", "showPicker", "category"],
  // model: {
  //   prop: "showPicker",
  //   event: "showOrNot"
  // },
  data() {
    return {
      value: "",
      columns: [],
      columnsList: [],
      showModel : this.showPicker
    };
  },
  created () {
    if (this.category) {
       this.initData();
      }
  },
  watch: {
    category() {
      if (this.category) {
       this.initData();
      }
    },
    showPicker(){
      this.showModel = this.showPicker;
    }
  },
  methods: {
    initData(){
      queryDictionary({ type: this.category }, data => {
          this.columnsList = data;
          var dataValueList = [];
          data.forEach(item => {
            dataValueList.push(item.value);
          });
          this.columns = dataValueList;
        });
    },
    onConfirm(value, index) {
      this.showModel = false;
      this.$emit("okEvent", this.columnsList[index]);
    },
    onCancel(){
      this.showModel = false;
      this.$emit("okEvent","");
    }
  }
};
</script>
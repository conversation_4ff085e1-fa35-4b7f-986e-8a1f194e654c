<!--适当性匹配弹框组件-->
<template>
  <div>
    <headComponent headerTitle="适当性"></headComponent>

    <article class="content"></article>
    <!-- 遮罩层  -->
    <div class="ui dialog-overlay" v-show="!showProtocolParam.isShow"></div>
    <!-- 弹出层  -->
    <div class="approp_layer" v-show="!showProtocolParam.isShow">
      <div class="approp_box">
        <div class="approp_result">
          <h5>
            <i class="icon">
              <img :src="icon_src" />
            </i>您的适当性评估
            <span>{{pageParam.matchingFlag == '0' ? "匹配" : "不匹配"}}</span>
          </h5>
        </div>
        <div class="approp_info">
          <ul>
            <li>
              <span class="tit">个人风险承受能力等级</span>
              <p>{{pageParam.riskLevelDesc}}</p>
            </li>
            <li>
              <span class="tit">拟接受服务</span>
              <p>{{pageParam.businessName}}</p>
            </li>
            <!-- <li>
              <span class="tit">服务风险等级</span>
              <p>{{pageParam.approLevelDesc}}</p>
            </li>-->
            <li>
              <span class="tit">投资品种</span>
              <p>{{pageParam.investTypeDesc}}</p>
            </li>
          </ul>
        </div>
        <div class="approp_tips">
          <p>
            本人在此确认自身风险承受能力等级该金融服务风险等级
            <span
              :class="pageParam.matchingFlag == '0' ? 'ablue' : 'ared'"
            >{{pageParam.matchingFlag == '0' ? "匹配" : "不匹配"}}</span>。本人投资该项产品或接受该项服务的决定，系本人独立、自主、真实的意思表示，与贵公司及相关从业人员无关。
          </p>
        </div>
        <div class="rule_check">
          <span
            class="icon_check"
            @click.stop="signProtocolCheck = !signProtocolCheck"
            :class="{ checked: signProtocolCheck }"
          ></span>
          <label>
            已知晓并确认签署
            <a
              v-for="(item,index) in portocol"
              @click.stop="showProtocolClick(item)"
              :key="index"
            >《{{item.agreeName}}》</a>
          </label>
        </div>
      </div>
      <div class="ce_btn">
        <a
          v-if="pageParam.isStrongMatch=='0' || pageParam.matchingFlag == '0' || pageParam.minRankFlag!='0'"
          class="ui button block rounded"
          @click.stop="doSubmit"
        >确定</a>
        <a class="ui button block rounded border mt10" @click.stop="toIndex">放弃办理</a>
      </div>
    </div>
    <showProtocol
      v-if="showProtocolParam.isShow"
      v-model="showProtocolParam.isShow"
      :agreementId="showProtocolParam.agreementId"
      :isRead="showProtocolParam.isRead"
      @showProtocolCall="showProtocolCall"
    ></showProtocol>
  </div>
</template>
<script>
import okSrc from '@/assets/images/appro_ok.png'
import errorSrc from '@/assets/images/appro_error.png'
import headComponent from '@/components/headComponent' // 头部
import showProtocol from '@/components/showProtocol' // 协议详情
import { getProtocolByType } from '@/service/comServiceNew'

export default {
  props: ['pageParam'],
  name: 'sdxCheck',
  components: { showProtocol, headComponent },
  data () {
    return {
      userId: $h.getSession('ygtUserInfo').userId,
      portocol: [],
      queryValue: '',
      icon_src: '',
      signProtocolCheck: false,
      showProtocolPage: false, // 是否展示协议签署页面，当开通委托需要签署协议时展示
      showProtocolParam: {
        isShow: false, // 是否展示
        title: '', // 协议名称
        agreementId: '',
        isRead: false // 是否已阅读
      } // 协议详情组件参数
    }
  },
  methods: {
    getProtocol () {
      let params = this.$route.params
      getProtocolByType(
        { userId: this.userId, queryType: '0', queryValue: this.queryValue },
        {}
      ).then(
        res => {
          if (res.error_no === '0') {
            this.portocol = res.results
          } else {
            _hvueToast({
              icon: 'error',
              mes: res.error_info
            })
          }
        },
        err => {
          console.log(err)
        }
      )
    },
    doSubmit () {
      if (!this.signProtocolCheck) {
        _hvueToast({ mes: '请先勾选同意协议' })
        return
      }
      // 调用父组件提交事件方法
      this.$emit('my-event', {
        agreement: this.portocol,
        matchingFlag: this.pageParam.matchingFlag,
        riskLevelDesc: this.pageParam.riskLevelDesc
      })
    },

    // 展示协议详情
    showProtocolClick (agree) {
      this.showProtocolParam.isShow = true
      this.showProtocolParam.isRead = agree.isRead
      this.showProtocolParam.agreementId = agree.agreementId
    },
    // 展示协议详情返回
    showProtocolCall () {
      this.portocol.isRead = true
      this.showProtocolParam.isShow = false
    },
    toIndex () {
      this.$router.push({ name: 'index', params: {} })
    }
  },
  watch: {
    pageParam () {
      if (this.pageParam.matchingFlag === '0') {
        // 匹配
        this.icon_src = okSrc
        this.queryValue = 'sdx_match_agreement'
      } else {
        // 不匹配
        this.icon_src = errorSrc
        this.queryValue = 'sdx_mismatch_agreement'
      }
      this.getProtocol()
    }
  }
}
</script>

<template>
  <div>
    <headComponent headerTitle="测评结果"></headComponent>
    <div class="test_result">
      <div class="test_level knowledge">
        <div class="test_canvas">
          <span style="display:none;" ref="zstestData">{{scoreResult.paperScore}}</span>
          <div class="info">
            <strong>{{parseInt(scoreResult.paperScore)}}</strong>
            <span>测评得分</span>
          </div>
          <canvas ref="zstestCanvas" width="320" height="320"></canvas>
        </div>
        <h5 v-if="scoreResult.paperScore<passScore" class="error">
          抱歉！你的测评分数过低
          <br />请重新评测
        </h5>
        <h5 v-else-if="passScore > 0">恭喜你通过测评</h5>
        <slot></slot>
      </div>
    </div>
    <div class="ce_btn">
      <a
        v-if="scoreResult.paperScore<passScore"
        class="ui button block rounded"
        v-throttle
        @click.stop="pageBack"
      >重新测评</a>
      <a v-else class="ui button block rounded" @click.stop="next" v-throttle>下一步</a>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
export default {
  props: ['quesitionResult'],
  name: 'questionResult',
  components: {
    headComponent
  },
  data () {
    return {
      passScore:
        process.env.NODE_ENV == 'development'
          ? 20
          : process.env.NODE_ENV == 'test'
            ? 30
            : 80 // 通过分数 根据不同业务的情况可能会有所不同，此处应该是从后端返回告知前端多少分算通过。默认是80
    }
  },
  computed: {
    scoreResult () {
      return this.quesitionResult
    }
  },
  created () {
    this.passScore =
      this.$parent.$parent.flow.currentStepInfo.flow_current_step_name ==
      'creditInvestigation'
        ? 0
        : this.passScore
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack =  this.pageBack
    let canvas1 = this.$refs.zstestCanvas
    let ctx1 = canvas1.getContext('2d')
    let W1 = canvas1.width
    let H1 = canvas1.height
    let deg1 = 0,
      new_deg1 = 0,
      dif1 = 0
    let loop1, re_loop1
    let t_data1 = this.$refs.zstestData.innerHTML
    let deColor1 = '#ffffff',
      dotColor1 = '#B80205'

    function init1 () {
      ctx1.clearRect(0, 0, W1, H1)
      ctx1.beginPath()
      ctx1.strokeStyle = deColor1
      ctx1.lineWidth = 5
      ctx1.arc(
        W1 / 2,
        H1 / 2 + 30,
        130,
        (Math.PI * 5) / 6,
        (Math.PI * 39) / 18,
        false
      )
      ctx1.stroke()

      let r1 = (2.4 * deg1 * Math.PI) / 180
      ctx1.beginPath()
      ctx1.strokeStyle = dotColor1
      ctx1.lineWidth = 5
      ctx1.arc(
        W1 / 2,
        H1 / 2 + 30,
        130,
        (Math.PI * 5) / 6,
        r1 + (Math.PI * 5) / 6,
        false
      )
      ctx1.stroke()

      let r2 = (2.4 * deg1 * Math.PI) / 180 + (Math.PI * 1) / 3
      ctx1.beginPath()
      ctx1.fillStyle = dotColor1
      ctx1.arc(
        W1 / 2 - 130 * Math.sin(r2),
        H1 / 2 + 30 + 130 * Math.cos(r2),
        7,
        -180,
        true
      )
      ctx1.fill()
    }
    function draw1 () {
      new_deg1 = t_data1
      dif1 = new_deg1 - deg1
      loop1 = setInterval(to1, 500 / dif1)
    }
    function to1 () {
      if (deg1 == new_deg1) {
        clearInterval(loop1)
      }
      if (deg1 < new_deg1) {
        deg1++
      }
      init1()
    }
    draw1()
  },
  methods: {
    /** ********************************************子组件公共方法定义*****start******************************* */
    doCheck () {
      // 执行下一步前的校验
      return new Promise((resolve, reject) => {
        // 测评是否通过
        resolve()
      })
    },
    /**
     * 请求参数打包
     */
    reqParamPackage () {
      return {}
    },
    putFormData () {
      return {}
    },
    /** ********************************************子组件公共方法定义***end************************************* */
    next () {
      this.$parent.handleResult = false
      this.$parent.$parent.emitNextEvent()
    },
    pageBack () {
      this.$parent.pageBack()
    }
  }
}
</script>

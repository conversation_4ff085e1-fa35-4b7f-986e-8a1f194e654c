<!--
  @desc 家庭地址
  <AUTHOR>
-->
<template>
  <div class="page_main">
    <headComponent headerTitle="家庭地址"></headComponent>
    <article class="content">
      <div class="user_main">
        <div class="input_form">
          <div class="ui field text">
            <label class="ui label">省市区</label>
            <div class="ui dropdown">
              <strong @click.stop="distShow=!distShow">{{p_province}} {{p_city}} {{p_area}}</strong>
            </div>
          </div>
          <div class="ui field text">
            <label class="ui label">详细地址</label>
            <input type="text" v-model="p_detailed" class="ui input" placeholder="街道门牌信息" maxlength="120">
            <a class="txt_close" @click.stop="p_detailed=''" v-show="p_detailed!=''"></a>
          </div>
        </div>
        <div class="ce_btn mt20">
          <a class="ui button block rounded" @click.stop="verification">确认修改</a>
        </div>
      </div>
    </article>
    <!--遮罩层-->
    <div class="blacks" v-show="distShow" @click.stop="countermand"></div>
    <div class="divwrap">
      <v-distpicker
        v-show="distShow"
        :province="p_province"
        :city="p_city"
        :area="p_area"
        @province="onChangeProvince"
        @city="onChangeCity"
        @area="onChangeArea"
        @selected="onSelected"
        type="mobile"
      ></v-distpicker>
    </div>
  </div>
</template>

<script>
import headComponent from '@/components/headComponent' // 头部
import { HPicker } from 'thinkive-hui'
import VDistpicker from 'v-distpicker-q'

export default {
  components: {
    headComponent,
    VDistpicker
  },
  model: {
    event: 'change'
  },
  props: {
    address: {
      type: String,
      default: ''
    },
    province: {
      type: String,
      default: ''
    },
    city: {
      type: String,
      default: ''
    },
    area: {
      type: String,
      default: ''
    },
    detailed: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      p_address: this.address,
      p_province: this.province,
      p_city: this.city,
      p_area: this.area,
      p_detailed: this.detailed,
      distShow: false
    }
  },
  methods: {
    pageBack () {
      this.$emit('change', false)
    },
    // 校验格式
    verification () {
      if (
        $h.isEmptyString(this.p_province) ||
        $h.isEmptyString(this.p_city) ||
        $h.isEmptyString(this.p_area)
      ) {
        _hvueToast({ mes: '请选择省市区' })
        return
      }
      if ($h.isEmptyString(this.p_detailed)) {
        _hvueToast({ mes: '请输入详细地址' })
        return
      }
      if (this.p_detailed.length < 5) {
        _hvueToast({ mes: '详细地址长度需要在5~120个字符，不得包含非法字符' })
        return
      }
      if (this.p_detailed.match(/[^a-zA-Z0-9\u4e00-\u9fa5]/i)) {
        _hvueToast({ mes: '详细地址长度需要在5~120个字符，不得包含非法字符' })
        return
      }
      this.$emit('selCallback', {
        address: this.p_province + this.p_city + this.p_area + this.p_detailed,
        province: this.p_province,
        city: this.p_city,
        area: this.p_area,
        detailed: this.p_detailed
      })
      this.pageBack()
    },
    // 取消选择地区
    countermand: function () {
      this.distShow = false
    },
    onChangeProvince: function (a) {
      this.p_province = a.value
      if (a.value === '海外') {
        this.$nextTick(() => {
          this.p_city = ''
          this.p_area = ''
        })
      }
    },
    onChangeCity: function (a) {
      this.p_city = a.value
    },
    onChangeArea: function (a) {
      this.p_area = a.value
    },
    onSelected: function (a) {
      this.distShow = false
    }
  },
  created () {
    // 如果没有返回省市区，先进行正则匹配
    if (this.province === '' && this.city === '' && this.area === '') {
      let reg = /.+?(省|市|自治区|自治州|县|区)/g
      let specialProvince = ['北京', '天津', '上海', '重庆']
      let addPick = this.address.match(reg)

      if (addPick) {
        if (addPick.length === 2) {
          specialProvince.forEach(item => {
            if (addPick[0].includes(item)) {
              addPick[0] = addPick[0].slice(2)
              addPick.unshift(item)
            }
          })
        }
        if (addPick.length === 3) {
          this.p_province = addPick[0] || ''
          this.p_city = addPick[1] || ''
          this.p_area = addPick[2] || ''
          let lastIndexOf = this.address.lastIndexOf(addPick[2])
          this.p_detailed = this.address.substr(lastIndexOf + addPick[2].length)
          return
        }
        this.p_detailed = this.address
      }
    }
  },
  mounted () {
    window.phoneBackBtnCallBack = this.pageBack
    window.androidAppBack =  this.pageBack
  },
  destroyed () {}
}
</script>

<style scoped>
/*遮罩层*/
.blacks {
  position: fixed;
  width: 100%;
  height: 50%;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.45);
}
/*省市区三级联动*/
.divwrap {
  height: 50%;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 99;
}
.divwrap > .distpicker-address-wrapper {
  position: relative;
  height: 100%;
  color: #bf7b7b;
}
.divwrap >>> .address-header {
  width: 100%;
  position: absolute;
  top: -45px;
  left: 0;
}
.divwrap >>> .address-container {
  height: 100%;
  overflow: scroll;
}
.divwrap >>> .address-container ul {
  height: auto;
  overflow: visible;
}
.divwrap >>> .address-header ul li.active {
  border-bottom: #d01313 solid 3px;
  color: #bf7b7b;
}
.divwrap >>> .address-container ul li.active {
  color: #d01313;
}
</style>

<template>
  <div>
    <div v-show="accountList.length>0" class="cond_box">
      <h5 class="title">您已开通的深A账户</h5>
      <ul class="acct">
        <li v-for="(item,index) in accountList" :key="index">
          <div class="cont">
            <p>
              <span>{{item.stkbdName}} {{ item.stockAccount }}</span>
              <em :class="getAccountStyle(item.holderStatus)">{{item.holderName}}</em>
            </p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import okSrc from "@/assets/images/appro_ok.png";
import errorSrc from "@/assets/images/appro_error.png";

export default {
  props: ["accountList"],
  name: "showAccount",
  data() {
    return {
      icon_src: ""
    };
  },
  methods: {
    doSubmit() {
      // 调用父组件提交事件方法
      this.$parent.nextStep();
    }
  }
};
</script>

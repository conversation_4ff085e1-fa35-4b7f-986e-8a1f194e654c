export default {
  updateCurrentBusiness (state, businessCode) {
    state.currentBusinessCode = businessCode
  },
  // 自动义加载层
  showLoading (state, tips) {
    state.isShowLoading = true
    state.tips = tips
  },
  hideLoading (state) {
    state.isShowLoading = false
  },
  // 更新要跳转的路由对象
  updateToPage (state, to) {
    state.toPage = to
  },
  updateBusinessNextBtnStatus (state, value) {
    state.businessNextBtnShow = !!value
    if (typeof value === 'string') {
      state.nextBtnText = value
    }
  },
  // 更新下一步按钮的文字
  updateNextBtnText (state, text) {
    state.nextBtnText = text
  },
  updateIsWhite (state, value) {
    state.isWhite = value
  },
  updateIsShowHead (state, value) {
    state.isShowHead = value
  },
  // 更新下一步按钮的底色
  updateNextBtnCss (state, value) {
    state.nextBtnCss = value
  },
  updateNextBtnDisabled (state, value) {
    state.nextBtnDisabled = value
  },
  updateAgreementIsFixed (state, value) {
    state.agreementIsFixed = value
  },
  updateAllFundCheckClickFlag (state, value) {
    state.allFundCheckClickFlag = value
  },
  updateSelectedAccountsLength (state, value) {
    state.selectedAccountsLength = value
  },
  updateCachePath (state, value) {
    state.cachePath = value
  },
  updateHandleStatus (state, value) {
    state.handleStatus = value
  },
  updateReturnHomeConfirmFlag (state, value) {
    state.returnHomeConfirm = !!value
    if (!value) {
      state.returnHomeConfirmText = ''
    }
  },
  updateReturnHomeConfirmText (state, value) {
    if (value) {
      state.returnHomeConfirm = true
    } else {
      state.returnHomeConfirm = false
    }
    state.returnHomeConfirmText = value
  },
  updateFlowParamDelay (state, value) {
    state.flowParamDelay = !!value
  },
  updateIsComponentsFlexVertical (state, value) {
    state.isComponentsFlexVertical = !!value
  }
}

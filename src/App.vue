<template>
  <div id="app" class="app_wrap page_box bneedsfocus">
    <section class="main fixed" v-bind:class="{ white_bg: isWhite }">
      <transition :name="`${$store.state.router.direction}`">
        <keep-alive :include="cachePath">
          <router-view v-if="isRouterAlive" />
        </keep-alive>
      </transition>
    </section>
    <loading></loading>
  </div>
</template>

<script>
import Loading from '@/views/common/loading.vue'
import {
  getUrlParam,
  randomNum,
  getDeviceInfoParam,
  getDeviceInfoParamV2
} from '@/common/util'
import { mapGetters } from 'vuex'
import('@/plugins/webview-bridge.js')
export default {
  name: 'App',
  components: { Loading },
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      // 头部的背景色样式，默认为红色，部分特殊页面设置为白色
      // isWhite: this.$store.state.isWhite
      isRouterAlive: true
    }
  },
  computed: {
    ...mapGetters(['cachePath']),
    // cachePath() {
    //   return this.$store.state.cachePath
    // },
    isWhite() {
      return this.$store.state.isWhite
    }
  },
  watch: {
    cachePath(newValue) {}
  },
  mounted() {
    window.$this = this
    $h.clearSession('toPage')
    const urlParams = $h.getUrlParams()
    const businesscode = getUrlParam('businesscode') || ''
    const sys_from = getUrlParam('sys_from')
    const backToApp = getUrlParam('backToApp')
    console.log('businesscode', businesscode)

    let deviceInfoParam = getUrlParam('deviceInfo')
    const ztToken = getUrlParam('ztToken')
    let XCLIP = ''
    let MAC = ''
    if (deviceInfoParam) {
      deviceInfoParam = decodeURIComponent()
      XCLIP = getDeviceInfoParam('LIP', deviceInfoParam)
      MAC = getDeviceInfoParam('MAC', deviceInfoParam)
    }
    $h.setSession('MAC', MAC)
    if (XCLIP) {
      $h.setSession('LIP', XCLIP)
    } else {
      let ip = '127.0.0.' + randomNum(0, 255)
      $h.setSession('LIP', ip)
    }
    if (ztToken) {
      $h.setSession('ztToken', ztToken)
    }
    if (urlParams && businesscode) {
      $h.setSession(
        'toPage',
        JSON.stringify({
          businessCode: businesscode
        }),
        { encrypt: false }
      )
      $h.setSession('fromSiteUrl', document.referrer, { encrypt: false })
    }
    let env = 'ths'
    // if (location.href.indexOf('THSSDK') > -1) {

    this.$nextTick(() => {
      console.log('getDeviceInfoParamV2 start')
      let deviceInfoParam = getUrlParam('deviceInfo')
      let deviceInfo = {}
      if (deviceInfoParam) {
        deviceInfoParam = decodeURIComponent(deviceInfoParam)
        // 将字符串按分号分割成数组
        let keyValuePairs = deviceInfoParam.split(';')
        // 遍历数组，将每个键值对分割并存储到对象中
        keyValuePairs.forEach(pair => {
          let [key, value] = pair.split('=')
          if (key) {
            deviceInfo[key] = value || ''
          }
        })
        $h.setSession('deviceInfo', deviceInfo)
      }
      getDeviceInfoParamV2(5)
      console.log('getDeviceInfoParamV2 end')
    })

    if (sys_from != 'gsfy' && location.href.indexOf('THSSDK') == -1) {
      env = ''
      $hvue.env = env
      return
    }
    let gsfyParam = location.search
    $h.setSession('gsfyParam', gsfyParam)
    $h.setSession('backToApp', backToApp)
    $h.setSession('fromSiteUrl', document.referrer, { encrypt: false })
    $hvue.env = env
  },
  methods: {
    pageBack() {
      this.$store.commit('updateIsWhite', false)
      this.$router.go(-1)
    },
    pageBusinessRecords() {
      this.$store.commit('updateIsWhite', true)
      this.$router.push({ name: 'businessRecords', params: {} })
    },
    // 重新加载当前页面
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function() {
        this.isRouterAlive = true
      })
    }
  }
}
</script>
<style>
/* page transition css  start*/
.app_wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  overflow: hidden;
  height: 100%;
}
.page_header {
  position: relative;
  z-index: 0;
}
.page_main {
  position: absolute;
  left: 0;
  top: 0px;
  right: 0;
  bottom: 0;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  flex-direction: column;
}
.left-enter-active,
.left-leave-active,
.right-enter-active,
.right-leave-active {
  transition: all 0.3s;
}
.left-enter {
  opacity: 0.5;
  transform: translate3d(100%, 0, 0);
}
.left-leave-active {
  opacity: 0.5;
  transform: translate3d(-100%, 0, 0);
}
.right-enter {
  opacity: 0.5;
  transform: translate3d(-100%, 0, 0);
}
.right-leave-active {
  opacity: 0.5;
  transform: translate3d(100%, 0, 0);
}
/* page transition css  end*/

/* thinkive-hui root css */
html {
  font-size: 100px;
  touch-action: none;
}
</style>

import TKFlow from './TKFlow'
import {
  queryUserFlowStatus,
  closeFlow
} from '@/service/comServiceNew'
import ErrorCode from '@/flow/ErrorCodeConstants'

/**
 * 页面流程引擎类
 */
class Flow {
  /**
     * 构造函数
     * options属性描述：
     * @param {*} userId 用户编号
     * @param {*} flowName 流程名称 --后端人员提供对应的流程名称
     * @param {*} flowNameCN 流程中文名称
     * @param {*} hasBreakPoint --流程是否需要支持断点 默认需要 需要:true,不需要：false
     * @param {*} reqParam 请求公共参数
     * @param {*} ErrorCodeConstants  错误码定义对象
     * @param {*} loadData 执行每一步骤的初始化方法的回调方法-做页面数据展示与事件绑定
     * @param {*} finishedFunc 流程执行完的处理事件，根据需要是否处理
     * @param {*} steps 保存全流程信息，用于控制导航条状态
     * @param {*} currentView 当前流程的view，用于展示当前步骤的页面
     * @param {*} currentName 当前流程的名称
     * @param {*} loadBoxEle 加载流程导航条对象元素的ID
     * @param {*} currentStepInfo 当前流程信息
     * @param {*} formStatus 当前用户该业务的表单状态
     * @param {*} isRejectFlow 当前flow对象是否已经执行过一次驳回 若已执行过，则不再驳回。
     * @param {*} serivalId 业务表单流水ID
     * @param {*} interceptorExcute 流程的拦截器事件是否已经执行过，若已经执行过，该流程后面的步骤将不再执行，不然没执行一个步骤都会再执行一次拦截器事件
     */
  constructor (options) {
    this.userId = options.userId // 用户编号
    this.flowName = options.flowName // 当前流程名称
    // this.flowNameCN = options.flowNameCN; // 当前流程中文名称
    // this.fillDataToPage = options.loadData; // 方法
    // this.finishedFunc = options.finishedFunc; // 流程执行完成的回调方法
    // this.reqParam = options.reqParam; // 公共入参
    // this.ErrorCodeConstants = options.errorCodeConstants; // 错误码定义集合
    // this.hasBreakPoint = options.hasBreakPoint; // 是否支持断点
    // this.steps = []; // 保存全流程信息，用于控制导航条状态
    // this.currentView = ""; // 当前页面
    // this.currentName = ""; // 当前流程名称
    // this.loadBoxEle = "#load_stepbox"; // 加载导航条的对象
    this.currentStepInfo = {} // 当前的流程信息
    // this.formStatus = ''; // 当前流程表单状态
    // this.isRejectFlow = true; // 默认流程结束需要驳回，驳回一次后不再驳回
    this.serivalId = '' // 业务表单流水ID
    this.interceptorExcute = false // 标记拦截器事件是否已经执行过
    this.publicParam = options.publicParam // 公共入参

    // 新增
    this.vueComponentObj = options._this // 当前页面vue 对象 必传
    this.historyStepInfo = [] // 只记录有页面的步骤。用于页面返回时，以确定展示哪个页面
    this.stepsInfo = [] // 流程全部的信息
  }

  /**
     * 流程开始
     */
  startFlow (initParams) {
    // 5、查询流程全部步骤信息
    this.stepsInfo = this.queryFlowInfo()
    /**
         * 重置密码业务不查询在途业务
         */
    if (this.flowName === 'czmm') {
      // 创建新的流程ID
      this.flowId = this.createFlowId()
      // 加载流程
      this.loadFlow(initParams)
      return
    }
    // 查询用户表单状态
    queryUserFlowStatus({
      userId: this.userId,
      businessCode: this.flowName
    }, {
      isLastReq: false
    }).then(
      res => {
        if (res.error_no === '0') {
          let result = res.results[0]
          this.serivalId = result.serivalId
          $h.setSession('serivalId', this.serivalId) // 保存流水号

          // 断点提示需要根据业务配置是否有断点来做，现在暂时没有这个属性，统一都有断点提示
          let groupCode = window.$this.$root.$route.query.groupCode
          if (this.flowName !== 'xgmm' && this.flowName !== 'cgyhbg' && groupCode !== 'authority') {
            if (res.results[0].flowId && result.formStatus === '0') {
              _hvueConfirm({
                mes: '检测您有未完成操作，是否继续上次操作？',
                opts: [{
                  txt: '取消',
                  color: false,
                  callback: () => {
                    closeFlow({
                      userId: this.userId,
                      businessCode: this.flowName,
                      serivalId: this.serivalId
                    }, {}).then(
                      res => {
                        if (res.error_no === '0') {
                          // 重新加载流程
                          this.startFlow()
                        } else {
                          _hvueAlert({
                            title: '提示',
                            mes: res.error_info,
                            callback: () => {
                              // 确定之后的回调
                              let sectionDom = document.querySelectorAll('section.main')
                              let viewDom = sectionDom[0].getElementsByTagName('div')
                              if (viewDom.length == 1) {
                                this.vueComponentObj.$router.push({
                                  name: 'index'
                                })
                              }
                            }
                          })
                        }
                      }
                    )
                  }
                }, {
                  txt: '确定',
                  color: true,
                  callback: () => {
                    this.flowId = res.results[0].flowId
                    // 加载流程
                    this.loadFlow(initParams)
                  }
                }]
              })
            } else {
              this.flowId = res.results[0].flowId ? res.results[0].flowId : this.createFlowId()
              // 加载流程
              this.loadFlow(initParams)
            }
          } else {
            this.flowId = this.createFlowId()
            // 加载流程
            this.loadFlow(initParams)
          }
        } else {
          _hvueAlert({
            title: '提示',
            mes: res.error_info,
            callback: () => {
              // 确定之后的回调
              let sectionDom = document.querySelectorAll('section.main')
              let viewDom = sectionDom[0].getElementsByTagName('div')
              if (viewDom.length == 1) {
                this.vueComponentObj.$router.push({
                  name: 'index'
                })
              }
            }
          })
        }
      }
    )
  }

  /**
     * 创建流程ID
     */
  createFlowId () {
    return TKFlow.createFlowId(this.userId + Math.random(), this.flowName)
  }

  /**
     * 加载流程
     */
  loadFlow (initParams) {
    // 加载流程，获取当前的步骤信息
    let loadFlowResult = TKFlow.loadFlow(this.flowId, this.flowName, {
      isLastReq: false
    })
    let _this = this
    loadFlowResult.then(data => {
      if (data.error_no === '0') {
        let result = data.results ? data.results.length > 0 ? data.results[0] : {} : {}
        // 处理当前步骤
        _this.currentStepInfo = result
        _this.excuteNextStep(initParams)
      } else {
        _hvueAlert({
          title: '提示',
          mes: data.error_info,
          callback: () => {
            // 确定之后的回调
            let sectionDom = document.querySelectorAll('section.main')
            let viewDom = sectionDom[0].getElementsByTagName('div')
            if (viewDom.length == 1) {
              this.vueComponentObj.$router.push({
                name: 'index'
              })
            }
          }
        })
      }
    })
  }

  /**
     * 执行流程的拦截器事件
     **/
  interceptorEvent (params) {
    // 与公共请求参数合并
    params = params || {}
    let reqParam = Object.assign(params, this.publicParam)
    TKFlow.executeFlowInterceptor(this.flowId, this.flowName, reqParam, {
      isLastReq: false
    }).then(
      res => {
        if (res.error_no === '0') {
          this.interceptorExcute = true // 标记拦截器事件已经执行过,后面的步骤不再执行
          // 执行步骤初始化事件
          this.initStep(params)
          let stepInfo = this.currentStepInfo
          let isAutoSubmit = stepInfo.flow_current_step_auto_submit // 是否自动提交
          let isFineshed = stepInfo.flow_finish // 流程是否已经结束
          let isRejectFlow = stepInfo.flow_reject // 流程是否被驳回
          if (isAutoSubmit === '1' && (isFineshed !== '1' || isRejectFlow === '1')) {
            // 自动提交的 -- 直接提交事件
            this.submitStep(params)
          }
        } else {
          // 此处匹配错误码，根据错误码做相应的处理
          // _hvueAlert({ title: "提示", mes: res.error_info })
          _dealErrorCodeEvent.apply(this, [res])
        }
      }
    )
  }

  /**
     * 执行当前步骤的初始化方法 执行pageFlow的init方法
     * @param {*} params
     */
  initStep (params) {
    let stepInfo = this.currentStepInfo
    let currentView = stepInfo.flow_current_step_view // 当前需要展示的组件
    let currentStepName = stepInfo.flow_current_step_name // 当前步骤名称
    let isShow = stepInfo.flow_current_step_show // 当前步骤是否显示

    this.currentView = currentView
    this.currentName = currentStepName
    if (isShow === '1') {
      // 展示对应的组件
      for (let viewsKey in this.vueComponentObj.views) {
        viewsKey != currentView ? this.vueComponentObj.views[viewsKey] = false : null
      }
      this.vueComponentObj.title = stepInfo.description
      this.vueComponentObj.views[currentView] = true
      // historyStepInfo里面判断有的时候则不插入数据
      if (this.historyStepInfo.indexOf(currentStepName + '-' + currentView) == -1) {
        this.historyStepInfo.push(currentStepName + '-' + currentView) // currentStepName为当前页面步骤的名称。 currentView为当前页面
      }
    }

    // 与公共请求参数合并
    params = params || {}
    let reqParam = Object.assign(params, this.publicParam)
    // let reqParam = Object.assign(this.publicParam, params)
    reqParam.serivalId = this.serivalId
    $h.setSession('serivalId', this.serivalId) // 保存流水号
    TKFlow.initFlowStepParameters(this.flowId, this.flowName, reqParam, {
      isLastReq: true
    }).then(
      data => {
        if (data.error_no === '0') {
          // 初始化展示当前view的数据 -- 调用当前页面method里面的loadPageData方法，以加载数据
          this.vueComponentObj.loadPageData(data)
        } else {
          _hvueAlert({
            title: '提示',
            mes: data.error_info,
            callback: () => {
              // 确定之后的回调
              let sectionDom = document.querySelectorAll('section.main')
              let viewDom = sectionDom[0].getElementsByTagName('div')
              if (viewDom.length == 1) {
                this.vueComponentObj.$router.push({
                  name: 'index'
                })
              }
            }
          })
        }
      }
    )
  }

  /**
     * 提交当前步骤--执行pageFLow的submit方法
     * @param {*} params // 请求参数
     * @param {*} formData 表单数据
     * @param {*} ctrlParam 控制参数
     */
  submitStep (params, formData, ctrlParam) {
    // 与公共请求参数合并
    let reqParam = Object.assign({}, params, this.publicParam)
    // 表单数据
    if (formData) {
      reqParam.formData = JSON.stringify([formData])
    } else {
      reqParam.formData = JSON.stringify([{}])
    }
    // reqParam.atomCode = reqParam.atomCode ? reqParam.atomCode : this.currentStepInfo.flow_current_step_view
    reqParam.serivalId = this.serivalId
    return new Promise((resolve, reject) => {
      TKFlow.executeFlow(this.flowId, this.flowName, reqParam, {
        isLastReq: false
      }).then(data => {
        if (data.error_no === '0') {
          let dsNameArray = data.dsName
          let stepInfo = null // 下一步流程的信息
          // 取出当前的流程信息
          if (dsNameArray.indexOf('DataSet') > -1) {
            stepInfo = data['DataSet'][0]
          } else {
            stepInfo = data.results[0]
          }
          // 如果需要对提交结果做处理，则返回提交结果
          if (ctrlParam && ctrlParam.handleResult) {
            let submitResult = {}
            for (let index = 0; index < dsNameArray.length; index++) {
              let resultName = dsNameArray[index] // 结果集名称
              submitResult[resultName] = data[resultName] // 放入对象中
            }
            this.currentStepInfo = stepInfo
            resolve(submitResult) // 返回提交结果，备注：Promise对象then方法链式调用,并且在回调中调用excuteNextStep
          } else {
            // 处理下一步
            this.currentStepInfo = stepInfo

            let isFineshed = stepInfo.flow_finish // 流程是否已经结束
            let isRejectFlow = stepInfo.flow_reject // 流程是否被驳回
            // 除了正常流程中最后一步，都调用初始化
            if (!(isFineshed == '1' && isRejectFlow != '1')) {
              this.excuteNextStep(reqParam)
            } else {
              _hvueLoading.closeLoading()
            }
            resolve()
          }
        } else {
          _hvueAlert({
            title: '提示',
            mes: data.error_info,
            callback: () => {
              // 确定之后的回调
              let sectionDom = document.querySelectorAll('section.main')
              let viewDom = sectionDom[0].getElementsByTagName('div')
              if (viewDom.length == 1) {
                this.vueComponentObj.$router.push({
                  name: 'index'
                })
              }
            }
          })
          reject('error')
        }
      })
    })
  }

  /**
     * 根据当前的步骤信息做相应的处理
     * @param {流程步骤信息} stepInfo
     */
  excuteNextStep (initParams) {
    initParams = initParams || {}
    let stepInfo = this.currentStepInfo
    let currentView = stepInfo.flow_current_step_view // 当前需要展示的组件
    let isAutoSubmit = stepInfo.flow_current_step_auto_submit // 是否自动提交
    let currentStepName = stepInfo.flow_current_step_name // 当前步骤名称
    let isFineshed = stepInfo.flow_finish // 流程是否已经结束
    let isRejectFlow = stepInfo.flow_reject // 流程是否被驳回

    this.currentView = currentView
    this.currentName = currentStepName

    // 判断是否已经结束
    // if (isFineshed === '1') {
    //   // 已经执行完成，不再执行。
    //   return new Promise((resolve, reject) => {
    //     resolve({
    //       error_no: '0',
    //       error_info: ''
    //     })
    //   })
    // }

    // 未执行拦截器事件 并且 流程未结束时，先执行拦截器事件
    if (!this.interceptorExcute && isFineshed !== '1') {
      // 执行拦截器事件
      this.interceptorEvent(initParams)
    } else {
      //   // 执行步骤初始化事件
      this.initStep(initParams)
      if (isAutoSubmit === '1' && (isFineshed !== '1' || isRejectFlow === '1')) {
        // 自动提交的情况下、流程未结束或已驳回时 -- 直接提交事件
        this.submitStep(initParams)
      }
    }
  }

  /**
     * 回退当前流程到指定步骤
     * @param stepName 回退到的步骤名称
     * @return 返回Promise对象
     */
  rollbackFlow (stepName) {
    let stepInfo = this.currentStepInfo
    let isRejectFlow = stepInfo.flow_reject
    if (isRejectFlow === '1') {
      // 是资料驳回后重走流程中点击返回时，直接路由返回首页
      this.vueComponentObj.$router.push({
        name: 'index'
      })
      return new Promise((resolve, reject) => {
        resolve({
          error_no: '0',
          error_info: ''
        })
      })
    }
    if (this.historyStepInfo.length === 1) {
      var firstStepIndex = _findElem(this.stepsInfo, 'show', '1')
      // 是第一步时，关闭流程，结束原流水（重置密码业务除外）
      if (stepInfo.flow_current_step_name == this.stepsInfo[firstStepIndex].step_name && stepInfo.flow_name !== 'czmm') {
        closeFlow({
          userId: this.userId,
          businessCode: this.flowName,
          serivalId: this.serivalId
        }).then(res => {
          if (res.error_no === '0') {
            this.vueComponentObj.$router.push({
              name: 'index'
            })
          } else {
            _hvueAlert({
              title: '提示',
              mes: res.error_info,
              callback: () => {
                this.vueComponentObj.$router.push({
                  name: 'index'
                })
              }
            })
          }
        })
      } else {
        this.vueComponentObj.$router.push({
          name: 'index'
        })
      }
      return new Promise((resolve, reject) => {
        resolve({
          error_no: '0',
          error_info: ''
        })
      })
    }
    if (stepName == undefined) {
      let preStepInfo = this.historyStepInfo[this.historyStepInfo.length - 2].split('-')
      let currentView = preStepInfo[1]
      // 展示对应的组件
      for (let viewsKey in this.vueComponentObj.views) {
        viewsKey != currentView ? this.vueComponentObj.views[viewsKey] = false : null
      }
      this.vueComponentObj.views[preStepInfo[1]] = true
      // return TKFlow.rollbackFlow(this.flowId, this.flowName, preStepInfo[0])
      stepName = preStepInfo[0]
    }

    return new Promise((resolve, reject) => {
      TKFlow.rollbackFlow(this.flowId, this.flowName, stepName).then(data => {
        if (data.error_no === '0') {
          this.loadFlow()
          // 处理完成后，删掉最后的一个步骤
          this.historyStepInfo.pop()
        } else {
          _hvueToast({
            title: '错误提示',
            mes: `回退流程失败：${data.error_info}`
          })
        }
      })
    })
  }

  /**
     * 驳回流程到指定步骤
     * @param stepNames 驳回到的步骤  数组
     * @return 返回Promise对象
     */
  rejectFlow (stepNames) {
    return new Promise((resolve, reject) => {
      TKFlow.rejectFlow(this.flowId, this.flowName, stepNames).then(data => {
        if (data.error_no === '0') {
          // 隐藏组件
          for (let viewsKey in this.vueComponentObj.views) {
            this.vueComponentObj.views[viewsKey] = false
          }
          this.loadFlow()
          resolve({
            error_no: '0',
            error_info: ''
          })
        } else {
          _hvueAlert({
            title: '错误提示',
            mes: `驳回流程失败：${data.error_info}`,
            callback: () => {
              // 确定之后的回调
              let sectionDom = document.querySelectorAll('section.main')
              let viewDom = sectionDom[0].getElementsByTagName('div')
              if (viewDom.length == 1) {
                this.vueComponentObj.$router.push({
                  name: 'index'
                })
              }
            }
          })
        }
      })
    })
  }

  /**
     * 业务流程信息查询
     * @return 返回
     */
  queryFlowInfo () {
    return new Promise(resolve => {
      TKFlow.queryFlowInfo(this.flowName, {
        isLastReq: false
      }).then(res => {
        let result = res.DataSet ? res.DataSet[0] : res.results[0]
        this.stepsInfo = JSON.parse(result.flow_steps)
        resolve(this.stepsInfo)
      })
    })
  }
}
/**
 * 处理错误码信息
 * 只供内部调用使用
 * 私有方法-定义规则：在方法前面加下划线
 */
function _dealErrorCodeEvent (data) {
  let errorNo = data.error_no
  let matchFlag = false
  for (let index = 0; index < ErrorCode.length; index++) {
    const element = ErrorCode[index]
    if (errorNo === element.code) {
      matchFlag = true // 标记匹配到错误码
      // 跳转到公共的错误信息页面
      element.title = this.vueComponentObj.$route.meta.title
      this.vueComponentObj.$router.push({
        name: 'error',
        params: element
      })
    }
  }
  // 未匹配到错误码文件
  if (!matchFlag) {
    // 提示错误信息
    // _hvueAlert({
    //   title: '错误提示',
    //   mes: data.error_info
    // })
    this.vueComponentObj.$router.push({
      name: 'error',
      params: {
        title: '错误提示',
        message: data.error_info

      }
    })
  }
}

/**
 * 判断数组对象中是否有某个值
 * @param {数组} arrayToSearch
 * @param {key} attr
 * @param {val} val
 */
function _findElem (arrayToSearch, attr, val) {
  // debugger
  for (var i = 0; i < arrayToSearch.length; i++) {
    if (arrayToSearch[i][attr] == val) {
      return i // 返回当前索引值
    }
  }
  return -1
};

export default Flow

/**
 *
 * <AUTHOR>
 * 页面流组件
 * 公用方法:
 *        init: 页面组件初始化数据的方法
 *        next: 页面组件提交事件的方法
 *        back: 页面组件返回事件的方法
 * 业务组件使用方法:
 *   1、创建业务流组件：let flow = new Flow(options);
 *      options参数说明
 *            必传参数：userId 用户ID
 *                     flowName 当前业务流程名称，页面流pageFlow配置人员提供
 *                     _this :当前业务页面vue实例对象
 *  2、初始化流程：flow.init(params)
 *     params参数说明：初始化数据加载需要的入参
 *  3、跳转下一步：flow.next(params)
 *     params参数说明：
 *           格式如下：
 *            {
 *                param1:"",
 *                param2:"",
 *                formData : {} // formData为表单数据，需要提交表单数据时，必传此参数
 *            }
 */
import TKFlow from './TKFlowNew'
import ErrorCode from '@/flow/ErrorCodeConstants'
/**
 * 页面流程引擎对象类
 * options:
 *  userId : 用户编码
 *  flowName : 流程名称
 */
class Flow {
  /**
     *
     * @param {*} options
     */
  constructor(options) {
    /** *********业务组件需要传入的参数*****start******* */
    this.userId = options.userId // 用户编号
    this.clientId = options.clientId // 用户编号
    this.flowName = options.flowName // 当前流程名称
    this.vueComponentObj = options._this
    /** *********业务组件需要传入的参数******end****** */

    /** ********流程需要初始化的数据，不需要业务组件传入**********/
    this.currentStepInfo = {} // 当前的流程信息
    this.serivalId = options.serivalId // 流水ID
    this.formStatus = options.formStatus // 表单状态
    this.stepsInfo = [] // 流程全部的信息
    // 流程记录对象->用于页面返回时，以确定展示哪个页面
    this.historyStepInfo = []

    // 创建流程ID
    this.flowId = options.flowId ? options.flowId : this.createFlowId()
  }
  /** ************************************flow对象公有方法******start**********************************************/
  /**
     * 流程初始化方法
     * 功能：获取到当前流程步骤信息，展示对应步骤的组件。
     * return Promise 对象
     * return {data}:
     *    componentName : 组件名称（根据结果集名称加载对应的组件）
     *    componentResults : 初始化加载该组件需要的数据集合 数组类型
     *
     **/
  /**
     * 加载流程
     */
  loadFlow(params) {
    // 加载流程，获取当前的步骤信息
    return new Promise((resolve, reject) => {
      TKFlow.loadFlow(this.flowId, this.flowName, {
        isLastReq: false
      }).then(data => {
        if (data.error_no === '0') {
          let result = data.DataSet ? (data.DataSet.length > 0 ? data.DataSet[0] : {}) : (data.results[0] || {})
          // 保存当前的流程步骤
          this.currentStepInfo = result
          resolve()
        } else {
          _hvueAlert({
            title: '提示',
            mes: data.error_info
          })
          reject(data.error_info)
        }
      })
    })
  }
  /**
     * 执行流程的拦截器事件
     **/
  interceptorEvent(isAutoSubmit) {
    return new Promise((resolve, reject) => {
      TKFlow.executeFlowInterceptor(this.flowId, this.flowName, {
        userId: this.userId,
        clientId: this.clientId
      }, {
        isLastReq: false
      }).then(
        res => {
          if (res.error_no === '0') {
            // 继续执行
            resolve(res)
          } else {
            // 此处匹配错误码，根据错误码做相应的处理
            // _hvueAlert({ title: "提示", mes: res.error_info })
            _dealErrorCodeEvent.apply(this, [res, resolve, reject])
          }
        }
      )
    })
  }

  init(params) {
    return new Promise((resolve, reject) => {
      TKFlow.initFlowStepParameters(this.flowId, this.flowName, params, {
        isLastReq: true
      }).then(
        data => {
          if (data.error_no === '0') {
            // 封装初始化返回数据结果集
            var componentResults = []
            let dsName = []
            if (data.resultKeys) {
              // 有固定顺序的结果集
              for (let index = 0; index < data.resultKeys.length; index++) {
                const element = data[data.resultKeys[index].resultKey]
                componentResults.push(element)
                dsName.push(data.resultKeys[index].resultKey)
              }
            } else {
              for (let index = 0; index < data.dsName.length; index++) {
                const element = data[data.dsName[index]]
                componentResults.push(element)
              }
              dsName = data.dsName
            }
            var returnResult = {
              componentName: dsName, // 组件名称根据初始化步骤返回的结果集确定
              componentResults: componentResults,
              // 用于特殊场景进行额外判断
              data
            }
            this.historyStepInfo.push(this.currentStepInfo.flow_current_step_name) // 将当前步骤放在历史步骤信息中。
            resolve(returnResult)
          } else {
            // _hvueAlert({
            //   title: '提示',
            //   mes: data.error_info
            // })
            reject(data.error_info)
          }
        }
      )
    })
  }

  /**
     * 执行流程的提交事件。
     * 根据当前的步骤信息做相应的处理
     * @param {流程步骤信息} params 业务请求参数
     * @return Promise对象 提交上一步，并返回下一步的信息
     */
  next(params, options) {
    options = options || {
      isLastReq: false
    }
    return new Promise((resolve, reject) => {
      TKFlow.executeFlow(this.flowId, this.flowName, params, options).then(data => {
        if (data.error_no === '0') {
          let dsNameArray = data.dsName
          // 判断是否返回了表单结果，是的话将接口返回的serivalId和flowId同步到前端参数中
          if (dsNameArray.indexOf('formData') > -1) {
            let formData = data['formData'][0]
            this.serivalId = formData.serivalId
            this.flowId = formData.flowId
          }
          let stepInfo = null // 下一步流程的信息
          // 取出当前的流程信息
          if (dsNameArray.indexOf('DataSet') > -1) {
            stepInfo = data['DataSet'][0]
            // 如果需要对提交结果做处理，则返回提交结果
            let submitResult = {}
            for (let index = 0; index < dsNameArray.length; index++) {
              let resultName = dsNameArray[index] // 结果集名称
              submitResult[resultName] = data[resultName] // 放入对象中
            }
            this.currentStepInfo = stepInfo
            resolve(data) // 返回提交结果，备注：Promise对象then方法链式调用
          } else {
            stepInfo = data.DataSet[0]
          }
          // 处理下一步
          this.currentStepInfo = stepInfo

          let isFineshed = stepInfo.flow_finish // 流程是否已经结束
          let isRejectFlow = stepInfo.flow_reject // 流程是否被驳回
          if (isFineshed === '1' && isRejectFlow !== '1') {
            _hvueLoading.closeLoading()
          }
          resolve(data)
        } else {
          reject(data)
        }
      }).catch(error => {
        reject(error)
      }).finally((error) => {
        reject(error)
      })
    })
  }

  /**
     * 回退当前流程到指定步骤
     * @param stepName 回退到的步骤名称
     * @return 返回Promise对象
     */
  back(stepName) {
    return TKFlow.rollbackFlow(this.flowId, this.flowName, stepName)
  }

  /** ************************************flow对象公有方法******end**********************************************/

  /** ************************************flow对象私有方法*****************start************************************** */

  /**
     * 创建流程ID
     */
  createFlowId() {
    return TKFlow.createFlowId(this.userId + Math.random(), this.flowName)
  }

  /**
     * 驳回流程到指定步骤
     * @param stepNames 驳回到的步骤  数组
     * @return 返回Promise对象
     */
  rejectFlow(stepNames) {
    // return TKFlow.rejectFlow(this.flowId, this.flowName, stepNames)
    return new Promise((resolve, reject) => {
      TKFlow.rejectFlow(this.flowId, this.flowName, stepNames).then(data => {
        if (data.error_no === '0') {
          // 隐藏组件
          // for (let viewsKey in this.vueComponentObj.views) {
          //     this.vueComponentObj.views[viewsKey] = false
          // }
          // this.loadFlow()
          resolve({
            error_no: '0',
            error_info: ''
          })
        } else {
          _hvueAlert({
            title: '错误提示',
            mes: `驳回流程失败：${data.error_info}`,
            callback: () => {
              // 确定之后的回调
              let sectionDom = document.querySelectorAll('section.main')
              let viewDom = sectionDom[0].getElementsByTagName('div')
              if (viewDom.length === 1) {
                this.vueComponentObj.$router.push({
                  name: 'index'
                })
              }
            }
          })
        }
      })
    })
  }
  /**
     * 业务流程信息查询
     * @return 返回
     */
  queryFlowInfo() {
    return new Promise(resolve => {
      TKFlow.queryFlowInfo(this.flowName, {
        isLastReq: false
      }).then(res => {
        let results = res.DataSet || res.results
        this.stepsInfo = JSON.parse(results[0].flow_steps)
        resolve(this.stepsInfo)
      })
    })
  }
}
/**
 * 处理错误码信息
 * 只供内部调用使用
 * 私有方法-定义规则：在方法前面加下划线
 */
function _dealErrorCodeEvent(data, resolve, reject) {
  let errorNo = data.error_no
  let matchFlag = false
  for (let index = 0; index < ErrorCode.length; index++) {
    const element = ErrorCode[index]
    if (errorNo === element.code) {
      matchFlag = true // 标记匹配到错误码
      if (element.type === 'errorPage') {
        // 跳转到公共的错误信息页面
        element.title = this.vueComponentObj.$route.meta.title
        this.vueComponentObj.$router.push({
          name: 'error',
          params: element
        })
        return
      }
      if (element.type === 'confirmPopup') {
        _hvueConfirm({
          mes: data.error_info,
          opts: [{
            txt: '取消',
            color: false,
            callback: () => {
              resolve(data)
            }
          }, {
            txt: '确定',
            color: true,
            callback: () => {
              reject(data)
            }
          }]
        })
      }
    }
  }
  // 未匹配到错误码文件
  if (!matchFlag) {
    // 提示错误信息
    _hvueAlert({
      title: '错误提示',
      mes: data.error_info,
      callback: () => {
        this.vueComponentObj.$router.go(-1)
      }
    })
  }
}

export default Flow

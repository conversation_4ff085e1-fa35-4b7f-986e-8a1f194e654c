'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.
var pkg = require('../package.json')
const path = require('path')

//打包环境类型  dev:开发环境 test:测试环境 uat:准生产环境 pdt:生产环境
// var packageType = JSON.parse(process.env.npm_config_argv).cooked[1];
var packageType = process.env.npm_lifecycle_event
console.log(`环境变量: ${packageType}`)

//使用build指令打出开发环境包
if (packageType === 'build') {
  packageType = 'pdt'
}

//打包应用名
var pkgName = pkg.name

//各服务器环境配置
var serverUrls = {
  'dev': {
    'YGT_NEW_SERVER': '/api', // 测试接入
    'YGT_NEW_SERVER_SERVLET_API': '/servletApi' // 测试接入
  },
  'test': {
    'YGT_NEW_SERVER': '/ismp/servlet/json', // 测试接入
    'YGT_NEW_SERVER_SERVLET_API': '/ismp/servlet' // 测试接入
  },
  'uat': {
    'YGT_NEW_SERVER': '/ismp/servlet/json'
  },
  'pdt': {
    'YGT_NEW_SERVER': '/ismp/servlet/json',
    'YGT_NEW_SERVER_SERVLET_API': '/ismp/servlet' // 测试接入
  }
}

module.exports = {
  serverUrls: serverUrls,
  moduleName: pkgName,
  dev: {

    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: { //跨域代理
      '/api': {
        target: 'https://zhshtest.showcai.com.cn:41081/ismp/servlet/json',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      },
      '/servletApi': {
        target: 'https://zhshtest.showcai.com.cn:41081/ismp/servlet',
        // target: 'https://nkh-test.showcai.com.cn:41081/ismp/servlet/json',
        changeOrigin: true,
        pathRewrite: {
          '^/servletApi': ''
        }
      }
    },

    // Various Dev Server settings localhost
    host: '127.0.0.1', // can be overwritten by process.env.HOST
    port: 8090, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    //开发服务器地址
    serverUrl: JSON.stringify(serverUrls[packageType] || ''),
    webSocketBashPath: JSON.stringify('./static/websocket/'),

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: false,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist_' + packageType + '/' + pkgName + '/views/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist_' + packageType + '/' + pkgName + '/views'),
    assetsSubDirectory: 'static',
    assetsPublicPath: '/' + pkgName + '/views/',

    //生产服务器地址
    serverUrl: JSON.stringify(serverUrls[packageType]),
    webSocketBashPath: JSON.stringify('./static/websocket/'),

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: 'eval-source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: true,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
}

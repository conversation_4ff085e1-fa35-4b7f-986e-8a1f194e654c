const config = {
  // 请求签名所需参数对象
  ssoSign: {
    merchant_id: 'financeweb', // 商户id
    // 后端给的加签key再用signEncrypt方法加密后的key
    sign_key: 'OdDd+M4OHraSurI5/yRW3i1DohAZihnuvYOlohQ3r1DjyFimJqJy1w==',
    corp_id: '101000000001', // 统一账户公司编号
    app_id: '102000000001', // 统一账户应用编号
    encrypt_mode: '', // 请求参数加密模式: aes, des, base64. 默认base64.
    encrypt_key: '',
    sign_version: '1.0' // 加签名方法的版本，可与后端确定版本号。version: '1.0' or '2.0'
  },
  ssoSignNew: {
    sign_key: 'm53TzVkpa0OB4+zAawoUDxf+gYmPfy77/5HYbJ6oKHEKcZ264wPVjw==',
    company_id: 'THINKIVE', // 公司id
    system_id: 'TAMP', // 系统id
    encrypt_mode: 'none', // 请求参数加密模式: aes, des, base64, none. 默认base64
    encrypt_key: '',
    system_version: '1.0.2', // 系统版本, 这个值固定 '1.0.2'
    sign_version: '2.0' // 加签名方法的版本，可与后端确定版本号。version: '1.0' or '2.0'
  },
  // session数据保存到app内存中, true/false, 默认false
  sessionSaveToApp: false,
  // 开启session数据保存到app内存中选项后，需要配置此项来过滤存储的key, 
  // 配置在数组中key才会存在app内存中
  sessionKeyToApp: [],

  sm4Key: 'f0f91d79220c5ec9f3c013a5b4499cb2', // 加密秘钥

  useTYSP: '1', // 是否使用统一视频，1-是 0-否
  videoType: '0', // 视频类型 0-tchat 1-anychat

  //测试视频服务配置
  serviceType: '1', // 运营商类型
  // serviceTypeUrl: 'fxc2.ydsc.com.cn:9071', // 视频服务器url

  imgUrl: '',
  dwWtUrl:'https://zttest.showcai.com.cn:41081/',//测试环境
  wt2IndexUrl: 'https://app2.ydsc.com.cn:9012/m/ygt/index.html#!/account/addIndex.html', // 生产环境网厅2.0首页地址

  // riskExclusiveSelection: [ // 风险测评多选题独一选项配置
  //     { questionIndex: 3, selectionIndex: 1 },
  //     { questionIndex: 5, selectionIndex: 4 }
  // ]

  // noAgeCheckBusiness: [ // 无需年龄检测的业务
  //   'convertBond', 'cancelAccount', 'cybkt', 'mmjs', 'zhzh', 'zgdhsz', 'xgmm', 'fxcp',
  //   'dzqmyds', 'dzdcx', 'qxlbcx', 'xycx', 'wdzh'
  // ],

  noLoginBusiness: ['czmm', 'zhzh', 'xmzhjh'], // 无需登录就能办理的业务

  loginHideBusiness: ['zhzh'], // 登录状态下隐藏的业务

  videoTime: '9:00 ~ 16:00'
}

window.$hvue = {
  config
}
export default config
